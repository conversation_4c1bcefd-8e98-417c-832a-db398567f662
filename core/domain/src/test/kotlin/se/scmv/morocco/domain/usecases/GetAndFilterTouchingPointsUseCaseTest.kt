package se.scmv.morocco.domain.usecases

import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import junit.framework.TestCase.assertEquals
import junit.framework.TestCase.assertTrue
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import se.scmv.morocco.domain.models.AdDetails
import se.scmv.morocco.domain.models.AdTouchingPoint
import se.scmv.morocco.domain.models.CampaignData
import se.scmv.morocco.domain.models.LabelValue
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.NetworkErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.Targeting
import se.scmv.morocco.domain.repositories.AdViewRepository

@RunWith(JUnit4::class)
class GetAndFilterTouchingPointsUseCaseTest {

    @MockK
    lateinit var adViewRepository: AdViewRepository

    @InjectMockKs
    lateinit var useCase: GetAndFilterTouchingPointsUseCase

    private val touchingPoint = AdTouchingPoint(
        id = "fakeId",
        campaignData = CampaignData(
            shareOfVoice = listOf(),
            title = "suscipit",
            description = "veritus",
            clientLogo = "verterem",
            redirectLink = "tamquam"
        ),
        platforms = listOf("android", "desktop", "ios"),
        targeting = Targeting(
            categories = listOf(
                LabelValue("1", "label1"),
                LabelValue("2", "label2"),
                LabelValue("3", "label3"),
            ),
            cities = listOf(
                LabelValue("1", "label1"),
                LabelValue("2", "label2"),
                LabelValue("3", "label3"),
            )
        )
    )
    private val adCategory = AdDetails.Details.Category(
        id = "1",
        name = "Kathryn Gallegos",
        trackingValue = "Nola Nelson",
        parent = null
    )
    private val adCity = AdDetails.Details.City(
        id = "1",
        name = "some name",
        trackingValue = "some name"
    )

    @Before
    fun setup() {
        MockKAnnotations.init(this, relaxUnitFun = true)
    }

    @Test
    fun `invoke, empty list`() = runTest {
        // GIVEN
        coEvery { adViewRepository.getAdTouchingPoint() } returns Resource.Success(emptyList())

        // WHEN
        val result = useCase(adCategory, adCity)

        // THEN
        assertTrue(result.isEmpty())
    }

    @Test
    fun `invoke, error`() = runTest {
        // GIVEN
        val error = NetworkAndBackendErrors.Network(NetworkErrors.NO_INTERNET)
        coEvery {
            adViewRepository.getAdTouchingPoint()
        } returns Resource.Failure(error)

        val useCase = GetAndFilterTouchingPointsUseCase(adViewRepository)

        // WHEN
        val result = useCase(adCategory, adCity)

        // THEN
        assertTrue(result.isEmpty())
        coVerify(exactly = 1) { adViewRepository.getAdTouchingPoint() }
    }


    @Test
    fun `invoke, nominal case`() = runTest {
        // GIVEN
        coEvery { adViewRepository.getAdTouchingPoint() } returns Resource.Success(
            listOf(
                touchingPoint.copy(
                    campaignData = touchingPoint.campaignData.copy(
                        shareOfVoice = listOf("100%")
                    )
                ),
                touchingPoint.copy(id = "fakeId2"),
                touchingPoint.copy(id = "fakeId3")
            )
        )
        val requiredTp = touchingPoint.copy(
            campaignData = touchingPoint.campaignData.copy(shareOfVoice = listOf("100%"))
        )
        val optionalTps = listOf(
            touchingPoint.copy(id = "fakeId2"),
            touchingPoint.copy(id = "fakeId3")
        )

        // WHEN
        val result = useCase(adCategory, adCity)

        // THEN
        assertTrue(result.contains(requiredTp))
        assertTrue(result.count { optionalTps.contains(it) } == 1)
        coVerify(exactly = 1) { adViewRepository.getAdTouchingPoint() }
    }

    @Test
    fun `invoke, no full share of voice`() = runTest {
        // GIVEN
        val tps = listOf(
            touchingPoint.copy(id = "fakeId1"),
            touchingPoint.copy(id = "fakeId2"),
            touchingPoint.copy(id = "fakeId3")
        )
        coEvery { adViewRepository.getAdTouchingPoint() } returns Resource.Success(tps)

        // WHEN
        val result = useCase(adCategory, adCity)

        // THEN
        assertTrue(result.count { tps.contains(it) } == 2)
        coVerify(exactly = 1) { adViewRepository.getAdTouchingPoint() }
    }

    @Test
    fun `invoke, full share of voice but not targeting ad category`() = runTest {
        // GIVEN
        coEvery { adViewRepository.getAdTouchingPoint() } returns Resource.Success(
            listOf(
                touchingPoint.copy(
                    campaignData = touchingPoint.campaignData.copy(
                        shareOfVoice = listOf("100%")
                    )
                ),
                touchingPoint.copy(id = "fakeId2"),
                touchingPoint.copy(id = "fakeId3")
            )
        )
        val fullSovTp = touchingPoint.copy(
            campaignData = touchingPoint.campaignData.copy(shareOfVoice = listOf("100%"))
        )
        val category = adCategory.copy(id = "otherId")

        // WHEN
        val result = useCase(category, adCity)

        // THEN
        assertEquals(listOf(fullSovTp), result)
        coVerify(exactly = 1) { adViewRepository.getAdTouchingPoint() }
    }

    @Test
    fun `invoke, full share of voice but not targeting ad city`() = runTest {
        // GIVEN
        coEvery { adViewRepository.getAdTouchingPoint() } returns Resource.Success(
            listOf(
                touchingPoint.copy(
                    campaignData = touchingPoint.campaignData.copy(
                        shareOfVoice = listOf("100%")
                    )
                ),
                touchingPoint.copy(id = "fakeId2"),
                touchingPoint.copy(id = "fakeId3")
            )
        )
        val fullSovTp = touchingPoint.copy(
            campaignData = touchingPoint.campaignData.copy(shareOfVoice = listOf("100%"))
        )
        val city = adCity.copy(id = "otherId")

        // WHEN
        val result = useCase(adCategory, city)

        // THEN
        assertEquals(listOf(fullSovTp), result)
        coVerify(exactly = 1) { adViewRepository.getAdTouchingPoint() }
    }

    @Test
    fun `invoke, full share of voice but empty list of categories`() = runTest {
        // GIVEN
        coEvery { adViewRepository.getAdTouchingPoint() } returns Resource.Success(
            listOf(
                touchingPoint.copy(
                    campaignData = touchingPoint.campaignData.copy(
                        shareOfVoice = listOf("100%")
                    )
                ),
                touchingPoint.copy(id = "fakeId2", targeting = Targeting(emptyList(), emptyList())),
                touchingPoint.copy(id = "fakeId3", targeting = Targeting(emptyList(), emptyList()))
            )
        )
        val fullSovTp = touchingPoint.copy(
            campaignData = touchingPoint.campaignData.copy(shareOfVoice = listOf("100%"))
        )

        // WHEN
        val result = useCase(adCategory, adCity)

        // THEN
        assertEquals(listOf(fullSovTp), result)
        coVerify(exactly = 1) { adViewRepository.getAdTouchingPoint() }
    }
}