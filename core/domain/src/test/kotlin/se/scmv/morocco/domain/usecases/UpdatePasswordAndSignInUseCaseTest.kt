package se.scmv.morocco.domain.usecases

import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.coVerifyAll
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import kotlinx.coroutines.test.runTest
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.NetworkErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.UpdatePasswordErrors
import se.scmv.morocco.domain.repositories.AuthenticationRepository
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertTrue

@RunWith(JUnit4::class)
class UpdatePasswordAndSignInUseCaseTest {

    @MockK
    lateinit var authenticationRepository: AuthenticationRepository

    @MockK
    lateinit var logoutUseCase: LogoutUseCase

    @InjectMockKs
    lateinit var useCase: UpdatePasswordAndSignInUseCase

    private val email = "<EMAIL>"
    private val currentPassword = "123456"
    private val newPassword = "12345678"

    @BeforeTest
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `updatePassword, success`() = runTest {
        // Given
        coEvery {
            authenticationRepository.updatePassword(
                currentPassword = currentPassword,
                newPassword = newPassword
            )
        } returns Resource.Success(Unit)
        coEvery {
            authenticationRepository.signIn(emailOrPhone = email, password = newPassword)
        } returns Resource.Success("Fake accessToken")

        // When
        val result = useCase(
            email = email,
            currentPassword = currentPassword,
            newPassword = newPassword
        )

        // Then
        assertTrue { result == Resource.Success(Unit) }
        coVerifyAll {
            authenticationRepository.updatePassword(
                currentPassword = currentPassword,
                newPassword = newPassword
            )
            authenticationRepository.signIn(emailOrPhone = email, password = newPassword)
        }
    }

    @Test
    fun `updatePassword, failure`() = runTest {
        // Given
        coEvery {
            authenticationRepository.updatePassword(currentPassword, newPassword)
        } returns Resource.Failure(NetworkAndBackendErrors.Network(NetworkErrors.UNKNOWN))

        // When
        val result = useCase(
            email = email,
            currentPassword = currentPassword,
            newPassword = newPassword
        )

        // Then
        assertTrue {
            result == Resource.Failure(
                error = UpdatePasswordErrors.NetworkOrBackend(
                    error = NetworkAndBackendErrors.Network(NetworkErrors.UNKNOWN)
                )
            )
        }
        coVerify {
            authenticationRepository.updatePassword(
                currentPassword = currentPassword,
                newPassword = newPassword
            )
        }
    }

    @Test
    fun `updatePassword, update success but signIn failure`() = runTest {
        // Given
        coEvery {
            authenticationRepository.updatePassword(currentPassword, newPassword)
        } returns Resource.Success(Unit)
        coEvery {
            authenticationRepository.signIn(emailOrPhone = email, password = newPassword)
        } returns Resource.Failure(NetworkAndBackendErrors.Network(NetworkErrors.UNKNOWN))
        coEvery {
            logoutUseCase()
        } returns Resource.Success(true)

        // When
        val result = useCase(
            email = email,
            currentPassword = currentPassword,
            newPassword = newPassword
        )

        // Then
        assertTrue { result == Resource.Failure(error = UpdatePasswordErrors.NeedToReLogin) }
        coVerifyAll {
            authenticationRepository.updatePassword(
                currentPassword = currentPassword,
                newPassword = newPassword
            )
            authenticationRepository.signIn(emailOrPhone = email, password = newPassword)
            logoutUseCase()
        }
    }
}