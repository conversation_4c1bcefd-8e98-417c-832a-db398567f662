package se.scmv.morocco.data.mappers

enum class OrionComponentType(val key: String) {
    NEUTRAL("neutral"),
    SINGLE_SELECT_CATEGORY_DROPDOWN("single_select_category_dropdown_adinsert"),
    SINGLE_SELECT_CATEGORY_DROPDOWN_SEARCH("single_select_category_dropdown_search"),
    SINGLE_SELECT_SMART_DROPDOWN("single_select_smart_dropdown"),
    SINGLE_SELECT_SMART_DROPDOWN_ICON("single_select_smart_dropdown_icon"),
    SINGLE_SELECT_SIMPLE_DROPDOWN("single_select_simple_dropdown"),
    MULTIPLE_SELECT_SMART_DROPDOWN("multiple_select_smart_dropdown"),
    MULTIPLE_SELECT_SMART_DROPDOWN_ICON("multiple_select_smart_dropdown_icon"),
    MULTISELECT_EXTENDED("multiselect_extended"),
    SINGLE_SELECT_EXTENDED("single_select_extended"),
    TEXT_FIELD("text_field"),
    MEASURE_TEXT_FIELD("measure_text_field"),
    LARGE_TEXT_FIELD("large_text_field"),
    NATIVE_DROPDOWN("native_dropdown"),
    TOGGLE_FIELD("toggle_field"),
    TOGGLE_FIELD_FILTER("toggle_field_filter"),
    DATES_PICKER("dates_picker"),
    TIME_PICKER("time_picker"),
    NUMBER_COUNTER("number_counter"),
    BOOLEAN("boolean"),
    IMAGE_UPLOADER("image_uploader"),
    VIDEO_UPLOADER("video_uploader"),
    MESSAGE_WARNING("message_warning"),
    MESSAGE_INFO("message_info"),
    MIN_MAX_FIELD("min_max_field"),
    SLIDER("slider")
}

enum class OrionComponentTypeFieldUIType(val id: String) {
    SINGLE_SELECT_CATEGORY_DROPDOWN_SEARCH("single_select_category_dropdown_search"),
    SINGLE_SELECT_SMART_DROPDOWN("single_select_smart_dropdown"),
    MULTIPLE_SELECT_SMART_DROPDOWN("multiple_select_smart_dropdown"),
    MULTIPLE_SELECT_SMART_DROPDOWN_ICON("multiple_select_smart_dropdown_icon"),
    MULTISELECT_EXTENDED("multiselect_extended"),
    SINGLE_SELECT_SMART_DROPDOWN_ICON("single_select_smart_dropdown_icon"),
    SINGLE_SELECT_EXTENDED("single_select_extended"),
    MEASURE_TEXT_FIELD("measure_text_field"),
    TEXT_FIELD("text_field"),
    TOGGLE_FIELD_FILTER("toggle_field_filter"),
    MIN_MAX_FIELD("min_max_field"),
    SLIDER("slider")
}