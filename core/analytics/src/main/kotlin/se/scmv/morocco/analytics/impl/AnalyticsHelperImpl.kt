package se.scmv.morocco.analytics.impl

import com.braze.Braze
import com.facebook.appevents.AppEventsLogger
import com.google.firebase.analytics.FirebaseAnalytics
import com.tiktok.TikTokBusinessSdk
import com.tiktok.appevents.base.TTBaseEvent
import com.tiktok.appevents.contents.TTPurchaseEvent
import com.tiktok.appevents.contents.TTViewContentEvent
import se.scmv.morocco.analytics.AnalyticsHelper
import se.scmv.morocco.analytics.formatters.toBrazeProperties
import se.scmv.morocco.analytics.formatters.toBundle
import se.scmv.morocco.analytics.models.AnalyticsAddons
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.Param
import se.scmv.morocco.analytics.models.UserProperties
import javax.inject.Inject

/**
 * Implementation of the [AnalyticsHelper] interface for logging events and managing user data
 * across multiple analytics platforms including Firebase, Braze, and Facebook.
 *
 * @property braze The Braze SDK instance for tracking user events and properties.
 * @property facebookLogger The Facebook SDK instance for logging analytics events.
 * @property firebaseAnalytics The Firebase Analytics instance for logging events and user properties.
 */
internal class AnalyticsHelperImpl @Inject constructor(
    private val braze: Braze,
    private val facebookLogger: AppEventsLogger,
    private val firebaseAnalytics: FirebaseAnalytics,
) : AnalyticsHelper {

    override fun logEvent(event: AnalyticsEvent, where: Set<AnalyticsAddons>) {
        where.forEach {
            when (it) {
                AnalyticsAddons.FIREBASE -> firebaseAnalytics.logEvent(
                    event.name,
                    event.properties.toBundle()
                )

                AnalyticsAddons.BRAZE -> braze.logCustomEvent(
                    event.name,
                    event.properties.toBrazeProperties()
                )

                AnalyticsAddons.FACEBOOK -> facebookLogger.logEvent(
                    event.name,
                    event.properties.toBundle()
                )

                AnalyticsAddons.TIKTOK -> {
                    when (event.name) {
                        //Adview equivalent
                        AnalyticsEvent.Types.TIKTOK_VIEW_CONTENT -> {
                            val ttEvent = TTViewContentEvent.newBuilder()
                            event.properties.forEach { param ->
                                when (param.key) {
                                    AnalyticsEvent.ParamKeys.CONTENT_ID -> {
                                        ttEvent.setContentId(param.value)
                                    }

                                    AnalyticsEvent.ParamKeys.CONTENT_TYPE -> {
                                        ttEvent.setContentType(param.value)
                                    }
                                }
                            }
                            TikTokBusinessSdk.trackTTEvent(ttEvent.build())
                        }
                        //Lead equivalent
                        AnalyticsEvent.Types.TIKTOK_PURCHASE -> {
                            val ttEvent = TTPurchaseEvent.newBuilder()
                            event.properties.forEach { param ->
                                when (param.key) {
                                    AnalyticsEvent.ParamKeys.CONTENT_ID -> {
                                        ttEvent.setContentId(param.value)
                                    }

                                    AnalyticsEvent.ParamKeys.CONTENT_TYPE -> {
                                        ttEvent.setContentType(param.value)
                                    }
                                }
                            }
                            TikTokBusinessSdk.trackTTEvent(ttEvent.build())
                        }

                        AnalyticsEvent.Types.TIKTOK_INSERT_AD, AnalyticsEvent.Types.TIKTOK_SEARCH_RESULT -> {
                            // Custom events with content_id and content_type
                            val ttEvent = TTBaseEvent.newBuilder(event.name)
                            event.properties.forEach { param ->
                                when (param.key) {
                                    AnalyticsEvent.ParamKeys.CONTENT_ID, AnalyticsEvent.ParamKeys.CONTENT_TYPE -> {
                                        ttEvent.addProperty(param.key, param.value)
                                    }
                                }
                            }
                            TikTokBusinessSdk.trackTTEvent(ttEvent.build())
                        }

                        AnalyticsEvent.Types.TIKTOK_START_INSERT_AD -> {
                            TikTokBusinessSdk.trackTTEvent(
                                TTBaseEvent.newBuilder(event.name).build()
                            )
                        }
                    }
                }
            }
        }
    }

    override fun identify(userId: String) {
        braze.changeUser(userId)
        AppEventsLogger.setUserID(userId)
        firebaseAnalytics.setUserId(userId)
    }

    override fun setUserProperties(properties: UserProperties) {
        braze.currentUser?.apply {
            setFirstName(properties.name)
            setEmail(properties.email)
            setPhoneNumber(properties.phone)
        }
        AppEventsLogger.setUserData(
            email = properties.email,
            firstName = properties.name,
            phone = properties.phone,
            lastName = null, dateOfBirth = null, gender = null,
            city = null, state = null, zip = null, country = null
        )
        firebaseAnalytics.setUserProperty(AnalyticsEvent.ParamKeys.NAME, properties.name)
        firebaseAnalytics.setUserProperty(AnalyticsEvent.ParamKeys.EMAIL, properties.email)
        firebaseAnalytics.setUserProperty(AnalyticsEvent.ParamKeys.PHONE, properties.phone)
    }

    override fun clearUserData() {
        braze.requestImmediateDataFlush()
        braze.changeUser(null)
        facebookLogger.flush()
        AppEventsLogger.clearUserID()
        AppEventsLogger.clearUserData()
        firebaseAnalytics.setUserId(null)
        firebaseAnalytics.setUserProperty(AnalyticsEvent.ParamKeys.NAME, null)
        firebaseAnalytics.setUserProperty(AnalyticsEvent.ParamKeys.EMAIL, null)
        firebaseAnalytics.setUserProperty(AnalyticsEvent.ParamKeys.PHONE, null)
        TikTokBusinessSdk.logout()
    }
}

fun AnalyticsHelper.trackTikTokViewContent(contentId: String, contentType: String) {
    logEvent(
        event = AnalyticsEvent(
            name = AnalyticsEvent.Types.TIKTOK_VIEW_CONTENT,
            properties = setOf(
                Param(AnalyticsEvent.ParamKeys.CONTENT_ID, contentId),
                Param(AnalyticsEvent.ParamKeys.CONTENT_TYPE, "subcategory_id_${contentType}")
            )
        ),
        where = setOf(AnalyticsAddons.TIKTOK)
    )
}

fun AnalyticsHelper.trackTikTokPurchase(contentId: String, contentType: String) {
    logEvent(
        event = AnalyticsEvent(
            name = AnalyticsEvent.Types.TIKTOK_PURCHASE,
            properties = setOf(
                Param(AnalyticsEvent.ParamKeys.CONTENT_ID, contentId),
                Param(AnalyticsEvent.ParamKeys.CONTENT_TYPE, "subcategory_id_${contentType}")
            )
        ),
        where = setOf(AnalyticsAddons.TIKTOK)
    )
}

fun AnalyticsHelper.trackTikTokInsertAd(contentId: String, contentType: String) {
    logEvent(
        event = AnalyticsEvent(
            name = AnalyticsEvent.Types.TIKTOK_INSERT_AD,
            properties = setOf(
                Param(AnalyticsEvent.ParamKeys.CONTENT_ID, "VAS_${contentId}"),
                Param(AnalyticsEvent.ParamKeys.CONTENT_TYPE, "subcategory_id_${contentType}")
            )
        ),
        where = setOf(AnalyticsAddons.TIKTOK)
    )
}

fun AnalyticsHelper.trackTikTokSearchResult(contentType: String) {
    logEvent(
        event = AnalyticsEvent(
            name = AnalyticsEvent.Types.TIKTOK_SEARCH_RESULT,
            properties = setOf(
                Param(AnalyticsEvent.ParamKeys.CONTENT_TYPE, "subcategory_id_${contentType}")
            )
        ),
        where = setOf(AnalyticsAddons.TIKTOK)
    )
}

fun AnalyticsHelper.trackTikTokStartInsertAd() {
    logEvent(
        event = AnalyticsEvent(
            name = AnalyticsEvent.Types.TIKTOK_START_INSERT_AD,
            properties = setOf(
                Param(AnalyticsEvent.ParamKeys.CONTENT_ID, "start_insert_${System.currentTimeMillis()}"),
                Param(AnalyticsEvent.ParamKeys.CONTENT_TYPE, "ad_insert_start")
            )
        ),
        where = setOf(AnalyticsAddons.TIKTOK)
    )
}