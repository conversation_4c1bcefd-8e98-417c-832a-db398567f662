package se.scmv.morocco.analytics.models

/**
 * Represents an analytics event with a type, name, and associated parameters.
 *
 * @property name The name of the event.
 * @property properties A [Set] of additional parameters associated with the event.
 */
data class AnalyticsEvent(
    val name: String,
    val properties: Set<Param> = emptySet(),
) {
    /**
     * Contains standard event types for analytics.
     */
    object Types {
        const val SCREEN_VIEW = "screen_view"
        const val ELEMENT_CLICKED = "element_clicked"
        const val LOGGED_IN = "logged_in"
        const val LOG_IN = "log_in"
        const val LOG_IN_FACEBOOK = "log_in_facebook"
        const val LOG_IN_GOOGLE = "log_in_google"
        const val INSERT_AD = "insert_ad"
        const val INSERTED_AD = "inserted_ad"
        const val VAS_SELECTION = "vas_selection"
        const val ELEMENT_DISPLAYED = "element_displayed"
        // Add more standard event types here
        const val SHOP_LEAD = "shop_lead"
        const val TIKTOK_VIEW_CONTENT = "VIEW_CONTENT"
        const val TIKTOK_PURCHASE = "PURCHASE"
        const val TIKTOK_INSERT_AD = "INSERT_AD"
        const val TIKTOK_SEARCH_RESULT = "SEARCH_RESULT"
        const val TIKTOK_START_INSERT_AD = "START_INSERT_AD"
    }

    /**
     * Contains standard parameter keys for analytics events.
     */
    object ParamKeys {
        const val SCREEN_NAME = "content_type"
        const val ELEMENT_NAME = "element_name"
        const val SOURCE = "source"
        const val ELEMENT_SOURCE = "element_source"
        const val PAGE_NAME = "page_name"
        const val LANG = "lang"
        const val EMAIL = "email"
        const val NAME = "name"
        const val PHONE = "phone"
        const val ACCOUNT_TYPE = "account_type"
        const val LOGIN_TYPE = "login_type"
        const val LEAD = "lead"
        const val LEAD_TYPE = "lead_type"
        const val VALUE = "value"
        const val CONTENT_TYPE = "content_type"
        const val CONTENT_ID = "content_id"

        const val AD_ID = "ad_id"
        const val AD_LIST_ID = "ad_list_id"
        const val AD_TYPE = "ad_type"
        const val AD_PRICE = "ad_price"
        const val VERTICAL_ID = "vertical_id"
        const val VERTICAL_NAME = "vertical_name"
        const val CATEGORY_ID = "category_id"
        const val CATEGORY_NAME = "category_name"
        const val SUB_CATEGORY_ID = "subcategory_id"
        const val SUB_CATEGORY_NAME = "subcategory_name"
        const val CITY = "city"
        const val CITY_ID = "city_id"
        const val AREA = "area"
        const val AREA_ID = "area_id"
        const val SELLER_TYPE = "seller_type"
        const val SELLER_NAME = "seller_name"
        const val SELLER_ID = "seller_id"
        const val SELLER_PHONE = "seller_phone"
        const val PICTURE_COUNT = "picture_count"
        const val HAS_PHONE = "has_phone"
        const val IS_PHONE_VERIFIED = "is_phone_verified"
        const val PUBLISH_DATE = "publish_date"
        const val STEP = "step"
        const val ERROR_TYPE = "error_type"
        const val VAS_ID = "vas_id"
        const val VAS_NAME = "vas_name"
        const val VAS_DURATION = "vas_duration"
        const val NOTIFICATION_SOURCE = "notification_source"

        // Add more standard parameter keys here
    }

    /**
     * Contains standard parameter values for analytics events.
     */
    object ParamValues {
        const val ACCOUNT_TYPE_SHOP = "pro"
        const val ACCOUNT_TYPE_PRIVATE = "private"
        const val CANCEL_ORDER = "cancelorder"
        const val SELECT_STATUS = "select_status"
        const val DELETE_SELECTED = "delete_selected"
        const val FINAL_DELETE = "final_delete"
        const val REACTIVATE = "reactivate"
        const val SHOW_MORE = "show_more"
        const val AD_DETAIL = "ad_detail"
        const val SELECT_ALL = "select_all"
        const val AD_EDIT = "ad_edit"
        const val LIMIT = "limit"
        const val NO_LIMIT = "nolimit"
        const val PAYMENT = "payment"
        const val VAS_SELECTION = "vas_selection"
        const val METHOD_SELECTION = "method_selection"
        const val SAVE_AD = "save_ad"
        const val UNSAVE_AD = "unsave_ad"
        const val SHARE_AD = "share_ad"
        const val CHAT = "chat"
        const val CALL = "call"
        const val REPORT_AD = "report_ad"
        const val BUY = "buy"
        const val BOOSTER = "booster"
        const val SHOW_PHONE = "show phone"
        const val WHATSAPP = "whatsapp"
        const val CAR_INSPECTION_CTA = "car_inspection_CTA"
        const val SUBMIT_INSPECTION_FORM = "submit_inspection_form"
        const val CAR_INSPECTION_REPORT = "car_inspection_report"
        const val DOWNLOAD_INSPECTION_REPORT = "download_inspection_report"
        const val PREVIEW_INSPECTION_REPORT = "preview_inspection_report"
        const val FIREBASE = "firebase"
    }

    /**
     * Contains standard screens names for analytics events.
     */
    object ScreensNames {
        const val LOGIN = "login"
        const val SIGNUP = "signup"
        const val PASSWORD_RECOVERY = "password_recovery"
        const val ACCOUNT = "mysetting"
        const val ACCOUNT_EDIT = "editprofile"
        const val STATISTICS = "mystatistics"
        const val UPDATE_PASSWORD = "update_password"
        const val ORDERS = "myorders"
        const val ACCOUNT_ADS = "myads"
        const val BOOKMARKED_ADS = "mysavedads"
        const val BOOKMARKED_SEARCH = "mysavedsearch"
        const val AD_INSERT = "adinsert"
        const val AD_EDIT = "adedit"
        const val AD_VIEW = "adview"
        // Add more standard parameter keys here
        const val SHOPE_PAGE = "shop_page"
    }
}
