package se.scmv.morocco.core

import android.app.Dialog
import android.os.Bundle
import android.view.View
import android.widget.FrameLayout
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

abstract class BaseBottomSheet(
    private val mState: Int,
    private val mDraggable: Boolean
) : BottomSheetDialogFragment() {

    constructor(): this(BottomSheetBehavior.STATE_EXPANDED, true)

    constructor(
        mState: Int,
    ) : this(mState, true)

    constructor(
         mDraggable: <PERSON>olean,
    ) : this(BottomSheetBehavior.STATE_EXPANDED, mDraggable)

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.setOnShowListener { dialogInterface ->
            val d = dialogInterface as BottomSheetDialog
            with(
                d.findViewById<View>(
                    com.google.android.material.R.id.design_bottom_sheet
                ) as FrameLayout?
            ) {
                this?.let {
                    BottomSheetBehavior.from(it).apply {
                        this.state = mState
                        this.isDraggable = mDraggable
                    }
                }
            }
        }
        return dialog
    }
}