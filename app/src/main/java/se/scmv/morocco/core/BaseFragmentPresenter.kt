package se.scmv.morocco.core

import se.scmv.morocco.fragments.BaseFragment

open class BaseFragmentPresenter<V : BaseFragment?> : BaseMvpPresenter<V> {
        var view: V? = null
        override fun attach(v: V) {
                view = v
        }

        override fun detach() {
                view = null
        }

        override val isAttached: <PERSON>olean
                get() = view != null
}