package se.scmv.morocco.core

import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.net.Uri
import android.widget.ImageView
import androidx.annotation.DrawableRes
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.toBitmap
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.swiperefreshlayout.widget.CircularProgressDrawable
import coil.imageLoader
import coil.request.ImageRequest
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.google.android.material.button.MaterialButton
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.common.math.Quantiles
import com.stfalcon.chatkit.commons.ImageLoader
import com.stfalcon.chatkit.commons.models.MessageContentType
import kotlinx.coroutines.launch
import se.scmv.morocco.Avito
import se.scmv.morocco.GetChatConversationByIdQuery
import se.scmv.morocco.R
import se.scmv.morocco.login.models.AccountToken
import se.scmv.morocco.utils.Constants.DATE_FORMAT_ISO_8601
import se.scmv.morocco.utils.DateUtils
import java.io.File

fun getRequestToken(): String? = AccountToken.getCurrentToken(Avito.context)?.requestToken
fun getTokenStoreId(): Int = AccountToken.getCurrentToken(Avito.context)?.storeId ?: -1

fun GetChatConversationByIdQuery.Partner.getName(): String? =
    if (__typename == "StoreProfile") onStoreProfile?.name else onPrivateProfile?.name

fun String?.formattedDate(format: String = DATE_FORMAT_ISO_8601) = DateUtils.formatStringToDate(
    DateUtils.formatDateLocalization(this ?: "", DATE_FORMAT_ISO_8601),
    format
)

fun <T : Any> T.singletonList(): List<T> = listOf(this)
fun <T : Any> T.mutableSingletonList(): MutableList<T> = mutableListOf(this)
fun <T : Any> MutableList<T>.presentIfAbsent(t: T) {
    if (!contains(t)) add(t)
}

fun Fragment.setupChatkitImageLoader(): ImageLoader =
    ImageLoader { imageView: ImageView?, url: String?, imageType: MessageContentType.Type?, _ ->
        if (imageView != null) {
            val uri = imageType?.let { type ->
                when (type) {
                    MessageContentType.Type.LOCAL -> url?.let {
                        Uri.fromFile(
                            File(it)
                        )
                    }

                    MessageContentType.Type.REMOTE -> Uri.parse(url)
                }
            }
            Glide.with(this)
                .load(uri)
                .fitCenter()
                .centerCrop()
                .error(R.drawable.ic_chat_placeholder)
                .into(imageView)
        }
    }

fun Fragment.showConfirmationDialog(
    title: Int,
    description: String? = null,
    onConfirm: () -> Unit,
    onDecline: () -> Unit
) {
    showConfirmationDialog(getString(title), description, onConfirm, onDecline)
}

fun Fragment.showConfirmationDialog(
    title: String,
    description: String? = null,
    onConfirm: () -> Unit,
    onDecline: () -> Unit
) {
    MaterialAlertDialogBuilder(requireContext())
        .setTitle(title)
        .setMessage(description)
        .setPositiveButton(
            R.string.yes
        ) { _, _ ->
            onConfirm.invoke()
        }.setNegativeButton(
            R.string.no
        ) { _, _ ->
            onDecline.invoke()
        }
        .show()
}

fun LifecycleOwner.repeatWithCreatedLifeCycle(block: suspend () -> Unit) {
    lifecycleScope.launch {
        repeatOnLifecycle(Lifecycle.State.CREATED) {
            block()
        }
    }
}

fun LifecycleOwner.repeatWithStartedLifeCycle(block: suspend () -> Unit) {
    lifecycleScope.launch {
        repeatOnLifecycle(Lifecycle.State.STARTED) {
            block()
        }
    }
}

fun MaterialButton.showHideProgress(showProgress: Boolean) {
    icon = if (showProgress) {
        CircularProgressDrawable(context).apply {
            setStyle(CircularProgressDrawable.LARGE)
            setColorSchemeColors(ContextCompat.getColor(context!!, R.color.avitoPrimary))
            start()
        }
    } else null
    if (icon != null) { // callback to redraw button icon
        icon.callback = object : Drawable.Callback {
            override fun unscheduleDrawable(who: Drawable, what: Runnable) {
            }

            override fun invalidateDrawable(who: Drawable) {
                <EMAIL>()
            }

            override fun scheduleDrawable(who: Drawable, what: Runnable, `when`: Long) {
            }
        }
    }
}

/**
 * To Use this function prefer in the xml to set:
 */

// I replace Glid by Coil to switch from listing to ad view witch caching available
fun ImageView.loadUrlWithPlaceholder(
    url: String?,
    @DrawableRes placeholder: Int = R.drawable.img_no_image_placeholder,
    @DrawableRes error: Int = R.drawable.img_no_image_placeholder,
) {
    if (url == null) {
        setImageDrawable(ContextCompat.getDrawable(context, placeholder))
        scaleType = ImageView.ScaleType.CENTER
        return
    }

    val progressDrawable = CircularProgressDrawable(context).apply {
        setColorSchemeColors(R.color.avitoPrimary, R.color.avitoPrimary, R.color.grey_line)
        centerRadius = 30f
        strokeWidth = 5f
        start()
    }

    val request = ImageRequest.Builder(context)
        .data(url)
        .placeholder(progressDrawable)
        .error(error)
        .target { drawable ->
            setDrawableToImageView(drawable)
        }
        .build()

    context.imageLoader.enqueue(request)
}

private fun ImageView.setDrawableToImageView(drawable: Drawable?) {
    if (drawable == null) return
    if (drawable is BitmapDrawable) {
        setImageBitmap(drawable.bitmap)
    } else {
        setImageBitmap(drawable.toBitmap())
    }
}
