package se.scmv.morocco.core

import android.content.Context
import androidx.annotation.StringRes

sealed interface UiText {
    data class FromRes(@StringRes val id: Int, val args: List<Any> = emptyList()) : UiText

    data class AsText(val value: String) : UiText

    fun getValue(context: Context): String  = when(this) {
        is AsText -> value
        is FromRes -> context.getString(id, args)
    }
}