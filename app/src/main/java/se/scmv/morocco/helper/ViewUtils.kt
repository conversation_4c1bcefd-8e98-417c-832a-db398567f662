package se.scmv.morocco.helper

import android.app.Activity
import android.graphics.Rect
import android.util.DisplayMetrics
import android.view.View


object ViewUtils {

        fun isVisibleOrNot(view: View?, activity: Activity): Boolean {
                if (view == null) {
                        return false
                }
                if (!view.isShown) {
                        return false
                }
                val displayMetrics = DisplayMetrics()
                activity.windowManager.defaultDisplay.getMetrics(displayMetrics)
                val height = displayMetrics.heightPixels
                val width = displayMetrics.widthPixels

                val actualPosition = Rect()
                view.getGlobalVisibleRect(actualPosition)
                val screen = Rect(0, 0, width, height)
                return actualPosition.intersect(screen)
        }
}