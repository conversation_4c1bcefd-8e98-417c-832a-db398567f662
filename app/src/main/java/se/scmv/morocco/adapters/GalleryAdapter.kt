package se.scmv.morocco.adapters

import android.net.Uri
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import se.scmv.morocco.fragments.GalleryFragment
import se.scmv.morocco.fragments.GalleryFragment.Companion.getInstance

/**
 * Created by amine on 28/01/15.
 */
class GalleryAdapter(
    private val activity: FragmentActivity,
    private val pictures: List<String>?,
    private val videos: List<String>? = null,
    private val videoFirst: Boolean? = false
) : FragmentStateAdapter(activity) {

    private val fragments: MutableList<GalleryFragment> = ArrayList()

    init {
        if (videoFirst != null && videoFirst == true) {
            if (videos != null) {
                for (video in videos) {
                    fragments.add(getInstance(Uri.parse(video), true, pictures?.firstOrNull()))
                }
            }
            if (pictures != null) {
                for (picture in pictures) {
                    fragments.add(getInstance(Uri.parse(picture)))
                }
            }
        } else {
            if (pictures != null) {
                for (picture in pictures) {
                    fragments.add(
                        getInstance(
                            Uri.parse(picture)
                        )
                    )
                }
            }
            if (videos != null) {
                for (video in videos) {
                    fragments.add(
                        getInstance(
                            Uri.parse(video),
                            true,
                            pictures?.first()
                        )
                    )
                }
            }
        }
    }

    override fun getItemCount(): Int {
        var count = 0
        if (pictures != null) count = pictures.size
        if (videos != null) count += videos.size
        return count
    }

    override fun createFragment(position: Int): Fragment {
        return fragments[position]
    }
}