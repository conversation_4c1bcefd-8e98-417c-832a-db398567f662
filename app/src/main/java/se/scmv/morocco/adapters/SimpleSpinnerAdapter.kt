package se.scmv.morocco.adapters

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseAdapter
import android.widget.TextView
import se.scmv.morocco.R

/**
 * Created by hb on 26-Sep-16.
 */
class SimpleSpinnerAdapter(var context: Context, var spinnerItem: ArrayList<String>) :
        BaseAdapter() {
        override fun getCount(): Int {
                return spinnerItem.size
        }

        override fun getItem(i: Int): Any {
                return spinnerItem[i]
        }

        override fun getItemId(i: Int): Long {
                return i.toLong()
        }

        override fun getView(position: Int, view: View?, viewGroup: ViewGroup): View? {
                val layoutInflater = LayoutInflater.from(context)
                val textView = layoutInflater.inflate(
                        R.layout.spinner_list_item,
                        viewGroup,
                        false
                ) as TextView
                textView.text = spinnerItem[position]
                return textView
        }
}