package se.scmv.morocco.services

import android.app.ProgressDialog
import android.content.Context
import android.graphics.Bitmap
import android.os.AsyncTask
import android.view.View
import se.scmv.morocco.R
import se.scmv.morocco.utils.ExternalStorageUtils
import se.scmv.morocco.utils.ImageUtils
import se.scmv.morocco.utils.NotifyUtils
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Async task for saving the screenshot
 */
class SaveImageInternallyService : AsyncTask<Bitmap?, Void?, File?> {
        private var pDialog: ProgressDialog? = null
        private var context: Context
        private var view: View
        var screenShotTakenCallBack: ScreenShotTakenCallBack? = null

        constructor(
                context: Context,
                view: View,
                screenShotTakenCallBack: ScreenShotTakenCallBack?
        ) {
                this.context = context
                this.view = view
                this.screenShotTakenCallBack = screenShotTakenCallBack
        }

        constructor(context: Context, view: View) {
                this.context = context
                this.view = view
        }

        override fun onPreExecute() {
                pDialog = ProgressDialog(context)
                pDialog?.setMessage(context.resources.getString(R.string.vas_saving_screenshot))
                pDialog?.show()
        }

        override fun doInBackground(vararg params: Bitmap?): File? {
                val screenShotBitmap = params[0]
                if (screenShotBitmap != null) {
                        val now =
                                SimpleDateFormat("yyyy-MM-dd_hh:mm:ss", Locale.getDefault()).format(
                                        Date()
                                )
                        return ImageUtils.saveBitmapInFile(screenShotBitmap, "avito_code_$now.jpg")
                }
                return null
        }

        override fun onPostExecute(file: File?) {
                super.onPostExecute(file)
                if (file != null) {
                        ExternalStorageUtils.notifyTheExternalStorage(file, context)
                        NotifyUtils.displaySuccessSnackbar(view, R.string.common_screenshot_saved)
                        pDialog?.dismiss()
                } else {
                        NotifyUtils.displayErrorSnackbarFromString(
                                view,
                                context.getString(R.string.common_screenshot_not_saved_error)
                        )
                        pDialog?.dismiss()
                }
                if (screenShotTakenCallBack != null) {
                        screenShotTakenCallBack?.updateView()
                }
        }

        interface ScreenShotTakenCallBack {
                fun updateView()
        }
}