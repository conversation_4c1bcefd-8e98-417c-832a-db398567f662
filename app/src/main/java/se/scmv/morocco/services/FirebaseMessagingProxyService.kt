package se.scmv.morocco.services

import android.app.Service
import com.braze.push.BrazeFirebaseMessagingService
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import se.scmv.morocco.core.AvitoPushNotificationsService
import se.scmv.morocco.messaging.notifications.AvitoMessagingService
import java.lang.reflect.Field

class FirebaseMessagingProxyService : FirebaseMessagingService() {
        companion object {
                const val AF_UNINSTALL_TRACKING =
                        "af-uninstall-tracking" //Solving android uninstall push notification not being silent
        }

        private val messagingServices: List<FirebaseMessagingService> by lazy {
                listOf(
                        AvitoPushNotificationsService(),
                        AvitoMessagingService(),
                        BrazeFirebaseMessagingService()
                ).onEach { it.injectContext(this) }
        }

        override fun onNewToken(token: String) {
                super.onNewToken(token)
                messagingServices.forEach { it.onNewToken(token) }
        }

        override fun onMessageReceived(remoteMessage: RemoteMessage) {
                super.onMessageReceived(remoteMessage)
                messagingServices.forEach {
                        if (remoteMessage.data.containsKey(AF_UNINSTALL_TRACKING)) {
                                return
                        } else {
                                it.onMessageReceived(remoteMessage)
                        }
                }
        }

        override fun onDeletedMessages() {
                super.onDeletedMessages()
                messagingServices.forEach { it.onDeletedMessages() }
        }

        override fun onMessageSent(message: String) {
                super.onMessageSent(message)
                messagingServices.forEach { it.onMessageSent(message) }
        }

        override fun onSendError(message: String, e: Exception) {
                super.onSendError(message, e)
                messagingServices.forEach { it.onSendError(message, e) }
        }
}

fun <T : Service> T.injectContext(context: T, func: T.() -> Unit = {}) {
        setField("mBase", context)
        func()
}

fun Class<*>.findDeclaredField(name: String): Field? {
        var clazz: Class<*>? = this
        do {
                try {
                        return clazz?.getDeclaredField(name)
                } catch (e: Throwable) {
                }
                clazz = clazz?.superclass
        } while (clazz != null)
        return null
}

fun Any.setField(name: String, value: Any): Boolean =
        javaClass.findDeclaredField(name)?.let {
                try {
                        it.isAccessible = true
                        it.set(this, value)
                        true
                } catch (e: Throwable) {
                        false
                }
        } ?: false

val Any.TAG: String
        get() {
                val tag = javaClass.simpleName
                val max = 23
                return if (tag.length <= max) tag else tag.substring(0, max)
        }