package se.scmv.morocco.ui

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.consumeWindowInsets
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Menu
import androidx.compose.material3.Badge
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.DrawerState
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilledIconButton
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalDrawerSheet
import androidx.compose.material3.ModalNavigationDrawer
import androidx.compose.material3.NavigationBarDefaults
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.NavigationBarItemDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SearchBar
import androidx.compose.material3.SearchBarDefaults
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.contentColorFor
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.Stable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.isTraversalGroup
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.traversalIndex
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavDestination
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.navigation.navDeepLink
import androidx.navigation.toRoute
import coil.compose.AsyncImage
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.android.EntryPointAccessors
import dagger.hilt.components.SingletonComponent
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import se.scmv.morocco.R
import se.scmv.morocco.account.presentation.bookmarks.master.AccountBookmarksRoute
import se.scmv.morocco.account.presentation.master.AccountMasterRoute
import se.scmv.morocco.account.presentation.messaging.MessagingSubscriptionManager
import se.scmv.morocco.account.presentation.messaging.ui.conversation.ConversationListScreen
import se.scmv.morocco.account.presentation.navigation.AccountAdsRoute
import se.scmv.morocco.account.presentation.navigation.AccountBookmarksRoute
import se.scmv.morocco.account.presentation.navigation.AccountEditRoute
import se.scmv.morocco.account.presentation.navigation.AccountMasterRoute
import se.scmv.morocco.account.presentation.navigation.AccountOrdersRoute
import se.scmv.morocco.account.presentation.navigation.AccountStatisticsRoute
import se.scmv.morocco.account.presentation.navigation.ChatRoute
import se.scmv.morocco.account.presentation.navigation.MessagingRoute
import se.scmv.morocco.account.presentation.navigation.UpdatePasswordRoute
import se.scmv.morocco.account.presentation.statistics.AccountStatisticsRoute
import se.scmv.morocco.ad.listing.AdsListingRoute
import se.scmv.morocco.ad.navigation.AdViewRoute
import se.scmv.morocco.ad.navigation.ListingRoute
import se.scmv.morocco.adstickybanner.network.Lead
import se.scmv.morocco.adstickybanner.network.LeadRequest
import se.scmv.morocco.adstickybanner.network.Tags
import se.scmv.morocco.adstickybanner.presentation.AdViewModel
import se.scmv.morocco.adstickybanner.presentation.BubbleOverlayAd
import se.scmv.morocco.designsystem.components.AvPrimaryButton
import se.scmv.morocco.designsystem.components.avTextFieldColors
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.Blue50
import se.scmv.morocco.designsystem.theme.Green700
import se.scmv.morocco.designsystem.theme.White
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.BookmarkedSearch
import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.domain.models.SearchSuggestion
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.ui.HomeOneTimeEvents.NotifySearchSuggestionSelected
import se.scmv.morocco.utils.URLUtils.openUrl

@EntryPoint
@InstallIn(SingletonComponent::class)
interface MessagingSubscriptionManagerEntryPoint {
    fun messagingSubscriptionManager(): MessagingSubscriptionManager
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun HomeScreen(
    account: Account,
    navigate: (Any) -> Unit,
    navigateToAuth: () -> Unit,
    navigateToAdInsert: () -> Unit,
    navigateToFilters: (String?) -> Unit,
    navigateToInfoScreen: () -> Unit,
    notifyCategoryChanged: (String, String?) -> Unit,
    notifySearchSuggestionSelected: (List<OrionBaseComponentValue>) -> Unit,
    notifyBookmarkedSearchSelected: (BookmarkedSearch) -> Unit,
    notifyCancelExtendedDeliveryClicked: () -> Unit,
    notifyCancelExtendedSearchClicked: () -> Unit,
    updateAppLanguage: () -> Unit,
    openContactSupport: () -> Unit,
    onUpdateAppClicked: () -> Unit,
    viewModel: HomeViewModel = hiltViewModel(),
    adViewModel: AdViewModel = hiltViewModel()
) {
    val appState = rememberAppState(account = account, homeNavController = rememberNavController())
    val scope = rememberCoroutineScope()
    val drawerState = rememberDrawerState(initialValue = DrawerValue.Closed)
    val context = LocalContext.current
    val messagingSubscriptionManager = remember {
        EntryPointAccessors.fromApplication(
            context.applicationContext,
            MessagingSubscriptionManagerEntryPoint::class.java
        ).messagingSubscriptionManager()
    }
    val unreadCount = messagingSubscriptionManager.unreadCount.collectAsState().value

    val currentTopLevelDestination = appState.currentTopLevelDestination

    LaunchedEffect(currentTopLevelDestination) {
        if (currentTopLevelDestination == TopLevelDestination.MESSAGING) {
            messagingSubscriptionManager.resetUnreadCount()
        }
    }

    LaunchedEffect(Unit) {
        messagingSubscriptionManager.initializeUnreadCount()
        messagingSubscriptionManager.startSubscription()
    }

    ModalNavigationDrawer(
        drawerContent = {
            ModalDrawerSheet {
                AvitoNavigationDrawer(
                    account = appState.account,
                    drawerState = drawerState,
                    onAdInsertClicked = navigateToAdInsert,
                    onLoginClicked = navigateToAuth,
                    onAccountClicked = { navigate(AccountEditRoute) },
                    onMessagingClicked = {
                        appState.navigateToTopLevelDestination(TopLevelDestination.MESSAGING)
                    },
                    onFavoritesClicked = {
                        appState.navigateToTopLevelDestination(TopLevelDestination.BOOKMARKS)
                    },
                    onStatsClicked = {
                        appState.navigateToTopLevelDestination(TopLevelDestination.STATS)
                    },
                    onLanguageClicked = updateAppLanguage,
                    onContactSupportClicked = openContactSupport,
                    onInformationClicked = navigateToInfoScreen,
                    onUpdateAppClicked = onUpdateAppClicked,
                )
            }
        },
        drawerState = drawerState
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize(),
        ) {
            // BubbleOverlayAd positioned in center right
            val notifAd by adViewModel.notifAdFlow.collectAsState()
            val isNotificationAdActive by adViewModel.isNotificationAdActive.collectAsState()
            Scaffold(
                bottomBar = {
                    AvitoBottomBar(
                        destinations = appState.topLevelDestinations.toPersistentList(),
                        currentDestination = appState.currentDestination,
                        onNavigateToDestination = appState::navigateToTopLevelDestination,
                        onNavigateToAdInsert = navigateToAdInsert,
                        unreadMessages = unreadCount
                    )
                },
            ) { paddingValues ->
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .consumeWindowInsets(paddingValues)
                ) {
                    val stickyBanner by adViewModel.imageSlideFlow.collectAsState()
                    val isStickyBannerActive by adViewModel.isImageSlideActive.collectAsState()
                    if (isStickyBannerActive && !isNotificationAdActive) {
                        AsyncImage(
                            model = stickyBanner,
                            contentDescription = "stickyBanner",
                            contentScale = ContentScale.FillWidth,
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(100.dp)
                                .padding(5.dp)
                                .clickable {
                                    adViewModel.onStickyBannerClick()
                                    adViewModel.recordClick { url ->
                                        openUrl(website = url, context = context)
                                    }
                                }
                        )
                    }
                    Box(
                        Modifier
                            .fillMaxSize()
                            .semantics { isTraversalGroup = true }
                    ) {
                        AvitoTopBar(
                            state = viewModel.viewState.collectAsStateWithLifecycle().value,
                            onSearchChanged = viewModel::onSearchChanged,
                            onMenuClick = { scope.launch { drawerState.open() } },
                            onFilterClicked = { navigateToFilters(null) },
                            onSuggestionClicked = viewModel::onSuggestionClicked,
                            onDeleteSuggestionClicked = viewModel::onDeleteSuggestionClicked
                        )
                        HomeScreenNavHost(
                            appState = appState,
                            navigate = navigate,
                            navigateToAuth = navigateToAuth,
                            navigateToFilters = navigateToFilters,
                            notifyCategoryChanged = { category, type ->
                                adViewModel.updateCategory(category, emptyList())
                                notifyCategoryChanged(category, type)
                            },
                            notifyBookmarkedSearchSelected = { bookmarkedSearch ->
                                appState.navigateToTopLevelDestination(TopLevelDestination.LISTING)
                                notifyBookmarkedSearchSelected(bookmarkedSearch)
                            },
                            notifyCancelExtendedDeliveryClicked = notifyCancelExtendedDeliveryClicked,
                            notifyCancelExtendedSearchClicked = notifyCancelExtendedSearchClicked,
                            onLoadMoreItems = { adViewModel.recordImpression() },
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(top = SearchBarDefaults.InputFieldHeight + MaterialTheme.dimens.default)
                        )
                    }
                }
            }

            if (isNotificationAdActive && notifAd != null) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.CenterEnd
                ) {
                    BubbleOverlayAd(
                        notifAd = notifAd!!,
                        onDismiss = {
                            adViewModel.hideNotificationAd()
                        },
                        onNotifAdClick = { url ->
                            adViewModel.recordClick {
                                navigate(WebViewRoute(title = null, url = url))
                            }
                        },
                        onFormSubmit = { campaign, name, phone, email ->
                            adViewModel.storeLeads(
                                LeadRequest(
                                    listOf(
                                        Lead(
                                            platform = context.getString(se.scmv.morocco.designsystem.R.string.generale_avito_word),
                                            campaign = campaign,
                                            tags = Tags(
                                                name = name,
                                                phone = phone,
                                                email = email,
                                                city = ""
                                            )
                                        )
                                    )
                                )
                            )
                        },
                        onActionClick = { url ->
                            adViewModel.onNotificationAdCtaClick()
                            adViewModel.recordCreativeClick()
                            navigate(WebViewRoute(title = null, url = url))
                        }
                    )
                }
            }
        }

    }
    LaunchedEffect(viewModel) {
        viewModel.oneTimeEvents.collectLatest {
            when (it) {
                is NotifySearchSuggestionSelected -> notifySearchSuggestionSelected(it.filtersValues)
            }
        }
    }
}

@Composable
private fun HomeScreenNavHost(
    appState: AvitoAppState,
    navigate: (Any) -> Unit,
    navigateToAuth: () -> Unit,
    navigateToFilters: (String?) -> Unit,
    notifyCategoryChanged: (String, String?) -> Unit,
    notifyBookmarkedSearchSelected: (BookmarkedSearch) -> Unit,
    notifyCancelExtendedDeliveryClicked: () -> Unit,
    notifyCancelExtendedSearchClicked: () -> Unit,
    onLoadMoreItems: (() -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    NavHost(
        navController = appState.homeNavController,
        startDestination = ListingRoute(),
        modifier = modifier
    ) {
        composable<ListingRoute>(
            deepLinks = listOf(
                // Listing deep linking with query parameters
                navDeepLink {
                    uriPattern = "${AppDeepLinks.LISTING}?matchExactlyParam1={matchExactlyParam1}&matchExactlyParam2={matchExactlyParam2}"
                },
                navDeepLink {
                    uriPattern = "${AppDeepLinks.LISTING}?matchExactlyParam1={matchExactlyParam1}"
                },
                navDeepLink {
                    uriPattern = AppDeepLinks.LISTING
                },
                navDeepLink {
                    uriPattern = "${WebDeepLinks.LISTING_HTTP}?matchExactlyParam1={matchExactlyParam1}&matchExactlyParam2={matchExactlyParam2}"
                },
                navDeepLink {
                    uriPattern = "${WebDeepLinks.LISTING_HTTP}?matchExactlyParam1={matchExactlyParam1}"
                },
                navDeepLink {
                    uriPattern = WebDeepLinks.LISTING_HTTP
                },
                navDeepLink {
                    uriPattern = "${WebDeepLinks.LISTING_HTTPS}?matchExactlyParam1={matchExactlyParam1}&matchExactlyParam2={matchExactlyParam2}"
                },
                navDeepLink {
                    uriPattern = "${WebDeepLinks.LISTING_HTTPS}?matchExactlyParam1={matchExactlyParam1}"
                },
                navDeepLink {
                    uriPattern = WebDeepLinks.LISTING_HTTPS
                },
            ),
            enterTransition = { fadeIn() },
            exitTransition = { fadeOut() }
        ) { backStackEntry ->
            AdsListingRoute(
                account = appState.account,
                navigateToFilters = navigateToFilters,
                notifyCategoryChanged = notifyCategoryChanged,
                newItemsLoaded = {
                    onLoadMoreItems
                },
                navigateToNewConstruction = {
                    navigate(WebViewRoute(title = null, url = it))
                },
                navigateToAdView = {
                    when (it) {
                        is ListingAd.Published -> it.listId
                        is ListingAd.Premium -> it.listId
                        else -> null
                    }?.let { adId ->
                        navigate(AdViewRoute(adId))
                    }
                },
                navigateToAuth = navigateToAuth,
                navigateToChat = { conversationId ->
                    navigate(ChatRoute(conversationId))
                },
                notifyCancelExtendedDeliveryClicked = notifyCancelExtendedDeliveryClicked,
                notifyCancelExtendedSearchClicked = notifyCancelExtendedSearchClicked,
            )
        }
        composable<AccountBookmarksRoute>(
            deepLinks = listOf(
                navDeepLink<AccountBookmarksRoute>(basePath = AppDeepLinks.BOOKMARKS),
            ),
            enterTransition = { fadeIn() },
            exitTransition = { fadeOut() }
        ) {
            val tab = it.toRoute<AccountBookmarksRoute>().tab
            AuthProtectedContent(
                account = appState.account,
                onLoginClicked = navigateToAuth
            ) { connectedAccount ->
                AccountBookmarksRoute(
                    tab = tab,
                    navigateToHome = {
                        appState.navigateToTopLevelDestination(TopLevelDestination.LISTING)
                    },
                    applySearch = notifyBookmarkedSearchSelected,
                    notifyAdRemovedFromFavorites = {},
                    navigateToAdView = { adId ->
                        navigate(AdViewRoute(adId))
                    },
                    navigateToAuthentication = navigateToAuth,
                    account = connectedAccount,
                    onSendMessage = { message ->
                        // This will be handled by the AdViewRepository in the future
                        // For now, we can show a success message or handle it differently
                    }
                )
            }
        }
        composable<AccountStatisticsRoute>(
            enterTransition = { fadeIn() },
            exitTransition = { fadeOut() }
        ) {
            AuthProtectedContent(
                account = appState.account,
                onLoginClicked = navigateToAuth
            ) { _ ->
                AccountStatisticsRoute()
            }
        }
        composable<MessagingRoute>(
            deepLinks = listOf(
                navDeepLink<MessagingRoute>(basePath = AppDeepLinks.MESSAGING),
                navDeepLink<MessagingRoute>(basePath = WebDeepLinks.MESSAGING_HTTP),
                navDeepLink<MessagingRoute>(basePath = WebDeepLinks.MESSAGING_HTTPS),
            ),
            enterTransition = { fadeIn() },
            exitTransition = { fadeOut() }
        ) {
            AuthProtectedContent(
                account = appState.account,
                onLoginClicked = navigateToAuth
            ) { _ ->
                ConversationListScreen(
                    onNavigateToChat = { conversation ->
                        navigate(ChatRoute(conversation.id))
                    },
                    onNavigateBack = { appState.homeNavController.navigateUp() },
                    onNavigateToAdsList = {
                        appState.navigateToTopLevelDestination(
                            TopLevelDestination.LISTING
                        )
                    }
                )
            }
        }

        composable<AccountMasterRoute>(
            enterTransition = { fadeIn() },
            exitTransition = { fadeOut() }
        ) {
            AuthProtectedContent(
                account = appState.account,
                onLoginClicked = navigateToAuth
            ) { connectedAccount ->
                AccountMasterRoute(
                    account = connectedAccount,
                    navigateToAccountEdit = { navigate(AccountEditRoute) },
                    navigateToAccountAds = { navigate(AccountAdsRoute()) },
                    navigateToAccountOrders = { navigate(AccountOrdersRoute) },
                    navigateToUpdatePassword = { email ->
                        navigate(UpdatePasswordRoute(email))
                    }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun BoxScope.AvitoTopBar(
    state: HomeViewState,
    onSearchChanged: (String) -> Unit,
    onMenuClick: () -> Unit,
    onFilterClicked: () -> Unit,
    onSuggestionClicked: (SearchSuggestion) -> Unit,
    onDeleteSuggestionClicked: (SearchSuggestion) -> Unit,
    modifier: Modifier = Modifier,
) {
    var expanded by remember { mutableStateOf(false) }
    SearchBar(
        modifier = modifier
            .align(Alignment.TopCenter)
            .semantics { traversalIndex = 0f },
        inputField = {
            SearchBarDefaults.InputField(
                modifier = Modifier.fillMaxWidth(),
                query = state.searchQuery,
                onQueryChange = onSearchChanged,
                onSearch = onSearchChanged,
                expanded = expanded,
                onExpandedChange = { expanded = it },
                placeholder = {
                    Text(
                        text = stringResource(R.string.common_search),
                        color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.5f),
                        style = MaterialTheme.typography.bodyMedium
                    )
                },
                leadingIcon = {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        if (state.isLoading) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(MaterialTheme.dimens.extraBig),
                                trackColor = MaterialTheme.colorScheme.background,
                            )
                        } else FilledIconButton(
                            modifier = Modifier.padding(top = MaterialTheme.dimens.small),
                            onClick = {
                                if (expanded) {
                                    expanded = false
                                } else {
                                    onMenuClick()
                                }
                            },
                            colors = IconButtonDefaults.filledIconButtonColors(
                                containerColor = MaterialTheme.colorScheme.outline,
                            )
                        ) {
                            Icon(
                                imageVector = if (expanded) {
                                    Icons.AutoMirrored.Filled.ArrowBack
                                } else Icons.Filled.Menu,
                                contentDescription = "Open/close navigation menu"
                            )
                        }
                        AnimatedVisibility(visible = expanded.not()) {
                            Image(
                                modifier = Modifier
                                    .padding(end = MaterialTheme.dimens.small)
                                    .size(30.dp),
                                painter = painterResource(R.drawable.avito_logo_no_padding),
                                contentDescription = null,
                                contentScale = ContentScale.Fit
                            )
                        }
                    }
                },
                trailingIcon = {
                    AnimatedVisibility(visible = expanded.not()) {
                        FilledIconButton(
                            modifier = Modifier.padding(top = MaterialTheme.dimens.small),
                            onClick = onFilterClicked
                        ) {
                            Icon(
                                painter = painterResource(R.drawable.ic_filter),
                                contentDescription = "Open/close navigation menu"
                            )
                        }
                    }
                },
                colors = avTextFieldColors,
            )
        },
        expanded = expanded,
        onExpandedChange = { expanded = it },
        shape = CircleShape,
        colors = SearchBarDefaults.colors(
            containerColor = avTextFieldColors.unfocusedContainerColor,
            dividerColor = MaterialTheme.colorScheme.outline,
        )
    ) {
        Column(
            modifier = Modifier
                .padding(vertical = MaterialTheme.dimens.large)
                .fillMaxSize(),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            when {
                state.searchSuggestions.isNotEmpty() -> SearchesSection(
                    modifier = Modifier.fillMaxWidth(),
                    title = R.string.suggestions,
                    searches = state.searchSuggestions,
                    onSuggestionClicked = {
                        expanded = false
                        onSuggestionClicked(it)
                    },
                    onDeleteSuggestionClicked = onDeleteSuggestionClicked
                )

                state.recentSearches.isNotEmpty() -> SearchesSection(
                    modifier = Modifier.fillMaxWidth(),
                    title = R.string.recent_searches,
                    searches = state.recentSearches,
                    onSuggestionClicked = {
                        expanded = false
                        onSuggestionClicked(it)
                    },
                    onDeleteSuggestionClicked = onDeleteSuggestionClicked
                )
            }
        }
    }
}

@Composable
private fun SearchesSection(
    modifier: Modifier = Modifier,
    title: Int,
    searches: PersistentList<SearchSuggestion>,
    onSuggestionClicked: (SearchSuggestion) -> Unit,
    onDeleteSuggestionClicked: (SearchSuggestion) -> Unit,
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
    ) {
        Text(
            text = stringResource(title),
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.padding(start = MaterialTheme.dimens.medium)
        )
        searches.forEach { suggestion ->
            Column {
                DropdownMenuItem(
                    text = { Text(suggestion.getBody(LocalContext.current)) },
                    onClick = { onSuggestionClicked(suggestion) },
                    leadingIcon = {
                        Icon(
                            modifier = Modifier.size(MaterialTheme.dimens.bigger),
                            painter = painterResource(
                                if (suggestion.isHistory) R.drawable.ic_recent_search
                                else R.drawable.ic_search_suggestion
                            ),
                            contentDescription = "Search suggestion icon"
                        )
                    },
                    trailingIcon = if (suggestion.isHistory) {
                        {
                            IconButton(onClick = { onDeleteSuggestionClicked(suggestion) }) {
                                Icon(
                                    modifier = Modifier.size(MaterialTheme.dimens.big),
                                    imageVector = Icons.Default.Clear,
                                    contentDescription = "Search suggestion icon"
                                )
                            }
                        }
                    } else null
                )
                HorizontalDivider()
            }
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
private fun AvitoTopBarPreview() {
    AvitoTheme {
        Box(
            Modifier
                .fillMaxSize()
                .semantics { isTraversalGroup = true }
        ) {
            AvitoTopBar(
                state = HomeViewState(searchQuery = "test"),
                onMenuClick = {},
                onSearchChanged = {},
                onFilterClicked = {},
                onSuggestionClicked = {},
                onDeleteSuggestionClicked = {}
            )
        }
    }
}

@Composable
private fun AvitoBottomBar(
    destinations: PersistentList<TopLevelDestination>,
    currentDestination: NavDestination?,
    onNavigateToDestination: (TopLevelDestination) -> Unit,
    onNavigateToAdInsert: () -> Unit,
    unreadMessages: Int,
    modifier: Modifier = Modifier
) {
    BottomNavigationBar(
        modifier = modifier,
        containerColor = MaterialTheme.colorScheme.background,
        onActionClick = onNavigateToAdInsert,
        windowInsets = WindowInsets(0)
    ) {
        destinations.forEachIndexed { index, destination ->
            val selected = currentDestination.isRouteInHierarchy(destination.route)
            NavigationBarItem(
                selected = selected,
                label = {
                    Text(
                        text = stringResource(destination.iconTextId),
                        maxLines = 1,
                    )
                },
                icon = {
                    Box {
                        Icon(
                            modifier = Modifier.size(MaterialTheme.dimens.bigger),
                            painter = painterResource(destination.getIcon(selected)),
                            contentDescription = null
                        )
                        if (destination == TopLevelDestination.MESSAGING && unreadMessages > 0) {
                            Badge(
                                modifier = Modifier.align(Alignment.TopEnd),
                                containerColor = Color.Red
                            ) {
                                Text(unreadMessages.toString())
                            }
                        }
                    }
                },
                onClick = { onNavigateToDestination(destination) },
                colors = NavigationBarItemDefaults.colors(
                    selectedIconColor = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.8f),
                    selectedTextColor = MaterialTheme.colorScheme.onBackground,
                    indicatorColor = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.2f),
                    unselectedIconColor = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.8f),
                    unselectedTextColor = MaterialTheme.colorScheme.onBackground,
                )
            )
            if (index == 1) {
                NavigationBarItem(selected = false, label = {}, icon = {}, onClick = {})
            }
        }
    }
}

@Composable
private fun BottomNavigationBar(
    modifier: Modifier = Modifier,
    containerColor: Color = NavigationBarDefaults.containerColor,
    contentColor: Color = MaterialTheme.colorScheme.contentColorFor(containerColor),
    tonalElevation: Dp = NavigationBarDefaults.Elevation,
    windowInsets: WindowInsets = NavigationBarDefaults.windowInsets,
    onActionClick: () -> Unit,
    content: @Composable RowScope.() -> Unit,
) {
    Surface(
        color = containerColor,
        contentColor = contentColor,
        tonalElevation = tonalElevation,
        modifier = modifier
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .windowInsetsPadding(windowInsets)
                .defaultMinSize(minHeight = 80.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectableGroup(),
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
                verticalAlignment = Alignment.CenterVertically,
                content = content
            )
            Column(
                modifier = Modifier
                    .align(Alignment.Center)
                    .padding(bottom = MaterialTheme.dimens.large),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                FilledIconButton(
                    modifier = Modifier.size(60.dp),
                    onClick = onActionClick,
                    colors = IconButtonDefaults.filledIconButtonColors(
                        containerColor = Green700,
                        contentColor = White
                    )
                ) {
                    Icon(
                        painter = painterResource(R.drawable.ic_camera_line),
                        contentDescription = "Open ad insert button icon"
                    )
                }
                Spacer(Modifier.height(MaterialTheme.dimens.small))
                Text(
                    text = stringResource(R.string.navigation_sell),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onBackground
                )
            }
        }
    }
}

@Composable
private fun AvitoNavigationDrawer(
    account: Account,
    drawerState: DrawerState,
    onAdInsertClicked: () -> Unit,
    onLoginClicked: () -> Unit,
    onAccountClicked: () -> Unit,
    onMessagingClicked: () -> Unit,
    onFavoritesClicked: () -> Unit,
    onStatsClicked: () -> Unit,
    onLanguageClicked: () -> Unit,
    onContactSupportClicked: () -> Unit,
    onInformationClicked: () -> Unit,
    onUpdateAppClicked: () -> Unit,
    modifier: Modifier = Modifier
) {
    val scope = rememberCoroutineScope()
    Column(modifier = modifier.fillMaxWidth()) {
        Column(
            modifier = Modifier
                .background(Blue50)
                .padding(MaterialTheme.dimens.large)
                .fillMaxWidth(),
        ) {
            Image(
                modifier = Modifier.fillMaxWidth(),
                painter = painterResource(R.drawable.side_menu_visual),
                contentDescription = "Drawer menu illustration",
                contentScale = ContentScale.Crop
            )
            Spacer(modifier = Modifier.height(MaterialTheme.dimens.default))
            if (account.isLogged().not()) {
                DrawerNotConnectedWelcome()
            }
            if (account is Account.Connected) {
                DrawerConnectedUserInfo(
                    imageUrl = (account as? Account.Connected.Shop)?.store?.logoUrl,
                    name = account.connectedContact().name,
                    creationDate = account.connectedContact().creationDate
                )
            }
        }
        Column(
            modifier = Modifier
                .background(MaterialTheme.colorScheme.background)
                .padding(MaterialTheme.dimens.large)
                .fillMaxWidth()
                .weight(1f),
        ) {
            AvPrimaryButton(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(R.string.menu_ad_insert),
                leadingIcon = {
                    Icon(
                        painter = painterResource(R.drawable.ic_camera_line),
                        contentDescription = "Open ad insert button icon"
                    )
                },
                colors = ButtonDefaults.buttonColors(containerColor = Green700),
                onClick = {
                    scope.launch { drawerState.close() }.invokeOnCompletion {
                        onAdInsertClicked()
                    }
                }
            )
            Spacer(modifier = Modifier.height(MaterialTheme.dimens.medium))
            DrawerItems(
                items = listOf(
                    DrawerItem(
                        label = if (account.isLogged()) {
                            R.string.my_info
                        } else R.string.common_login,
                        icon = R.drawable.ic_outlined_user,
                        onClick = if (account.isLogged()) onAccountClicked else onLoginClicked
                    ),
                    DrawerItem(
                        label = R.string.menu_my_messages,
                        icon = R.drawable.ic_chat_white,
                        onClick = onMessagingClicked
                    ),
                    DrawerItem(
                        label = if (account.isShop()) {
                            R.string.menu_my_statistics
                        } else R.string.menu_my_favorites,
                        icon = if (account.isShop()) {
                            R.drawable.ic_outlined_statistics
                        } else R.drawable.ic_outlined_heart,
                        onClick = if (account.isShop()) onStatsClicked else onFavoritesClicked
                    ),
                    DrawerItem(
                        label = R.string.menu_language,
                        icon = R.drawable.ic_translate,
                        onClick = onLanguageClicked
                    ),
                    DrawerItem(
                        label = R.string.menu_contact_us,
                        icon = R.drawable.ic_outlined_phone,
                        onClick = onContactSupportClicked
                    ),
                    DrawerItem(
                        label = R.string.menu_information,
                        icon = R.drawable.ic_outlined_info,
                        onClick = onInformationClicked
                    ),
                    DrawerItem(
                        label = R.string.inn_app_update_side_menu_item,
                        icon = R.drawable.ic_update_app_fill,
                        onClick = onUpdateAppClicked
                    ),
                ),
                drawerState = drawerState
            )
        }
    }
}

@Composable
private fun DrawerNotConnectedWelcome() {
    Text(
        text = stringResource(R.string.drawer_message_title),
        style = MaterialTheme.typography.titleLarge
    )
    Spacer(modifier = Modifier.height(MaterialTheme.dimens.small))
    Text(
        text = stringResource(R.string.drawer_message_body),
        style = MaterialTheme.typography.bodyMedium
    )
}

@Composable
private fun DrawerConnectedUserInfo(
    imageUrl: String?,
    name: String,
    creationDate: String,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight(),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Card(
            shape = CircleShape,
            elevation = CardDefaults.cardElevation(MaterialTheme.dimens.tiny)
        ) {
            if (imageUrl != null) AsyncImage(
                modifier = Modifier.size(MaterialTheme.dimens.extraExtraBig),
                model = imageUrl,
                contentDescription = "Store logo",
                contentScale = ContentScale.Crop
            ) else Image(
                modifier = Modifier.size(MaterialTheme.dimens.extraExtraBig),
                painter = painterResource(R.drawable.ic_avatar_1),
                contentDescription = "User image or placeholder",
                contentScale = ContentScale.Crop
            )
        }
        Column(
            modifier = Modifier.height(IntrinsicSize.Max),
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = name,
                style = MaterialTheme.typography.titleLarge
            )
            creationDate
                .split("-")
                .toTypedArray()
                .firstOrNull()
                ?.let {
                    Text(
                        text = stringResource(R.string.drawer_membre_depuis, it),
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
        }
    }
}

@Stable
private data class DrawerItem(
    val label: Int,
    val icon: Int,
    val onClick: () -> Unit
)

@Composable
private fun DrawerItems(
    items: List<DrawerItem>,
    drawerState: DrawerState,
    modifier: Modifier = Modifier
) {
    val scope = rememberCoroutineScope()
    Column(modifier) {
        items.forEach { item ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable(
                        onClick = {
                            scope
                                .launch { drawerState.close() }
                                .invokeOnCompletion {
                                    item.onClick()
                                }
                        }
                    )
                    .padding(
                        horizontal = MaterialTheme.dimens.default,
                        vertical = MaterialTheme.dimens.small
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier
                        .clip(CircleShape)
                        .background(MaterialTheme.colorScheme.surfaceContainerHighest)
                        .padding(MaterialTheme.dimens.medium),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        modifier = Modifier.size(MaterialTheme.dimens.big),
                        painter = painterResource(item.icon),
                        contentDescription = "${stringResource(item.label)} icon"
                    )
                }
                Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))
                Text(
                    text = stringResource(item.label),
                    style = MaterialTheme.typography.bodyMedium
                )
                Spacer(modifier = Modifier.weight(1f))
                Icon(
                    imageVector = Icons.AutoMirrored.Default.KeyboardArrowRight,
                    contentDescription = "Arrow icon"
                )
            }
        }
    }
}

@Preview(showSystemUi = true, showBackground = true)
@Composable
private fun AvitoNavigationDrawerPreview() {
    AvitoTheme {
        AvitoNavigationDrawer(
            drawerState = rememberDrawerState(initialValue = DrawerValue.Closed),
            account = Account.NotConnected,
            onAdInsertClicked = {},
            onLoginClicked = {},
            onAccountClicked = {},
            onMessagingClicked = {},
            onFavoritesClicked = {},
            onStatsClicked = {},
            onLanguageClicked = {},
            onContactSupportClicked = {},
            onInformationClicked = {},
            onUpdateAppClicked = {},
        )
    }
}