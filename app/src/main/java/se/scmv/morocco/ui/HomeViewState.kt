package se.scmv.morocco.ui

import android.content.Context
import androidx.compose.runtime.Stable
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.persistentListOf
import se.scmv.morocco.R
import se.scmv.morocco.domain.models.SearchSuggestion
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue

@Stable
data class HomeViewState(
    val searchQuery: String = "",
    val searchSuggestions: PersistentList<SearchSuggestion> = persistentListOf(),
    val recentSearches: PersistentList<SearchSuggestion> = persistentListOf(),
    val isLoading: Boolean = false,
)

sealed interface HomeOneTimeEvents {
    data class NotifySearchSuggestionSelected(val filtersValues: List<OrionBaseComponentValue>) :
        HomeOneTimeEvents
}

fun SearchSuggestion.getBody(context: Context): String {
    val builder = StringBuilder()
    if (!keyword.isNullOrEmpty()) {
        builder.append("$keyword ")
    }
    if (!brandName.isNullOrEmpty()) {
        builder.append("$brandName ")
    }
    if (!modelName.isNullOrEmpty()) {
        builder.append(" (${modelName}) ")
    }
    if (!category?.name.isNullOrEmpty() && !keyword.isNullOrEmpty()) {
        builder.append("${category?.name} ")
    } else if (!category?.name.isNullOrEmpty() && (!keyword.isNullOrEmpty() || !brandName.isNullOrEmpty() || !modelName.isNullOrEmpty())) {
        builder.append("${context.getString(R.string.dans)} ${category?.name} ")
    } else if (!category?.name.isNullOrEmpty()) {
        builder.append("${context.getString(R.string.dans)} ${category?.name} ")
    }
    if (!adType?.name.isNullOrEmpty()) {
        builder.append(" ${adType?.name} ")
    }
    if (!city?.name.isNullOrEmpty()) {
        builder.append("${context.getString(R.string.a)} ${city?.name} ")
    }
    return builder.toString()
}