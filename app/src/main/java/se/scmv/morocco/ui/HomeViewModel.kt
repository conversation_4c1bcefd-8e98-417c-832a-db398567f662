package se.scmv.morocco.ui

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.SearchSuggestion
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.domain.repositories.AdViewRepository
import se.scmv.morocco.domain.repositories.ConfigRepository
import javax.inject.Inject

@HiltViewModel
class HomeViewModel @Inject constructor(
    private val configRepository: ConfigRepository,
    private val accountRepository: AccountRepository,
    private val adViewRepository: AdViewRepository
) : ViewModel() {
    private val searchQueryFlow = MutableStateFlow("")

    private val _viewState = MutableStateFlow(HomeViewState())
    val viewState = _viewState.asStateFlow()

    private val _oneTimeEvents = MutableSharedFlow<HomeOneTimeEvents>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    private var lastRequestTime: Long = 0 // To store the last request timestamp

    init {
        viewModelScope.launch {
            accountRepository.getBookmarkedSearchSuggestions().collectLatest { recentSearches ->
                _viewState.update { it.copy(recentSearches = recentSearches.toPersistentList()) }
            }
        }
        viewModelScope.launch {
            searchQueryFlow.collectLatest { query ->
                if (query.isNotBlank()) {
                    val currentTime = System.currentTimeMillis()
                    // Check if it's been more than 1.5 second since the last request
                    val timeDifference = currentTime - lastRequestTime
                    // If 1 second has passed
                    if (timeDifference >= 1_500) {
                        lastRequestTime = currentTime  // Update last request time
                        fetchSuggestions(query)
                    } else {
                        // If less than 1 second passed, schedule a request after the remaining time
                        delay(1_500 - timeDifference)
                        fetchSuggestions(query)
                    }
                }
            }
        }
    }

    fun onSearchChanged(query: String) {
        if (query.isBlank()) {
            clearSearch()
        } else {
            _viewState.update { it.copy(searchQuery = query) }
            searchQueryFlow.update { query }
        }
    }

    fun onSuggestionClicked(suggestion: SearchSuggestion) {
        viewModelScope.launch {
            clearSearch()
            val filters = configRepository.buildFiltersValuesFor(suggestion)
            _oneTimeEvents.emit(HomeOneTimeEvents.NotifySearchSuggestionSelected(filters))
            if (suggestion.isHistory.not()) {
                accountRepository.bookmarkSearchSuggestion(suggestion)
            }
        }
    }

    fun onDeleteSuggestionClicked(suggestion: SearchSuggestion) {
        viewModelScope.launch {
            accountRepository.unbookmarkSearchSuggestion(suggestion)
        }
    }

    private suspend fun fetchSuggestions(query: String) {
        _viewState.update { it.copy(isLoading = true) }
        val result = configRepository.getSearchSuggestions(query)
        when (result) {
            is Resource.Success -> _viewState.update {
                it.copy(searchSuggestions = result.data.toPersistentList(), isLoading = false)
            }

            is Resource.Failure -> Unit
        }
    }

    fun clearSearch() {
        _viewState.update {
            it.copy(
                searchQuery = "",
                searchSuggestions = persistentListOf(),
                isLoading = false
            )
        }
    }
}