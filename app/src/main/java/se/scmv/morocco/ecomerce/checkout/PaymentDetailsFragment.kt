package se.scmv.morocco.ecomerce.checkout

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import se.scmv.morocco.activities.BaseActivity
import se.scmv.morocco.databinding.PaymentDetailsFragmentBinding
import se.scmv.morocco.fragments.BaseFragment
import se.scmv.morocco.login.models.Account
import se.scmv.morocco.utils.Constants
import se.scmv.morocco.utils.Utils

class PaymentDetailsFragment : BaseFragment() {

    companion object {
        fun newInstance(extras: Bundle): PaymentDetailsFragment {
            return PaymentDetailsFragment().apply {
                arguments = extras
            }
        }
    }

    private var _binding: PaymentDetailsFragmentBinding? = null
    val binding: PaymentDetailsFragmentBinding
        get() = _binding!!

    var adId: String? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = PaymentDetailsFragmentBinding.inflate(inflater, container, false)
        return _binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        adId = arguments?.getString(Constants.VAS_AD_ID)

        setAccountDetails(view)
        (activity as BaseActivity).dismissLoading()
    }

    override fun onOffline() {
        TODO("Not yet implemented")
    }

    private fun setAccountDetails(view: View) {
        with(binding) {
            val account = Account.getCurrentAccount(context)
            if (account != null) {
                phoneField.setText(account.phone ?: "")
                accountEmailField.setText(account.email ?: "")
                fullNameField.setText(account.name ?: "")
            }

            Utils.getStringPreference(
                view.context,
                Utils.PREF_DELIVERY_ADDRESS
            )?.let {
                deliveryAddressField.setText(it)
            }
        }
    }
    fun validateFields(): Boolean {
        return with(binding) {
            if (deliveryAddressField.validate()) {
                Utils.savePreference(
                    context,
                    Utils.PREF_DELIVERY_ADDRESS,
                    deliveryAddressField.getValue().editTextValue
                )
            }
            phoneField.validate() && accountEmailField.validate() && deliveryAddressField.validate() && fullNameField.validate()
        }
    }
}