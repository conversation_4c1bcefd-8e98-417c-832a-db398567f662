package se.scmv.morocco.ecomerce.checkout

import EcommercePaymentMethodsAdapter
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import se.scmv.morocco.R
import se.scmv.morocco.databinding.PaymentSummaryFragmentBinding
import se.scmv.morocco.fragments.BaseFragment
import se.scmv.morocco.type.ECommercePaymentMethod
import se.scmv.morocco.utils.Constants

class PaymentSummaryFragment : BaseFragment() {

    companion object {
        fun newInstance(extras: Bundle): PaymentSummaryFragment {
            return PaymentSummaryFragment().apply {
                arguments = extras
            }
        }
    }

    private var _binding: PaymentSummaryFragmentBinding? = null
    private val binding: PaymentSummaryFragmentBinding
        get() = _binding!!

    var adId: String? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        _binding = PaymentSummaryFragmentBinding.inflate(inflater, container, false)
        return _binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        adId = arguments?.getString(Constants.VAS_AD_ID)
        with(binding) {
            paymentMethods.layoutManager = LinearLayoutManager(context);
            paymentMethods.addItemDecoration(
                EcommercePaymentMethodsAdapter.MarginItemDecoration(
                    resources.getDimensionPixelSize(R.dimen.margin_normal)
                )
            )
            paymentMethods.adapter = (activity as PaymentDetailsActivity).paymentMethodsList?.let {
                EcommercePaymentMethodsAdapter(requireContext(), it)
            }

            summaryName.text = (activity as PaymentDetailsActivity).name
            summaryAddress.text = (activity as PaymentDetailsActivity).address
            summaryPhone.text = (activity as PaymentDetailsActivity).phone
            summaryMail.text = (activity as PaymentDetailsActivity).email
        }
    }

    override fun onOffline() {}


    fun getChosenPaymentMethod(): ECommercePaymentMethod? {
        return (binding.paymentMethods.adapter as EcommercePaymentMethodsAdapter).chosenPaymentMethod
    }
}