package se.scmv.morocco.ecomerce.checkout

import android.content.Context
import android.content.res.ColorStateList
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RadioButton
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.bumptech.glide.Glide
import se.scmv.morocco.R
import se.scmv.morocco.type.ECommercePaymentMethod


class NewPaymentMethodAdapter(
        private val mPaymentMethods: List<ECommercePaymentMethod>,
        private val mContext: Context,
        resource: Int
) : ArrayAdapter<ECommercePaymentMethod?>(mContext, resource, mPaymentMethods) {
        var mSelectedItem = -1
        private var chosenPaymentMethod: ECommercePaymentMethod? = null


        override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
                var convertViewx: View?
                convertViewx = convertView
                if (convertViewx == null) {
                        convertViewx = LayoutInflater.from(context)
                                .inflate(R.layout.item_ecommerce_payment_method, parent, false);
                }
                val currentPaymentMethod = mPaymentMethods[position]
                val radioButton =
                        convertView?.findViewById<RadioButton>(R.id.vas_payment_method_chosen)
                val container =
                        convertView?.findViewById<LinearLayout>(R.id.payment_method_container)
                container?.setOnClickListener { radioButton?.performClick() }

                when (currentPaymentMethod) {
                        ECommercePaymentMethod.CC -> {
                                val paymentMethodImage =
                                        convertView?.findViewById<ImageView>(R.id.vas_payment_method_image)
                                if (paymentMethodImage != null) {
                                        Glide.with(mContext)
                                                .load(
                                                        ContextCompat.getDrawable(
                                                                mContext,
                                                                R.drawable.ic_ecommerce_card
                                                        )
                                                )
                                                .into(paymentMethodImage)
                                }
                                val paymentMethodTitle =
                                        convertView?.findViewById<TextView>(R.id.vas_payment_method_name)
                                val paymentMethodsubTitle =
                                        convertView?.findViewById<TextView>(R.id.vas_payment_method_subname)
                                paymentMethodsubTitle?.text = "Payer avec Mastercard ou visa"
                                paymentMethodTitle?.text =
                                        context.getString(R.string.webview_title_credit_card_payment)

                        }
                        ECommercePaymentMethod.COD -> {
                                val paymentMethodImage =
                                        convertView?.findViewById<ImageView>(R.id.vas_payment_method_image)
                                if (paymentMethodImage != null) {
                                        Glide.with(mContext)
                                                .load(
                                                        ContextCompat.getDrawable(
                                                                mContext,
                                                                R.drawable.ic_ecommerce_cash
                                                        )
                                                )
                                                .into(paymentMethodImage)
                                }
                                val paymentMethodTitle =
                                        convertView?.findViewById<TextView>(R.id.vas_payment_method_name)
                                val paymentMethodsubTitle =
                                        convertView?.findViewById<TextView>(R.id.vas_payment_method_subname)
                                paymentMethodsubTitle?.text = "Payer en espèce à la livraison"
                                paymentMethodTitle?.text =
                                        context.getString(R.string.cash_payment)

                        }
                        else -> {}
                }


                return convertViewx!!
        }

        private fun selectVisual(radioButton: RadioButton, container: LinearLayout) {
                val colorStateList = ColorStateList(
                        arrayOf(intArrayOf(android.R.attr.state_enabled)),
                        intArrayOf(ContextCompat.getColor(context, R.color.orionBlue))
                )
                //radioButton.supportButtonTintList = colorStateList
                container.background =
                        ContextCompat.getDrawable(context, R.drawable.payment_method_selected_bg)
        }

        private fun unSelectVisual(radioButton: RadioButton, container: LinearLayout) {
                val colorStateList = ColorStateList(
                        arrayOf(intArrayOf(android.R.attr.state_enabled)),
                        intArrayOf(ContextCompat.getColor(context, R.color.orionGrey))
                )
                //radioButton.supportButtonTintList = colorStateList
                container.background =
                        ContextCompat.getDrawable(context, R.drawable.payment_method_bg)

        }

}