package se.scmv.morocco.widgets

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.viewpager.widget.ViewPager

/**
 * Created by <PERSON><PERSON> on 14/12/15.
 */
class NoSwipePager : ViewPager {
        private var swipeEnabled = false

        constructor(context: Context?) : super(context!!)
        constructor(context: Context?, attrs: AttributeSet?) : super(context!!, attrs)

        override fun onTouchEvent(event: MotionEvent): Bo<PERSON>an {
                return if (swipeEnabled) {
                        super.onTouchEvent(event)
                } else false
        }

        override fun onInterceptTouchEvent(event: MotionEvent): Boolean {
                return if (swipeEnabled) {
                        super.onInterceptTouchEvent(event)
                } else false
        }

        fun setPagingEnabled(enabled: Boolean) {
                swipeEnabled = enabled
        }
}