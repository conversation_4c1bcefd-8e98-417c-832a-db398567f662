package se.scmv.morocco.widgets

import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Color
import android.graphics.Typeface
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import se.scmv.morocco.R
import se.scmv.morocco.utils.NightModeUtils.Companion.isNightMode

class BottomNavigationBar(private val mContext: Context, attrs: AttributeSet?) :
        FrameLayout(mContext, attrs), View.OnClickListener {
        var mNavigationCallback: OnBottomNavigationItemSelected? = null
        private var itemNavigationHome: TextView? = null
        private var itemNavigationFavoritesStats: TextView? = null
        private var itemNavigationMessage: TextView? = null
        private var itemNavigationProfile: TextView? = null
        private var notificationBadge: TextView? = null
        private var messagingBadge: TextView? = null
        private var itemNavigationSell: View? = null
        private var homeBottomView: View? = null
        private var profileBottomView: View? = null
        private var chatBottomView: View? = null
        private var notificationBottomView: View? = null

        /**
         * Used to get the raw bottom navigation view excluding the center button
         *
         * @return View
         */
        var rawView: View? = null
                private set

        fun getTabAtPosition(position: Int): View? {
                when (position) {
                        NAVIGATION_POSITION_HOME -> return itemNavigationHome
                        NAVIGATION_POSITION_FAVORITES_STATS -> return itemNavigationFavoritesStats
                        NAVIGATION_POSITION_AD_INSERT -> return itemNavigationSell
                        NAVIGATION_POSITION_MESSAGING -> return itemNavigationMessage
                        NAVIGATION_POSITION_PROFILE -> return itemNavigationProfile
                }
                return null
        }

        private fun initViews() {
                val view =
                        LayoutInflater.from(mContext)
                                .inflate(R.layout.view_bottom_navigation, this, false)
                rawView = view.findViewById(R.id.rawView)
                itemNavigationSell = view.findViewById(R.id.itemNavigationSell)
                homeBottomView = view.findViewById(R.id.homeBottomView)
                profileBottomView = view.findViewById(R.id.profileBottomView)
                chatBottomView = view.findViewById(R.id.chatBottomView)
                notificationBottomView = view.findViewById(R.id.notificationBottomView)
                notificationBadge = view.findViewById(R.id.badgeNotification)
                messagingBadge = view.findViewById(R.id.badgeMessaging)
                itemNavigationHome = view.findViewById(R.id.itemNavigationHome)
                itemNavigationFavoritesStats = view.findViewById(R.id.itemNavigationFavorites)
                itemNavigationMessage = view.findViewById(R.id.itemNavigationMessage)
                itemNavigationProfile = view.findViewById(R.id.itemNavigationProfile)
                itemNavigationHome?.isSelected = true
                itemNavigationHome?.typeface = Typeface.DEFAULT_BOLD
                itemNavigationHome?.setOnClickListener(this)
                itemNavigationFavoritesStats?.setOnClickListener(this)
                itemNavigationSell?.setOnClickListener(this)
                itemNavigationMessage?.setOnClickListener(this)
                itemNavigationProfile?.setOnClickListener(this)
                setNightModeTintList()
                this.addView(view)
        }

        fun setShop() {
                itemNavigationFavoritesStats?.text = context.getString(R.string.statistiques)
                val img = ContextCompat.getDrawable(context, R.drawable.ic_statistics)
                itemNavigationFavoritesStats?.setCompoundDrawablesWithIntrinsicBounds(
                        null,
                        img,
                        null,
                        null
                )
                setNightModeTintList()
        }

        fun setPrivate() {
                itemNavigationFavoritesStats?.text = context.getString(R.string.tab_favorites)
                itemNavigationFavoritesStats?.setCompoundDrawablesWithIntrinsicBounds(
                        null,
                        ContextCompat.getDrawable(context, R.drawable.ic_menu_favorite),
                        null,
                        null
                )
                setNightModeTintList()

        }

        fun setNightModeTintList() {
                val states = arrayOf(
                        intArrayOf(android.R.attr.state_enabled),
                        intArrayOf(-android.R.attr.state_enabled),
                        intArrayOf(-android.R.attr.state_checked),
                        intArrayOf(android.R.attr.state_pressed)
                )
                if (isNightMode(context)) {
                        val white = context.resources.getColor(R.color.white)
                        val lightGray = context.resources.getColor(R.color.colorLightGray)
                        val colors = intArrayOf(
                                lightGray,
                                Color.BLACK,
                                Color.BLACK,
                                Color.BLACK
                        )
                        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                                itemNavigationHome?.compoundDrawableTintList =
                                        ColorStateList(states, colors)
                                itemNavigationMessage?.compoundDrawableTintList =
                                        ColorStateList(states, colors)
                                itemNavigationProfile?.compoundDrawableTintList =
                                        ColorStateList(states, colors)
                                itemNavigationFavoritesStats?.compoundDrawableTintList =
                                        ColorStateList(states, colors)
                        }

                }
        }

        fun setNavigationBarListener(navigationBarListener: OnBottomNavigationItemSelected?) {
                mNavigationCallback = navigationBarListener
        }

        fun setNotificationCount(count: Int) {
                notificationBadge?.visibility = if (count > 0) VISIBLE else GONE
                notificationBadge?.text =
                        if (count > 9) MAX_COUNTER_PLACEHOLDER else count.toString()
        }

        fun setMessagingCount(count: Long) {
                messagingBadge?.visibility = if (count > 0) VISIBLE else GONE
        }

        /**
         * Used to handle the clicks on the tab items
         */
        override fun onClick(view: View) {
                when (view.id) {
                        R.id.itemNavigationHome -> updateSelection(NAVIGATION_POSITION_HOME)
                        R.id.itemNavigationFavorites -> updateSelection(
                                NAVIGATION_POSITION_FAVORITES_STATS
                        )
                        R.id.itemNavigationSell -> mNavigationCallback?.onBottomNavigationItemSelected(
                                NAVIGATION_POSITION_AD_INSERT
                        )
                        R.id.itemNavigationMessage -> { mNavigationCallback?.onBottomNavigationItemSelected(NAVIGATION_POSITION_MESSAGING)
                        }
                        R.id.itemNavigationProfile -> updateSelection(NAVIGATION_POSITION_PROFILE)
                }
        }

        /**
         * Used to update the tab views when selected or unselected
         */
        fun updateSelection(position: Int) {
                itemNavigationHome?.isSelected = position == NAVIGATION_POSITION_HOME
                itemNavigationFavoritesStats?.isSelected =
                        position == NAVIGATION_POSITION_FAVORITES_STATS
                itemNavigationMessage?.isSelected = position == NAVIGATION_POSITION_MESSAGING
                itemNavigationMessage?.isSelected = position == NAVIGATION_POSITION_MESSAGING
                itemNavigationProfile?.isSelected = position == NAVIGATION_POSITION_PROFILE
                homeBottomView?.visibility =
                        if (position == NAVIGATION_POSITION_HOME) VISIBLE else GONE
                profileBottomView?.visibility =
                        if (position == NAVIGATION_POSITION_FAVORITES_STATS) VISIBLE else GONE
                chatBottomView?.visibility =
                        if (position == NAVIGATION_POSITION_MESSAGING) VISIBLE else GONE
                notificationBottomView?.visibility =
                        if (position == NAVIGATION_POSITION_PROFILE) VISIBLE else GONE
                mNavigationCallback?.onBottomNavigationItemSelected(position)
        }

        interface OnBottomNavigationItemSelected {
                fun onBottomNavigationItemSelected(position: Int)
        }

        /**
         * Setup system navigation bar handling for Android 15+ compatibility
         */
        private fun setupSystemNavigationBarHandling() {
                // Handle window insets for proper positioning above system navigation bar
                ViewCompat.setOnApplyWindowInsetsListener(this) { _, windowInsets ->
                        val insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())
                        
                        // Apply bottom margin to account for system navigation bar
                        if (insets.bottom > 0) {
                                (layoutParams as? FrameLayout.LayoutParams)?.apply {
                                        bottomMargin = insets.bottom
                                }
                                requestLayout()
                        }
                        
                        WindowInsetsCompat.CONSUMED
                }
                
                // Ensure this view is always on top of system navigation bar
                elevation = 1000f
                translationZ = 1000f
        }

        companion object {
                const val NAVIGATION_POSITION_HOME = 0
                const val NAVIGATION_POSITION_FAVORITES_STATS = 1
                const val NAVIGATION_POSITION_AD_INSERT = -1
                const val NAVIGATION_POSITION_MESSAGING = 2
                const val NAVIGATION_POSITION_PROFILE = 3
                private const val MAX_COUNTER_PLACEHOLDER = "9+"
        }

        init {
                initViews()
                setupSystemNavigationBarHandling()
        }
}