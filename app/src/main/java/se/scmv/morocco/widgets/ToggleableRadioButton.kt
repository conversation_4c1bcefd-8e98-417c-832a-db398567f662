package se.scmv.morocco.widgets

import android.content.Context
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatRadioButton

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 6/6/17.
 */
class ToggleableRadioButton : AppCompatRadioButton {
        constructor(context: Context?) : super(context)
        constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs)
        constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(
                context,
                attrs,
                defStyleAttr
        )

        override fun toggle() {
                isChecked = !isChecked
        }
}