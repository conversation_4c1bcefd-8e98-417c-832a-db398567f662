package se.scmv.morocco.widgets

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import se.scmv.morocco.R
import java.util.Random

/**
 * Created by amine on 10/08/15.
 */
class SideMenuEntry : RelativeLayout, View.OnClickListener {
        private var menuTitle: TextView? = null
        private var menuIcon: ImageView? = null
        var onItemClickListener: OnItemClickListener? = null

        constructor(context: Context?) : super(context)
        constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
                menuTitle = TextView(context)
                menuIcon = ImageView(context)
                val rand = Random()
                val iconId = rand.nextInt(0x457689) + 1
                val titleId = rand.nextInt(0x457689) + 1
                menuIcon?.id = iconId
                menuTitle?.id = titleId
                val menuTitleParams = LayoutParams(
                        ViewGroup.LayoutParams.WRAP_CONTENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                )
                val menuIconParams = LayoutParams(
                        ViewGroup.LayoutParams.WRAP_CONTENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                )
                menuIcon?.layoutParams = menuIconParams
                menuTitle?.layoutParams = menuTitleParams
                menuIconParams.addRule(CENTER_VERTICAL)
                menuTitleParams.addRule(CENTER_VERTICAL)
                menuTitleParams.addRule(RIGHT_OF, iconId)
                val a = context.theme.obtainStyledAttributes(
                        attrs,
                        R.styleable.SideMenuEntry,
                        0, 0
                )
                val iconMarginLeft =
                        a.getDimension(R.styleable.SideMenuEntry_iconLeftMargin, 0f).toInt()
                val titleMarginLeft =
                        a.getDimension(R.styleable.SideMenuEntry_titleLeftMargin, 0f).toInt()
                menuIconParams.setMargins(iconMarginLeft, 0, 0, 0)
                menuTitleParams.setMargins(titleMarginLeft, 0, 0, 0)
                val titleColor = a.getColor(R.styleable.SideMenuEntry_titleColor, Color.BLACK)
                val titleSize: Float = a.getDimension(R.styleable.SideMenuEntry_titleSize, 12f)
                menuTitle?.setTextColor(titleColor)
                menuTitle?.textSize = titleSize
                this.addView(menuIcon)
                this.addView(menuTitle)
                setOnClickListener(this)
        }

        constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
                context,
                attrs,
                defStyleAttr
        ) {
                menuTitle = TextView(context)
                menuIcon = ImageView(context)
                val rand = Random()
                val iconId = rand.nextInt(0x457689) + 1
                val titleId = rand.nextInt(0x457689) + 1
                menuIcon?.id = iconId
                menuTitle?.id = titleId
                val menuTitleParams = LayoutParams(
                        ViewGroup.LayoutParams.WRAP_CONTENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                )
                val menuIconParams = LayoutParams(
                        ViewGroup.LayoutParams.WRAP_CONTENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                )
                menuIcon?.layoutParams = menuIconParams
                menuTitle?.layoutParams = menuTitleParams
                menuIconParams.addRule(CENTER_VERTICAL)
                menuTitleParams.addRule(CENTER_VERTICAL)
                menuTitleParams.addRule(RIGHT_OF, iconId)
                val a = context.theme.obtainStyledAttributes(
                        attrs,
                        R.styleable.SideMenuEntry,
                        0, 0
                )
                val iconMarginLeft =
                        a.getDimension(R.styleable.SideMenuEntry_iconLeftMargin, 0f).toInt()
                val titleMarginLeft =
                        a.getDimension(R.styleable.SideMenuEntry_titleLeftMargin, 0f).toInt()
                menuIconParams.setMargins(iconMarginLeft, 0, 0, 0)
                menuTitleParams.setMargins(titleMarginLeft, 0, 0, 0)
                val titleColor = a.getColor(R.styleable.SideMenuEntry_titleColor, Color.BLACK)
                val titleSize: Float = a.getDimension(R.styleable.SideMenuEntry_titleSize, 12f)
                menuTitle?.setTextColor(titleColor)
                menuTitle?.textSize = titleSize
                this.addView(menuIcon)
                this.addView(menuTitle)
                setOnClickListener(this)
        }

        fun setTitle(title: String?) {
                menuTitle?.text = title
        }

        fun setIcon(res: Int) {
                menuIcon?.setImageResource(res)
        }

        override fun onClick(v: View) {
                if (onItemClickListener != null) onItemClickListener?.onItemClick()
        }

        interface OnItemClickListener {
                fun onItemClick()
        }
}