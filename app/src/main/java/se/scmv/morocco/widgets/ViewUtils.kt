package se.scmv.morocco.widgets


import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.ImageView
import androidx.databinding.BindingAdapter
import com.bumptech.glide.Glide
import se.scmv.morocco.R

class Margin(val left: Int, val top: Int, val right: Int, val bottom: Int)

@JvmName("dataBindingSetVisibility")
@BindingAdapter("showHide")
fun setVisibility(view: View, visible: Boolean?) {
        view.visibility = if (visible == true) View.VISIBLE else View.GONE
}

@BindingAdapter("url")
fun loadImage(imageView: ImageView, url: String?) {
        url?.let {
                Glide.with(imageView.context)
                        .load(it)
                        .fitCenter()
                        .centerCrop()
                        .placeholder(R.drawable.ic_no_image_placeholder)
                        .error(R.drawable.ic_no_image_placeholder)
                        .into(imageView)
        }
}

fun EditText.clearFocusAfterClickOnDone() {
        setOnEditorActionListener { _, actionId, _ ->
                if (actionId == EditorInfo.IME_ACTION_DONE) {
                        //Clear focus here from edittext
                        clearFocus()
                }
                false
        }
}