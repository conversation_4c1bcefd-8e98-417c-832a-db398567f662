package se.scmv.morocco.widgets

import android.content.Context
import android.os.Parcelable
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatToggleButton

class CustomToggleButton : AppCompatToggleButton {
        constructor(context: Context) : super(context)
        constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
        constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
                context,
                attrs,
                defStyleAttr
        )

        override fun onRestoreInstanceState(state: Parcelable) {
                try {
                        super.onRestoreInstanceState(state)
                } catch (ignored: ClassCastException) {
                        super.onRestoreInstanceState(onSaveInstanceState())
                }
        }
}