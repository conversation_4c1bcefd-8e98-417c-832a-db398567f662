package se.scmv.morocco.widgets

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.ContextCompat
import se.scmv.morocco.R

class FilterView(context: Context, attrs: AttributeSet?) : LinearLayout(context, attrs) {
        private val icon: AppCompatImageView
        private val label: AppCompatTextView
        private val indicator: AppCompatImageView
        private var state: State
        var changeStateListener: onChangeStateListener? = null

        init {
                val view = LayoutInflater.from(context)
                        .inflate(R.layout.widget_filter_view, this, false)
                icon = view.findViewById(R.id.icon) as AppCompatImageView
                label = view.findViewById(R.id.label) as AppCompatTextView
                indicator = view.findViewById(R.id.indicator) as AppCompatImageView
                state = State.DISABLED
                setupAttrs(attrs)
                view.setOnClickListener {
                        if (this.isEnabled)
                                changeStateFollowing(this.state)

                }
                this.addView(view)
        }

        private fun setupAttrs(attrs: AttributeSet?) {
                val array = context.obtainStyledAttributes(attrs, R.styleable.FilterView)
                val iconId = array.getResourceId(R.styleable.FilterView_icon, -1)
                if (iconId != -1)
                        icon.setImageResource(iconId)
                label.text = array.getString(R.styleable.FilterView_label)
                array.recycle()
        }

        fun changeStateFollowing(state: State) {
                changeStateListener?.onChangeState(this, state)
                when (state) {
                        State.DISABLED -> {
                                setAsc()
                        }
                        State.ASC -> {
                                setDesc()
                        }
                        State.DESC -> {
                                setAsc()
                        }
                }
        }

        private fun setDesc() {
                tint(context, R.color.ef_colorPrimary)
                indicator.setImageResource(R.drawable.ic_baseline_arrow_drop_down)
                this.state = State.DESC
        }

        private fun setAsc() {
                indicator.visibility = View.VISIBLE
                indicator.setImageResource(R.drawable.ic_baseline_arrow_drop_up)
                tint(context, R.color.ef_colorPrimary)
                this.state = State.ASC
        }

        fun disable() {
                indicator.visibility = View.INVISIBLE
                tint(context, R.color.color_control_normal)
                this.state = State.DESC
        }

        fun tint(context: Context, colorId: Int) {
                icon.tint(context, colorId)
                indicator.tint(context, colorId)
                label.tint(context, colorId)
        }

        enum class State {
                DISABLED, ASC, DESC
        }

        fun AppCompatImageView.tint(context: Context, colorId: Int) {
                this.setColorFilter(
                        ContextCompat.getColor(context, colorId),
                        android.graphics.PorterDuff.Mode.SRC_IN
                )

        }

        fun AppCompatTextView.tint(context: Context, colorId: Int) {
                this.setTextColor(context.resources.getColor(colorId))

        }

        interface onChangeStateListener {
                fun onChangeState(view: View, state: State)
        }


}