package se.scmv.morocco.widgets

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.viewpager.widget.ViewPager

/**
 * Created by amine on 25/03/15.
 */
class GalleryViewPager : ViewPager {
        var isLocked: Boolean

        constructor(context: Context?) : super(context!!) {
                isLocked = false
        }

        constructor(context: Context?, attrs: AttributeSet?) : super(context!!, attrs) {
                isLocked = false
        }

        override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
                return if (!isLocked) {
                        try {
                                super.onInterceptTouchEvent(ev)
                        } catch (e: IllegalArgumentException) {
                                e.printStackTrace()
                                false
                        }
                } else false
        }

        override fun onTouchEvent(event: MotionEvent): Boolean {
                return if (!isLocked) {
                        super.onTouchEvent(event)
                } else false
        }

        fun toggleLock() {
                isLocked = !isLocked
        }
}