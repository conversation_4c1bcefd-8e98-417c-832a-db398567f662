package se.scmv.morocco

import com.apollographql.apollo3.ApolloClient
import com.apollographql.apollo3.network.okHttpClient
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import se.scmv.morocco.analytics.BuildConfig
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.network.util.UserAgent
import se.scmv.morocco.urlsprovider.UrlsProvider
import se.scmv.morocco.utils.Utils
import se.scmv.morocco.utils.Utils.FIREBASE_ANALYTICS_INSTANCE_ID
import java.util.concurrent.TimeUnit

val GRAPHQL_ENDPOINT = UrlsProvider.getGraphQlUrl()
val GRAPHQL_WEB_SOCKET_URL = UrlsProvider.getGraphQlWebSocketUrl()

val httpLoggingInterceptor = HttpLoggingInterceptor().apply {
        if (BuildConfig.DEBUG) {
                level = HttpLoggingInterceptor.Level.BODY
        }
}
val okHttpClient = OkHttpClient.Builder()
        .addNetworkInterceptor(httpLoggingInterceptor)
        .connectTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(2, TimeUnit.MINUTES)
        .readTimeout(2, TimeUnit.MINUTES)
        .build()

val apolloClient: ApolloClient
        get() = ApolloClient.Builder()
                .serverUrl(GRAPHQL_ENDPOINT)
                .addHttpHeader(
                        "User-Session-ID",
                        Utils.getStringPreferenceWithDefaultValue(
                                Avito.context,
                                FIREBASE_ANALYTICS_INSTANCE_ID,
                                ""
                        )
                )
                .addHttpHeader("Accept-Language", LocaleManager.getCurrentLanguage())
                .addHttpHeader("user-agent", UserAgent.getUserAgentString())
                .okHttpClient(okHttpClient = okHttpClient)
                .build()

fun getApolloClientWithAuth(token: String): ApolloClient {
        return apolloClient.newBuilder().addHttpHeader("Authorization", token).build()
}

fun getApolloClientWithAuthWebSocket(): ApolloClient {
        return apolloClient.newBuilder().webSocketServerUrl(GRAPHQL_WEB_SOCKET_URL).build()
}

