package se.scmv.morocco.network;


import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;

/**
 * Created by amine on 08/12/14.
 */
public class NetworkManager {

    /*make request queue static to prevent multiple object creation which prevent out of memory Fabric #49*/
    private final Context mContext;
    private final ConnectivityManager cm;
    //private static NetworkManager mInstance;
    private NetworkStatusListener networkListener;

    public NetworkManager(Context context) {
        mContext = context;
        cm = (ConnectivityManager) mContext.getApplicationContext().getSystemService(Context.CONNECTIVITY_SERVICE);
    }

    public NetworkStatusListener getNetworkListener() {
        return networkListener;
    }

    public void setNetworkListener(NetworkStatusListener networkListener) {
        this.networkListener = networkListener;
    }

    /**
     * Get the availability of the Internet connection.
     *
     * @return true: Internet available
     * false: no Internet available
     */
    public boolean isConnected() {
        if (isInternetAvailable()) {
            if (networkListener != null)
                networkListener.onConnected();
            return true;
        }

        ConnectivityManager manager = (ConnectivityManager) mContext.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo netInfo = manager.getActiveNetworkInfo();
        if (netInfo != null && netInfo.isConnected()) {
            if (networkListener != null)
                networkListener.onConnected();
            return true;
        } else {
            if (networkListener != null)
                networkListener.onUnavailableNetwork();
            return false;
        }
    }

    private boolean isInternetAvailable() {

        NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
        return activeNetwork != null && activeNetwork.isConnectedOrConnecting();
    }

    public interface NetworkStatusListener {
        void onConnected();

        void onUnavailableNetwork();
    }
}
