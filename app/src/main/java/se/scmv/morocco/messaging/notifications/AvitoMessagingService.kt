package se.scmv.morocco.messaging.notifications

import android.Manifest
import android.app.Notification
import android.app.Notification.EXTRA_TEXT
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.BitmapFactory
import android.os.Build
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import androidx.core.app.TaskStackBuilder
import androidx.core.net.toUri
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import se.scmv.morocco.Avito.Companion.context
import se.scmv.morocco.R
import se.scmv.morocco.activities.MainActivity
import se.scmv.morocco.login.models.AccountToken
import se.scmv.morocco.messaging.common.Constants
import se.scmv.morocco.messaging.common.Constants.FIREBASE_MESSAGING_TOKEN
import se.scmv.morocco.messaging.common.Constants.MESSAGING_NOTIFICATION_TYPE
import se.scmv.morocco.messaging.common.Constants.NOTIFICATION_CHANNEL_ID
import se.scmv.morocco.messaging.common.Constants.NOTIFICATION_ID
import se.scmv.morocco.messaging.utils.MessagingUtils
import se.scmv.morocco.ui.AppDeepLinks
import se.scmv.morocco.utils.Utils

class AvitoMessagingService : FirebaseMessagingService() {
    override fun onNewToken(token: String) {
        val oldToken = Utils.getStringPreference(context, FIREBASE_MESSAGING_TOKEN)
        if (token == oldToken) {
            return
        }
        Utils.savePreference(context, FIREBASE_MESSAGING_TOKEN, token)
        if (AccountToken.isLoggedIn(context) && !AccountToken.isSessionExpired(context))
            MessagingUtils.registerFirebaseToken(token, oldToken)
        super.onNewToken(token)
    }

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)
        sendNotification(remoteMessage)

    }

    private fun isAvitoChatPushNotification(remoteMessage: RemoteMessage): Boolean {
        return !remoteMessage.data["body"].isNullOrEmpty() && !remoteMessage.data["title"].isNullOrEmpty() && !remoteMessage.data["redirect_to_conversation"].isNullOrEmpty()
    }


    private fun sendNotification(remoteMessage: RemoteMessage) {
        if (!isAvitoChatPushNotification(remoteMessage)) {
            return
        }

        val conversationId = remoteMessage.data["redirect_to_conversation"]
        val messageType = remoteMessage.data["type"]
        val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
        var currentNotificationMessages = "• " + (remoteMessage.data["body"]) + "  "
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            for (sbn in notificationManager.activeNotifications) {
                if (conversationId.equals(
                        sbn.tag,
                        ignoreCase = true
                    )
                ) {
                    val text =
                        sbn.notification.extras.getCharSequence(EXTRA_TEXT)
                    currentNotificationMessages =
                        text.toString() + System.lineSeparator() + currentNotificationMessages
                    break
                }
            }
        }
        val activityIntent = Intent(applicationContext, MainActivity::class.java)
        activityIntent.data = (AppDeepLinks.CHAT + "/$conversationId").toUri()
        val pendingIntent = TaskStackBuilder.create(this).run {
            addNextIntentWithParentStack(activityIntent)
            getPendingIntent(
                0,
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
                    PendingIntent.FLAG_UPDATE_CURRENT
                } else PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
            )
        }
        val largeIcon = BitmapFactory.decodeResource(
            applicationContext.resources,
            R.drawable.avito_logo
        )
        val builder = NotificationCompat.Builder(applicationContext, NOTIFICATION_CHANNEL_ID)
        builder.setAutoCancel(true)
            .setDefaults(Notification.DEFAULT_LIGHTS or Notification.DEFAULT_VIBRATE)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setCategory(NotificationCompat.CATEGORY_MESSAGE)
            .setWhen(System.currentTimeMillis())
            .setLargeIcon(largeIcon)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(remoteMessage.data["title"])
            .setStyle(NotificationCompat.BigTextStyle().bigText(currentNotificationMessages))
            .setOnlyAlertOnce(false)
            .setContentText(currentNotificationMessages)
            .setContentIntent(pendingIntent)

        val notificationManagerCompat = NotificationManagerCompat.from(applicationContext)

        if (Utils.getStringPreference(
                applicationContext,
                Constants.CURRENTLY_OPENED_CONVERSATION_ID
            ) != conversationId && messageType == MESSAGING_NOTIFICATION_TYPE
        ) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val channelId = conversationId ?: ""
                val channel = NotificationChannel(
                    channelId,
                    "Chat",
                    NotificationManager.IMPORTANCE_HIGH
                )
                notificationManagerCompat.createNotificationChannel(channel)
                builder.setChannelId(channelId)
            }
            if (ActivityCompat.checkSelfPermission(
                    applicationContext,
                    Manifest.permission.POST_NOTIFICATIONS
                ) == PackageManager.PERMISSION_GRANTED
            ) {
                notificationManagerCompat.notify(
                    conversationId,
                    NOTIFICATION_ID,
                    builder.build()
                )
            }
        }
    }
}