package se.scmv.morocco.messaging.common

import android.Manifest
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.app.ActivityCompat
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import se.scmv.morocco.R
import se.scmv.morocco.core.ItemClick

class AttachmentChooserBottomSheet(
        private val itemClick: ItemClick<PickImageChooserType>,
        private val canUploadDocuments: Boolean = false
) : BottomSheetDialogFragment() {

        companion object {
                fun newInstance(
                        itemClick: ItemClick<PickImageChooserType>,
                        allowDocuments: Boolean = false
                ): AttachmentChooserBottomSheet {
                        return AttachmentChooserBottomSheet(itemClick, allowDocuments)
                }
        }

        private val requestCameraPermission = registerForActivityResult(
                ActivityResultContracts.RequestPermission()
        ) {
                if (it) {
                        onItemChosen(PickImageChooserType.CAMERA)
                }
        }

        private val requestStoragePermission = registerForActivityResult(
                ActivityResultContracts.RequestPermission()
        ) {
                if (it) {
                        onItemChosen(PickImageChooserType.GALLERY)
                }
        }

        private val requestDocumentsPermission = registerForActivityResult(
                ActivityResultContracts.RequestPermission()
        ) {
                if (it) {
                        onItemChosen(PickImageChooserType.DOCUMENT)
                }
        }

        override fun onCreateView(
                inflater: LayoutInflater,
                container: ViewGroup?,
                savedInstanceState: Bundle?
        ): View? {
                return layoutInflater.inflate(
                        R.layout.attachment_type_chooser_bottom_sheet,
                        container,
                        false
                )
        }

        override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
                super.onViewCreated(view, savedInstanceState)

                view.findViewById<TextView>(R.id.title).apply {
                        if (canUploadDocuments) text = getString(R.string.attachment_chooser_title)
                }
                view.findViewById<LinearLayout>(R.id.gallery_button).setOnClickListener {
                        onGalleryChosen()
                }
                view.findViewById<LinearLayout>(R.id.camera_button).setOnClickListener {
                        onCameraChosen()
                }
                view.findViewById<LinearLayout>(R.id.document_button).apply {
                        visibility = if (canUploadDocuments) View.VISIBLE else View.GONE
                        setOnClickListener { onDocumentChosen() }
                }
        }

        private fun onGalleryChosen() {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        onItemClick(
                                PickImageChooserType.GALLERY,
                                Manifest.permission.READ_MEDIA_IMAGES
                        )
                } else {

                        onItemClick(
                                PickImageChooserType.GALLERY,
                                Manifest.permission.READ_EXTERNAL_STORAGE
                        )

                }
        }

        private fun onCameraChosen() {
                onItemClick(PickImageChooserType.CAMERA, Manifest.permission.CAMERA)
        }

        private fun onDocumentChosen() {
                onItemClick(
                        PickImageChooserType.DOCUMENT,
                        Manifest.permission.READ_EXTERNAL_STORAGE
                )
        }

        private fun onItemClick(type: PickImageChooserType, neededPermission: String) {
                if (ActivityCompat.checkSelfPermission(
                                requireContext(),
                                neededPermission
                        ) != PackageManager.PERMISSION_GRANTED
                ) {
                        when (type) {
                                PickImageChooserType.GALLERY -> requestStoragePermission.launch(
                                        neededPermission
                                )
                                PickImageChooserType.CAMERA -> requestCameraPermission.launch(
                                        neededPermission
                                )
                                PickImageChooserType.DOCUMENT -> if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {

                                        onItemChosen(PickImageChooserType.DOCUMENT)

                                } else {
                                        requestDocumentsPermission.launch(
                                                neededPermission
                                        )
                                }
                        }
                } else {
                        onItemChosen(type)
                }
        }

        private fun onItemChosen(type: PickImageChooserType) {
                itemClick.onItemClick(type)
                dismiss()
        }
}

enum class PickImageChooserType {
        CAMERA, GALLERY, DOCUMENT
}