package se.scmv.morocco.messaging.analytics

import se.scmv.morocco.Avito
import se.scmv.morocco.analytics.AnalyticsManager
import se.scmv.morocco.login.models.Account
import se.scmv.morocco.login.models.AccountToken
import se.scmv.morocco.messaging.common.Constants

object MessagingAnalyticsUtils {

        @JvmStatic
        fun getAccountProperties(): HashMap<String, String> {
                val eventProperties = HashMap<String, String>()

                if (AccountToken.isLoggedIn(Avito.context)) {
                        val currentAccount = Account.getCurrentAccount(Avito.context)
                        if (currentAccount != null) {
                                eventProperties[Constants.SELLER_TYPE] =
                                        if (currentAccount.accountType == 1) Constants.ACCOUNT_TYPE_SHOP else Constants.ACCOUNT_TYPE_PRIVATE
                        }
                }
                return eventProperties
        }

        @JvmStatic
        fun trackFirstContactSubmitted(
                category: Int,
                conversationId: String,
        ) {
                val eventProperties = java.util.HashMap<String?, String?>()
                eventProperties.putAll(getAccountProperties())
                eventProperties[Constants.CONVERSATION_ID] = conversationId
                eventProperties[Constants.SUBCATEGORY_ID] = category.toString()
                eventProperties[Constants.ELEMENT_NAME] = Constants.SEND_MESSAGE
                eventProperties[Constants.PAGE_NAME] = Constants.FIRST_CONTACT
                val manager = AnalyticsManager.instance
                manager?.logFirebaseEvent(Constants.ELEMENT_CLICKED, eventProperties)
        }

        @JvmStatic
        fun trackMessageSent(
                conversationId: String
        ) {
                val eventProperties = java.util.HashMap<String?, String?>()
                eventProperties.putAll(getAccountProperties())
                eventProperties[Constants.CONVERSATION_ID] = conversationId
                eventProperties[Constants.ELEMENT_NAME] = Constants.SEND_MESSAGE
                eventProperties[Constants.PAGE_NAME] = Constants.CHAT_CONVERSATION
                val manager = AnalyticsManager.instance
                manager?.logFirebaseEvent(Constants.ELEMENT_CLICKED, eventProperties)
        }


        @JvmStatic
        fun trackFirstContactScreen(eventProperties: HashMap<String?, String?>) {
                eventProperties.putAll(getAccountProperties())
                val manager = AnalyticsManager.instance
                manager?.logFirebaseEvent(
                        Constants.SCREEN_VIEW,
                        eventProperties
                )
        }

        @JvmStatic
        fun trackChatListScreen() {
                val eventProperties = java.util.HashMap<String?, String?>()
                eventProperties[Constants.CONTENT_TYPE] = Constants.CHAT_LISTING
                val manager = AnalyticsManager.instance
                manager?.logFirebaseEvent(
                        Constants.SCREEN_VIEW,
                        eventProperties
                )
        }

        @JvmStatic
        fun trackConversationScreen() {
                val eventProperties = java.util.HashMap<String?, String?>()
                eventProperties[Constants.CONTENT_TYPE] = Constants.CHAT_CONVERSATION
                val manager = AnalyticsManager.instance
                manager?.logFirebaseEvent(
                        Constants.SCREEN_VIEW,
                        eventProperties
                )
        }

        @JvmStatic
        fun trackBlockConversation(isActionBlock: Boolean, pageName: String) {
                val eventProperties = HashMap<String?, String?>()
                eventProperties[Constants.ELEMENT_NAME] =
                        if (isActionBlock) Constants.BLOCK_CONVERSATION else Constants.UNBLOCK_CONVERSATION
                eventProperties[Constants.PAGE_NAME] = pageName

                eventProperties.putAll(getAccountProperties())
                val manager = AnalyticsManager.instance
                manager?.logFirebaseEvent(
                        Constants.ELEMENT_CLICKED,
                        eventProperties
                )
        }

        @JvmStatic
        fun trackDeleteConversation(pageName: String) {
                val eventProperties = HashMap<String?, String?>()
                eventProperties[Constants.ELEMENT_NAME] = Constants.DELETE_CONVERSATION
                eventProperties[Constants.PAGE_NAME] = pageName

                eventProperties.putAll(getAccountProperties())
                val manager = AnalyticsManager.instance
                manager?.logFirebaseEvent(
                        Constants.ELEMENT_CLICKED,
                        eventProperties
                )
        }

}