package se.scmv.morocco.messaging.common

data class Resource<out T>(
        val status: Status,
        val data: T?,
        val errorCode: String?,
        val message: String?,
        val exception: Exception? = null
) {

        companion object {

                fun <T> success(data: T?): Resource<T> {
                        return Resource(Status.SUCCESS, data, null, null)
                }

                fun <T> updateItemFromSubscription(data: T?): Resource<T> {
                        return Resource(Status.SUBSCRIPTION_EVENT, data, null, null)
                }

                fun <T> updateItemFromRefreshing(data: T?): Resource<T> {
                        return Resource(Status.UPDATE_ITEM, data, null, null)
                }

                fun <T> addNewItem(data: T?): Resource<T> {
                        return Resource(Status.ADD_NEW_ITEM, data, null, null)
                }

                fun <T> empty(data: T?): Resource<T> {
                        return Resource(Status.EMPTY, data, null, null)
                }

                fun <T> loading(data: T? = null): Resource<T> {
                        return Resource(Status.LOADING, data, null, null)
                }

                fun <T> loadingPagination(data: T?): Resource<T> {
                        return Resource(Status.LOADING_PAGINATION, data, null, null)
                }

                fun <T> refreshing(data: T?): Resource<T> {
                        return Resource(Status.REFRESHING, data, null, null)
                }

                fun <T> error(
                        msg: String,
                        data: T? = null,
                        code: String? = null,
                        exception: Exception? = null,
                ): Resource<T> {
                        return Resource(Status.ERROR, data, code, msg, exception)
                }

        }

}