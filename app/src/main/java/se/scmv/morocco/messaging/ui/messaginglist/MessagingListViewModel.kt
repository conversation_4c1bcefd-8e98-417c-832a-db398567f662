package se.scmv.morocco.messaging.ui.messaginglist

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.apollographql.apollo3.api.ApolloResponse
import com.apollographql.apollo3.api.Error
import com.apollographql.apollo3.api.Optional
import com.apollographql.apollo3.exception.ApolloException
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.cancellable
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import se.scmv.morocco.Avito
import se.scmv.morocco.BlockConversationMutation
import se.scmv.morocco.ClearConversationMutation
import se.scmv.morocco.GetConversationByIdQuery
import se.scmv.morocco.GetConversationsListQuery
import se.scmv.morocco.InboxChattingSubscription
import se.scmv.morocco.UnblockConversationMutation
import se.scmv.morocco.core.getRequestToken
import se.scmv.morocco.getApolloClientWithAuth
import se.scmv.morocco.getApolloClientWithAuthWebSocket
import se.scmv.morocco.login.models.AccountToken
import se.scmv.morocco.messaging.analytics.MessagingAnalyticsUtils
import se.scmv.morocco.messaging.common.Constants
import se.scmv.morocco.messaging.common.Constants.PAGE_SIZE
import se.scmv.morocco.messaging.common.Resource
import se.scmv.morocco.messaging.ui.conversation.ConversationAction
import se.scmv.morocco.utils.isNotEmpty
import se.scmv.morocco.utils.isNotNull

class MessagingListViewModel : ViewModel() {
        private val _conversations =
                MutableLiveData<Resource<List<GetConversationsListQuery.Conversation?>?>>()
        val conversations: LiveData<Resource<List<GetConversationsListQuery.Conversation?>?>> =
                _conversations

        private val _lastConversation =
                MutableLiveData<Resource<GetConversationByIdQuery.GetMyChatConversation>>()
        val lastConversation: LiveData<Resource<GetConversationByIdQuery.GetMyChatConversation>> =
                _lastConversation

        private val _allConversations: MutableMap<String, Int> = mutableMapOf("" to 0)

        private val _unreadCount = MutableLiveData<Int>()
        val unreadCount: LiveData<Int> = _unreadCount

        private val _subscribed = MutableLiveData(false)
        val subscribed: LiveData<Boolean> = _subscribed

        var subscriptionJob: Job? = null

        private val _clearAdapter = MutableLiveData(false)
        val clearAdapter: LiveData<Boolean> = _clearAdapter

        private val _conversationAction = MutableSharedFlow<ConversationAction>()
        val conversationAction: SharedFlow<ConversationAction> = _conversationAction

        private var _conversationsCounter = 0

        private var _oldestConversationTime: Optional<String?> = Optional.Absent
        private var _newestConversationTime: Optional<String?> = Optional.Absent

        var initToken: String? = AccountToken.getCurrentToken(Avito.context)?.token
                private set

        init {
                if (AccountToken.isLoggedIn(Avito.context) && !AccountToken.isSessionExpired(Avito.context)) {
                        subscribe()
                        getConversations()
                }
        }

        fun clearViewModel() {
                _conversations.postValue(Resource.success(null))
                _subscribed.postValue(false)
                _clearAdapter.postValue(true)
                _oldestConversationTime = Optional.Absent
                _newestConversationTime = Optional.Absent
                _conversationsCounter = 0
                _allConversations.clear()
                subscriptionJob?.cancel()
                initToken = AccountToken.getCurrentToken(Avito.context)?.token
                if (AccountToken.isLoggedIn(Avito.context) && !AccountToken.isSessionExpired(Avito.context)) {
                        subscribe()
                        getConversations()
                }
        }

        private fun fetchConversations(
                pageSize: Int = PAGE_SIZE,
                requestToken: String
        ): Flow<ApolloResponse<GetConversationsListQuery.Data>?> = getApolloClientWithAuth(
                requestToken
        ).query(
                GetConversationsListQuery(
                        pageSize,
                        beforeTime = _oldestConversationTime
                )
        ).toFlow()

        private fun refreshConversations(
                pageSize: Int,
                requestToken: String
        ): Flow<ApolloResponse<GetConversationsListQuery.Data>?> = getApolloClientWithAuth(
                requestToken
        ).query(
                GetConversationsListQuery(
                        pageSize,
                        afterTime = _newestConversationTime
                )
        ).toFlow()

        private fun fetchConversationById(
                id: String,
                requestToken: String
        ): Flow<ApolloResponse<GetConversationByIdQuery.Data>?> = getApolloClientWithAuth(
                requestToken
        ).query(
                GetConversationByIdQuery(id)
        ).toFlow()


        fun subscribe() {
                subscriptionJob = viewModelScope.launch(Dispatchers.IO) {
                        try {
                                _subscribed.postValue(true)
                                getApolloClientWithAuthWebSocket().subscription(
                                        InboxChattingSubscription(AccountToken.getCurrentToken(Avito.context).token)
                                ).toFlow()
                                        .cancellable()
                                        .collect {
                                                Log.d("TAG----", "messaging list success")
                                                updateConversationsFromSubscriptionEvent(it)
                                        }
                        } catch (e: ApolloException) {
                                _subscribed.postValue(false)
                        }
                }
        }

        fun getConversations(
                pageSize: Int = PAGE_SIZE,
                isOnLoadMore: Boolean = false,
                isRefreshing: Boolean = false
        ) {
                getRequestToken()?.let {
                        viewModelScope.launch {
                                if (isRefreshing) {
                                        _conversations.postValue(Resource.refreshing(null))
                                } else if (isOnLoadMore) {
                                        _conversations.postValue(Resource.loadingPagination(null))
                                } else {
                                        _conversations.postValue(Resource.loading(null))
                                }
                                fetchConversations(pageSize, it).catch {
                                        if (!isRefreshing && !isOnLoadMore) {
                                                postError()
                                        }
                                }.collect {
                                        if (it?.data.isNotNull()) {
                                                postConversations(
                                                        it,
                                                        isRefreshing,
                                                        isOnLoadMore
                                                )
                                        } else {
                                                postError(it?.errors)
                                        }
                                        _unreadCount.postValue(it?.data?.getMyChat?.unreadCount)
                                }
                        }
                }
        }

        private fun postError(errors: List<Error>? = null) {
                _conversations.postValue(
                        Resource.error(
                                errors?.first()?.message ?: "",
                                null,
                                errors?.let {
                                        it.first().extensions?.get(
                                                "code"
                                        ).toString()
                                }
                        )
                )

        }

        private fun postConversations(
                response: ApolloResponse<GetConversationsListQuery.Data>?,
                isRefreshing: Boolean,
                isOnLoadMore: Boolean
        ) {
                val unclearedConversations =
                        response?.data?.getMyChat?.conversations?.filter { conversation -> conversation?.lastMessage != null }
                if (unclearedConversations.isNotEmpty()) {
                        if (isRefreshing) {
                                _clearAdapter.postValue(true)
                                _conversations.postValue(
                                        Resource.success(
                                                null
                                        )
                                )
                                _allConversations.clear()
                        }

                        if (!isOnLoadMore) {
                                _newestConversationTime =
                                        Optional.presentIfNotNull(
                                                unclearedConversations?.first()?.lastMessage?.time
                                        )
                        }
                        _oldestConversationTime =
                                Optional.presentIfNotNull(
                                        unclearedConversations?.last()?.lastMessage?.time
                                )
                        _conversations.postValue(
                                Resource.success(
                                        unclearedConversations
                                )
                        )
                        unclearedConversations?.let { conversations ->
                                conversations.forEach { conv ->
                                        conv?.let { c ->
                                                _allConversations.putIfAbsent(
                                                        c.id,
                                                        c.unreadCount
                                                )
                                        }
                                }
                        }
                } else if (!isOnLoadMore) {
                        _conversations.postValue(Resource.empty(null))
                } else {
                        _conversations.postValue(
                                Resource.success(
                                        unclearedConversations
                                )
                        )
                }
        }

        private fun updateConversationsFromSubscriptionEvent(response: ApolloResponse<InboxChattingSubscription.Data>) {
                if (response.data.isNotNull()) {
                        response.data?.subscribe?.event?.onMessageReceived?.let { receivedMessage ->
                                if (_allConversations.containsKey(
                                                receivedMessage.conversationId
                                        )
                                ) {
                                        updateExistingConversation(receivedMessage)

                                } else {
                                        getConversationById(
                                                receivedMessage.conversationId
                                        )

                                }
                        }

                }
        }

        private fun updateExistingConversation(receivedMessage: InboxChattingSubscription.OnMessageReceived) {
                val lastMsg =
                        GetConversationsListQuery.LastMessage(
                                id = receivedMessage.id,
                                text = receivedMessage.text,
                                attachment = if (receivedMessage.attachment != null) {
                                        GetConversationsListQuery.Attachment(
                                                receivedMessage.attachment!!.type,
                                                receivedMessage.attachment!!.url
                                        )
                                } else {
                                        null
                                },
                                isUnread = true,
                                isMine = false,
                                time = receivedMessage.time
                        )
                var unreadCount = 0
                _allConversations[receivedMessage.conversationId]?.let { c ->
                        unreadCount = c + 1
                        _allConversations[receivedMessage.conversationId] = unreadCount
                }

                val conversation =
                        GetConversationsListQuery.Conversation(
                                receivedMessage.conversationId,
                                null,
                                null,
                                lastMsg,
                                false,
                                unreadCount
                        )

                val list: List<GetConversationsListQuery.Conversation> =
                        listOf(conversation)
                _conversations.postValue(
                        Resource.updateItemFromSubscription(
                                list
                        )
                )
                _unreadCount.postValue(
                        unreadCount
                )
        }

        private fun getConversationById(
                id: String
        ) {
                AccountToken.getCurrentToken(Avito.context)?.let { accountToken ->
                        val errorHandler = CoroutineExceptionHandler { _, error ->
                                se.scmv.morocco.utils.Log.e(
                                        "fetchLatestConversations",
                                        "Error: ${error.localizedMessage}"
                                )
                        }
                        viewModelScope.launch(errorHandler) {
                                fetchConversationById(id, accountToken.requestToken)
                                        .catch { e ->
                                        }
                                        .collect {
                                                it?.data?.getMyChatConversation?.let { conversation ->
                                                        _lastConversation.postValue(
                                                                Resource.addNewItem(
                                                                        conversation
                                                                )
                                                        )

                                                        _unreadCount.postValue(conversation.unreadCount)

                                                        _allConversations.putIfAbsent(
                                                                conversation.id,
                                                                conversation.unreadCount
                                                        )
                                                }

                                        }
                        }
                }
        }

        fun fetchLatestConversations(
                pageSize: Int = PAGE_SIZE
        ) {
                AccountToken.getCurrentToken(Avito.context)?.let { accountToken ->
                        val errorHandler = CoroutineExceptionHandler { _, error ->
                                se.scmv.morocco.utils.Log.e(
                                        "fetchLatestConversations",
                                        "Error: ${error.localizedMessage}"
                                )
                        }
                        viewModelScope.launch(errorHandler) {
                                _conversations.postValue(Resource.refreshing(null))
                                refreshConversations(pageSize, accountToken.requestToken)
                                        .catch {
                                                _conversations.postValue(Resource.success(null))
                                        }
                                        .collect {
                                                postLatestConversations(it)
                                        }
                        }
                }

        }

        private suspend fun postLatestConversations(response: ApolloResponse<GetConversationsListQuery.Data>?) {
                val unclearedConversations =
                        response?.data?.getMyChat?.conversations?.filter { conversation -> conversation?.lastMessage != null }

                if (!unclearedConversations.isNotEmpty()) {
                        _conversations.postValue(
                                Resource.success(
                                        null
                                )
                        )
                        return
                }

                unclearedConversations?.let { conversations ->
                        for (i in (0..conversations.lastIndex).reversed()) {
                                if (_newestConversationTime != Optional.presentIfNotNull(
                                                conversations[i]?.lastMessage?.time
                                        )
                                ) {
                                        if (_allConversations.containsKey(
                                                        conversations[i]?.id
                                                )
                                        ) {

                                                withContext(
                                                        Dispatchers.Main
                                                ) {
                                                        _conversations.value =
                                                                Resource.updateItemFromRefreshing(
                                                                        listOf(conversations[i])
                                                                )
                                                }

                                        } else {
                                                withContext(
                                                        Dispatchers.Main
                                                ) {
                                                        _conversations.value =
                                                                Resource.addNewItem(
                                                                        listOf(conversations[i])
                                                                )
                                                }
                                                conversations[i]?.let { c ->
                                                        _allConversations.putIfAbsent(
                                                                c.id,
                                                                c.unreadCount
                                                        )
                                                }
                                        }
                                } else
                                        _conversations.postValue(
                                                Resource.success(
                                                        null
                                                )
                                        )
                        }
                }

                _newestConversationTime =
                        Optional.presentIfNotNull(
                                unclearedConversations?.first()?.lastMessage?.time
                        )
        }

        fun resetUnreadCountById(conversationId: String) {
                _allConversations[conversationId] = 0
        }

        fun clearConversation(id: String) {
                getRequestToken()?.let {
                        val errorHandler = CoroutineExceptionHandler { _, error ->
                                se.scmv.morocco.utils.Log.e(
                                        "clearConversation",
                                        "Error: ${error.localizedMessage}"
                                )
                        }
                        viewModelScope.launch(errorHandler) {

                                getApolloClientWithAuth(it).mutation(
                                        ClearConversationMutation(id)
                                ).toFlow()
                                        .catch {
                                                _conversationAction.emit(
                                                        ConversationAction.ClearConv(
                                                                id = id,
                                                                cleared = false
                                                        )
                                                )
                                        }
                                        .collect {
                                                _conversationAction.emit(
                                                        ConversationAction.ClearConv(
                                                                id = id,
                                                                cleared = it.data?.clearConversation?.success
                                                                        ?: false
                                                        )
                                                )
                                                MessagingAnalyticsUtils.trackDeleteConversation(
                                                        Constants.CHAT_LISTING
                                                )

                                        }
                        }
                }
        }

        fun blockConversation(id: String) {
                getRequestToken()?.let {
                        val errorHandler = CoroutineExceptionHandler { _, error ->
                                se.scmv.morocco.utils.Log.e(
                                        "blockConversation",
                                        "Error: ${error.localizedMessage}"
                                )
                        }
                        viewModelScope.launch(errorHandler) {

                                getApolloClientWithAuth(it).mutation(
                                        BlockConversationMutation(id)
                                ).toFlow().catch {
                                        _conversationAction.emit(
                                                ConversationAction.BlockConv(
                                                        id = id,
                                                        blocked = false
                                                )
                                        )
                                }.collect {
                                        _conversationAction.emit(
                                                ConversationAction.BlockConv(
                                                        id = id,
                                                        blocked = it.data?.blockConversation?.success
                                                                ?: false
                                                )
                                        )
                                        MessagingAnalyticsUtils.trackBlockConversation(
                                                true,
                                                Constants.CHAT_LISTING
                                        )
                                }
                        }
                }
        }

        fun unblockConversation(id: String) {
                getRequestToken()?.let {
                        val errorHandler = CoroutineExceptionHandler { _, error ->
                                se.scmv.morocco.utils.Log.e(
                                        "unblockConversation",
                                        "Error: ${error.localizedMessage}"
                                )
                        }
                        viewModelScope.launch(errorHandler) {

                                getApolloClientWithAuth(it).mutation(
                                        UnblockConversationMutation(id)
                                ).toFlow().catch {
                                        _conversationAction.emit(
                                                ConversationAction.UnblockConv(
                                                        id = id,
                                                        unblocked = false
                                                )
                                        )
                                }.collect {
                                        _conversationAction.emit(
                                                ConversationAction.UnblockConv(
                                                        id = id,
                                                        unblocked = it.data?.unblockConversation?.success
                                                                ?: false
                                                )
                                        )
                                        MessagingAnalyticsUtils.trackBlockConversation(
                                                false,
                                                Constants.CHAT_LISTING
                                        )

                                }
                        }
                }
        }
}

