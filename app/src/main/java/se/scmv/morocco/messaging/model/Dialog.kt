package se.scmv.morocco.messaging.model

import com.stfalcon.chatkit.commons.models.IDialog


/*
* Created by troy379 on 04.04.17.
*/
class Dialog(
        private val id: String,
        private val dialogName: String?,
        private val dialogPhoto: String?,
        users: ArrayList<User> = arrayListOf(),
        lastMessage: Message,
        unreadCount: Int,
        partnerName: String = "",
        avatarUrl: String = "",
        private var blocked: Boolean = false
) :
        IDialog<Message> {
        private val users: ArrayList<User>
        private var lastMessage: Message
        private var partnerName: String
        private var avatarUrl: String
        private var unreadCount: Int
        override fun getId(): String {
                return id
        }

        override fun getDialogPhoto(): String {
                return dialogPhoto ?: ""
        }

        override fun getDialogName(): String? {
                return dialogName
        }

        override fun getUsers(): ArrayList<User> {
                return users
        }

        override fun getLastMessage(): Message {
                return lastMessage
        }

        override fun setLastMessage(lastMessage: Message) {
                this.lastMessage = lastMessage
        }

        override fun getUnreadCount(): Int {
                return unreadCount
        }

        fun setUnreadCount(unreadCount: Int) {
                this.unreadCount = unreadCount
        }


        override fun resetUnreadCount() {
                this.unreadCount = 0
        }

        init {
                this.users = users
                this.lastMessage = lastMessage
                this.unreadCount = unreadCount
                this.partnerName = partnerName
                this.avatarUrl = avatarUrl
        }

        override fun getPartnerName(): String {
                return partnerName
        }


        override fun getAvatar(): String {
                return avatarUrl
        }

        override fun setBlocked(blocked: Boolean) {
                this.blocked = blocked
        }

        override fun isBlocked(): Boolean {
                return this.blocked
        }
}