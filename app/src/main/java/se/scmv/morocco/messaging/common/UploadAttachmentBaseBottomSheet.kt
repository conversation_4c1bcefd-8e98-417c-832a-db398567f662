package se.scmv.morocco.messaging.common

import android.content.ContentResolver
import android.content.Context
import android.net.Uri
import android.os.Build
import android.webkit.MimeTypeMap
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.FileProvider
import org.apache.commons.io.FileUtils.copyInputStreamToFile
import se.scmv.morocco.BuildConfig
import se.scmv.morocco.core.BaseBottomSheet
import se.scmv.morocco.core.ItemClick
import se.scmv.morocco.messaging.common.Constants.MESSAGING_SUPPORTED_FILE_MIME_TYPES
import se.scmv.morocco.utils.UriUtils
import java.io.File


abstract class UploadAttachmentBaseBottomSheet : BaseBottomSheet(mDraggable = false),
        ItemClick<PickImageChooserType> {
        private var attachmentPath: String? = null

        abstract fun onAttachmentPicked(attachment: Attachment)

        private val takePictureLauncher = registerForActivityResult(
                ActivityResultContracts.TakePicture()
        ) { picked ->
                if (picked) attachmentPath?.let { onAttachmentPicked(Attachment.Image(it)) }
        }

        private val pickPictureLauncher = registerForActivityResult(
                ActivityResultContracts.GetContent()
        ) { uri ->
                getFileFromUri(uri)?.let {
                        onAttachmentPicked(Attachment.Image(it.absolutePath))
                }
        }

        private val uploadFile =
                registerForActivityResult(ActivityResultContracts.OpenDocument()) { uri ->
                        getFileFromUri(uri)?.let {
                                onAttachmentPicked(
                                        Attachment.File(
                                                path = it.absolutePath,
                                                name = UriUtils.getFileName(requireContext(), uri)
                                        )
                                )
                        }
                }

        protected fun pickImage() {
                AttachmentChooserBottomSheet
                        .newInstance(this, allowDocuments = true)
                        .show(
                                childFragmentManager,
                                AttachmentChooserBottomSheet::class.java.canonicalName
                        )
        }

        override fun onItemClick(item: PickImageChooserType) {
                when (item) {
                        PickImageChooserType.GALLERY -> pickPictureLauncher.launch("image/*")
                        PickImageChooserType.CAMERA -> takePictureLauncher.launch(getTmpFileUri())
                        PickImageChooserType.DOCUMENT -> if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                                uploadFile.launch(
                                        MESSAGING_SUPPORTED_FILE_MIME_TYPES
                                )
                        } else {
                                uploadFile.launch(
                                        MESSAGING_SUPPORTED_FILE_MIME_TYPES
                                )
                        }

                }
        }

        private fun getFileFromUri(fileUri: Uri?): File? {
                fileUri?.let { uri ->
                        val mimeType = getMimeType(requireContext(), uri)
                        mimeType?.let {
                                return createTmpFileFromUri(
                                        requireContext(),
                                        fileUri,
                                        "temp_attachment",
                                        ".$it"
                                )
                        }
                }
                return null
        }

        private fun getMimeType(context: Context, uri: Uri): String? {
                //Check uri format to avoid null
                val extension: String? = if (uri.scheme == ContentResolver.SCHEME_CONTENT) {
                        //If scheme is a content
                        val mime = MimeTypeMap.getSingleton()
                        mime.getExtensionFromMimeType(context.contentResolver.getType(uri))
                } else {
                        //If scheme is a File
                        //This will replace white spaces with %20 and also other special characters. This will avoid returning null values on file name with spaces and special characters.
                        MimeTypeMap.getFileExtensionFromUrl(Uri.fromFile(File(uri.path)).toString())
                }
                return extension
        }

        private fun createTmpFileFromUri(
                context: Context,
                uri: Uri,
                fileName: String,
                mimeType: String
        ): File? {
                return try {
                        val stream = context.contentResolver.openInputStream(uri)
                        val file =
                                File.createTempFile(fileName, mimeType, requireActivity().cacheDir)
                        copyInputStreamToFile(stream, file)
                        file
                } catch (e: Exception) {
                        e.printStackTrace()
                        null
                }
        }

        private fun getTmpFileUri(): Uri {
                val tmpFile = File.createTempFile(
                        "tmp_image_file", ".png",
                        requireActivity().cacheDir
                ).apply {
                        createNewFile()
                        deleteOnExit()
                }.also {
                        attachmentPath = it.absolutePath
                }

                return FileProvider.getUriForFile(
                        requireContext(),
                        "${BuildConfig.APPLICATION_ID}.imagesprovider",
                        tmpFile
                )
        }
}

sealed class Attachment {
        class Image(val path: String) : Attachment()
        class File(val path: String, val name: String) : Attachment()
}