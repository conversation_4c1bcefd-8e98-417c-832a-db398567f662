package se.scmv.morocco.messaging.ui.conversation

import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.annotation.Nullable
import androidx.appcompat.widget.Toolbar
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import se.scmv.morocco.R
import se.scmv.morocco.core.BaseBottomSheet

private const val IMAGE_URL = "se.scmv.morocco.messaging.ui.conversation.IMAGE_URL"

class PreviewImageBottomSheet : BaseBottomSheet(mDraggable = false) {

        private lateinit var adPictureView: ImageView

        override fun onCreateView(
                inflater: LayoutInflater,
                container: ViewGroup?,
                savedInstanceState: Bundle?
        ): View? {
                return inflater.inflate(R.layout.fragment_preview_image, container, false)
        }

        override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
                super.onViewCreated(view, savedInstanceState)
                adPictureView = view.findViewById(R.id.pictureView)
                val toolbar = view.findViewById<Toolbar>(R.id.toolbar)
                toolbar?.visibility = View.VISIBLE
                toolbar?.setNavigationOnClickListener {
                        dismiss()
                }

                arguments?.getString(IMAGE_URL)?.let {
                        displayImage(it)
                }
        }

        private fun displayImage(url: String) {
                Glide.with(requireContext())
                        .load(url)
                        .into(object : CustomTarget<Drawable?>() {
                                override fun onResourceReady(
                                        resource: Drawable,
                                        transition: Transition<in Drawable?>?
                                ) {
                                        adPictureView.setImageDrawable(resource)
                                }

                                override fun onLoadCleared(@Nullable placeholder: Drawable?) = Unit

                        })
        }


        companion object {
                fun newInstance(url: String): PreviewImageBottomSheet {
                        val args = Bundle()
                        args.putString(IMAGE_URL, url)
                        val fragment = PreviewImageBottomSheet()
                        fragment.arguments = args
                        return fragment
                }
        }
}
