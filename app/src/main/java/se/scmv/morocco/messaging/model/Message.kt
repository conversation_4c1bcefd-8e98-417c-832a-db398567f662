package se.scmv.morocco.messaging.model

import com.stfalcon.chatkit.commons.models.IMessage
import com.stfalcon.chatkit.commons.models.MessageContentType
import com.stfalcon.chatkit.commons.models.Status
import java.util.Date


class Message @JvmOverloads constructor(
        private val id: String,
        private val user: User,
        private var text: String,
        createdAt: Date = Date(),
        private var status: Status,
        var image: Image? = null,
        var file: File? = null,
        var paginationLoaderVisible: Boolean? = false,
        val conversationId: String? = "",
) :
        IMessage, MessageContentType.Image, MessageContentType.File, MessageContentType.Loader,
        MessageContentType /*and this one is for custom content type (in this case - voice message)*/ {
        private var createdAt: Date


        override fun getId(): String {
                return id
        }

        override fun getText(): String {
                return text
        }

        override fun getCreatedAt(): Date {
                return createdAt
        }

        override fun getStatus(): Status {
                return this.status
        }

        override fun visible(): Boolean? {
                return this.paginationLoaderVisible
        }

        override fun getUser(): User {
                return user
        }

        override fun getImageUrl(): String? {
                return image?.url
        }

        override fun getImageType(): MessageContentType.Type? {
                return image?.type
        }

        override fun getFilePath(): String? {
                return file?.path
        }

        override fun getFileName(): String? {
                return file?.name
        }

        fun setText(text: String) {
                this.text = text
        }

        fun setCreatedAt(createdAt: Date) {
                this.createdAt = createdAt
        }

        fun hasAttachment(): Boolean {
                return image != null
        }

        fun setStatus(status: Status) {
                this.status = status
        }

        class Image(val url: String, val type: MessageContentType.Type)
        class File(val path: String, val name: String)

        init {
                this.createdAt = createdAt
        }
}
