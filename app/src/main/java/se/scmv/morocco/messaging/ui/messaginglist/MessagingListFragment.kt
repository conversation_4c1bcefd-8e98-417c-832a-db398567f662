package se.scmv.morocco.messaging.ui.messaginglist

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.widget.PopupMenu
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout.OnRefreshListener
import com.stfalcon.chatkit.commons.ImageLoader
import com.stfalcon.chatkit.dialogs.DialogsListAdapter
import com.stfalcon.chatkit.utils.DateFormatter
import se.scmv.morocco.Avito
import se.scmv.morocco.R
import se.scmv.morocco.authentication.presentation.AuthenticationActivity
import se.scmv.morocco.core.formattedDate
import se.scmv.morocco.core.setupChatkitImageLoader
import se.scmv.morocco.core.showConfirmationDialog
import se.scmv.morocco.databinding.FragmentMessagingListBinding
import se.scmv.morocco.login.models.AccountToken
import se.scmv.morocco.messaging.analytics.MessagingAnalyticsUtils.trackChatListScreen
import se.scmv.morocco.messaging.common.Constants.AUTH_REQUIRED
import se.scmv.morocco.messaging.common.Constants.PERFORM_MESSAGING_LIST_REFRESH
import se.scmv.morocco.messaging.common.Status
import se.scmv.morocco.messaging.common.createMessageFromSubscription
import se.scmv.morocco.messaging.common.getConversation
import se.scmv.morocco.messaging.common.getConversations
import se.scmv.morocco.messaging.common.getLastMessageTextWithAttachment
import se.scmv.morocco.messaging.model.Dialog
import se.scmv.morocco.messaging.ui.conversation.ConversationAction
import se.scmv.morocco.messaging.ui.conversation.ConversationActionsListener
import se.scmv.morocco.messaging.ui.conversation.ConversationBottomSheet
import se.scmv.morocco.utils.Utils
import java.util.Date


class MessagingListFragment : Fragment(), DateFormatter.Formatter,
        DialogsListAdapter.OnLoadMoreListener, OnRefreshListener, ConversationActionsListener,
        ChatListUpdateListener {

        private var _binding: FragmentMessagingListBinding? = null
        private val binding: FragmentMessagingListBinding get() = _binding!!

        lateinit var imageLoader: ImageLoader
        lateinit var dialogsAdapter: DialogsListAdapter<Dialog>

        private val viewModel: MessagingListViewModel by viewModels()


        companion object {
                fun newInstance() = MessagingListFragment()
        }

        override fun onCreateView(
                inflater: LayoutInflater, container: ViewGroup?,
                savedInstanceState: Bundle?
        ): View {
                _binding = FragmentMessagingListBinding.inflate(
                        inflater,
                        container,
                        false
                )
                return binding.root
        }

        override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
                super.onViewCreated(view, savedInstanceState)

                setupImageLoader()
                initAdapter()
                setupObserver()

                binding.swipeToRefreshLayout.setOnRefreshListener(this)
                binding.chatErrorView.retryButton.setOnClickListener {
                        if (AccountToken.isLoggedIn(Avito.context) && !AccountToken.isSessionExpired(
                                        Avito.context
                                )
                        ) {
                                viewModel.subscribe()
                                viewModel.getConversations()
                        }
                }
                binding.loginLayout.loginButton.setOnClickListener {
                        startLogin()
                }
        }

        override fun onResume() {
                super.onResume()
                trackChatListScreen()
                if (isTokenChanged() && AccountToken.isLoggedIn(
                                Avito.context
                        ) && !AccountToken.isSessionExpired(
                                Avito.context
                        )
                ) {
                        dialogsAdapter.clear()
                        viewModel.clearViewModel()


                }
                if ((viewModel.subscribed.value == false) && AccountToken.isLoggedIn(
                                Avito.context
                        ) && !AccountToken.isSessionExpired(
                                Avito.context
                        )
                ) {
                        viewModel.subscribe()
                        viewModel.getConversations()
                }
                if (Utils.getBooleanPreference(
                                requireContext(),
                                PERFORM_MESSAGING_LIST_REFRESH, false
                        )
                ) {
                        onRefresh()
                        Utils.savePreference(
                                requireContext(),
                                PERFORM_MESSAGING_LIST_REFRESH,
                                false
                        )
                }

                if (!Utils.getSetPreference(
                                requireContext(), Utils.CONVERSATIONS_ACTION
                        ).isNullOrEmpty()
                ) {
                        val actions: MutableSet<String> = Utils.getSetPreference(
                                requireContext(),
                                Utils.CONVERSATIONS_ACTION
                        )
                        actions.reversed().forEach { action ->
                                when {
                                        action.contains(":block") -> {
                                                onConversationActionTriggered(
                                                        ConversationAction.BlockConv(
                                                                id = action.substring(
                                                                        0,
                                                                        action.indexOf(":")
                                                                ),
                                                                blocked = true
                                                        )
                                                )
                                        }
                                        action.contains(":unblock") -> {
                                                onConversationActionTriggered(
                                                        ConversationAction.UnblockConv(
                                                                id = action.substring(
                                                                        0,
                                                                        action.indexOf(":")
                                                                ),
                                                                unblocked = true
                                                        )
                                                )
                                        }
                                        action.contains(":clear") -> {
                                                onConversationActionTriggered(
                                                        ConversationAction.ClearConv(
                                                                id = action.substring(
                                                                        0,
                                                                        action.indexOf(":")
                                                                ),
                                                                cleared = true
                                                        )
                                                )
                                        }
                                }
                        }
                        Utils.removePreference(requireContext(), Utils.CONVERSATIONS_ACTION)

                }
                setLoginVisibility()
        }

        override fun onDestroyView() {
                _binding = null
                super.onDestroyView()
        }

        private fun setupImageLoader() {
                imageLoader = setupChatkitImageLoader()
        }

        private fun initAdapter() {
                dialogsAdapter = DialogsListAdapter(imageLoader)
                dialogsAdapter.setLoadMoreListener(this)
                dialogsAdapter.setDatesFormatter(this)

                dialogsAdapter.setOnDialogClickListener {
                        it?.let {
                                if (it.id.isNotEmpty()) {
                                        resetConversationUnreadCount(it.id)
                                        ConversationBottomSheet.newInstance(
                                                conversationId = it.id,
                                                conversationActionsListener = this,
                                                chatListUpdateListener = this
                                        ).show(
                                                requireActivity().supportFragmentManager,
                                                it.id
                                        )
                                }
                        }
                }
                dialogsAdapter.setOnDialogViewClickListener { view, dialog ->
                        when (view.id) {
                                R.id.dialogMoreOptions -> onAdapterMoreOptionClicked(view, dialog)
                        }
                }
                binding.dialogsList.setAdapter(dialogsAdapter)
        }

        private fun setupObserver() {
                viewModel.lastConversation.observe(viewLifecycleOwner) {
                        when (it.status) {
                                Status.ADD_NEW_ITEM -> {
                                        dialogsAdapter.addItem(
                                                0,
                                                getConversation(
                                                        it.data,
                                                        it.data?.lastMessage?.time.formattedDate(),
                                                        requireContext()
                                                )
                                        )
                                }

                                else -> Unit
                        }
                }

                viewModel.unreadCount.observe(viewLifecycleOwner) {

                }

                viewModel.conversations.observe(viewLifecycleOwner) {
                        setupLayoutsVisibility(it.status, it.errorCode)
                        when (it.status) {
                                Status.SUCCESS -> {
                                        it.data?.let { conversations ->
                                                dialogsAdapter.addItems(
                                                        getConversations(
                                                                conversations,
                                                                requireContext()
                                                        )
                                                )
                                        }
                                }
                                Status.SUBSCRIPTION_EVENT -> {
                                        dialogsAdapter.updateDialogWithMessage(
                                                it.data?.get(0)?.id, createMessageFromSubscription(
                                                        it.data?.get(0)?.lastMessage!!.id,
                                                        it.data[0]?.lastMessage?.time.formattedDate(),
                                                        getLastMessageTextWithAttachment(it.data[0]?.lastMessage)
                                                )
                                        )

                                        if (it.data[0]?.unreadCount != null && it.data[0]?.unreadCount!! > 0) {
                                                dialogsAdapter.getItemById(it.data[0]?.id)?.unreadCount =
                                                        it.data[0]?.unreadCount!!

                                        }
                                }

                                Status.UPDATE_ITEM -> {
                                        dialogsAdapter.deleteById(it.data?.get(0)?.id)
                                        dialogsAdapter.addItem(
                                                0, getConversation(
                                                        it.data?.get(0),
                                                        it.data?.get(0)?.lastMessage?.time.formattedDate(),
                                                        requireContext()
                                                )
                                        )
                                        binding.dialogsList.scrollToPosition(0)
                                }

                                Status.ADD_NEW_ITEM -> {
                                        dialogsAdapter.addItem(
                                                0,
                                                getConversation(
                                                        it.data?.get(0),
                                                        it.data?.get(0)?.lastMessage?.time.formattedDate(),
                                                        requireContext()
                                                )
                                        )
                                        binding.dialogsList.scrollToPosition(0)
                                }
                                Status.ERROR -> {
                                        if (it.errorCode != AUTH_REQUIRED) {
                                                val errorMsg =
                                                        if (it?.message?.isEmpty() == true) getString(
                                                                R.string.mc_inbox_error_loading_messages_subtitle
                                                        ) else it?.message
                                                binding.chatErrorView.textViewDescription.text =
                                                        errorMsg
                                        }
                                }

                                else -> Unit
                        }
                }
                viewModel.clearAdapter.observe(viewLifecycleOwner, Observer {
                        if (it == true)
                                dialogsAdapter.clear()
                })
                lifecycleScope.launchWhenStarted {
                        viewModel.conversationAction.collect { it: ConversationAction ->
                                onConversationActionTriggered(it)
                                //use this
                        }
                }
        }

        private fun onAdapterMoreOptionClicked(view: View, dialog: Dialog) {
                val popupMenu = PopupMenu(requireContext(), view)
                popupMenu.menuInflater.inflate(
                        if (dialog.isBlocked) {
                                R.menu.messaging_list_blocked_item_menu
                        } else {
                                R.menu.messaging_list_unblocked_item_menu
                        },
                        popupMenu.menu
                )
                popupMenu.setOnMenuItemClickListener {
                        when (it.itemId) {
                                R.id.block_user -> {
                                        showConfirmationDialog(
                                                title = R.string.app_name,
                                                description = getString(R.string.mc_inbox_block_user_dialog_message),
                                                onConfirm = { viewModel.blockConversation(dialog.id) },
                                                onDecline = {}
                                        )
                                        true
                                }
                                R.id.unblock_user -> {
                                        viewModel.unblockConversation(dialog.id)
                                        true
                                }
                                R.id.clear_conversation -> {
                                        showConfirmationDialog(
                                                title = R.string.app_name,
                                                description = getString(R.string.mc_inbox_delete_conversation_dialog_message),
                                                onConfirm = { viewModel.clearConversation(dialog.id) },
                                                onDecline = {}
                                        )
                                        true
                                }
                                else -> {
                                        false
                                }
                        }
                }
                popupMenu.show()
        }

        override fun onLoadMore(page: Int, totalItemsCount: Int) {
                viewModel.getConversations(isOnLoadMore = true)
        }

        override fun onConversationActionTriggered(conversationAction: ConversationAction) {
                when (conversationAction) {
                        is ConversationAction.ClearConv -> if (conversationAction.cleared) {
                                dialogsAdapter.deleteById(conversationAction.id)
                        } else {
                                Toast.makeText(
                                        requireContext(),
                                        getString(R.string.mc_error_delete_not_available_when_integration_on_going),
                                        Toast.LENGTH_SHORT
                                )
                                        .show()
                        }
                        is ConversationAction.BlockConv -> if (conversationAction.blocked) {
                                dialogsAdapter.setBlockedById(conversationAction.id, true)
                                Toast.makeText(
                                        requireContext(),
                                        getString(R.string.mc_inbox_user_has_been_blocked),
                                        Toast.LENGTH_SHORT
                                )
                                        .show()
                        } else {
                                Toast.makeText(
                                        requireContext(),
                                        R.string.mc_error_block_not_available_when_integration_on_going,
                                        Toast.LENGTH_SHORT
                                )
                                        .show()
                        }
                        is ConversationAction.UnblockConv -> if (conversationAction.unblocked) {
                                dialogsAdapter.setBlockedById(conversationAction.id, false)
                                Toast.makeText(
                                        requireContext(),
                                        getString(R.string.mc_inbox_user_has_been_unblocked),
                                        Toast.LENGTH_SHORT
                                )
                                        .show()
                        } else {
                                Toast.makeText(
                                        requireContext(),
                                        R.string.mc_error_unblock_not_available_when_integration_on_going,
                                        Toast.LENGTH_SHORT
                                )
                                        .show()
                        }
                }
        }

        private fun setupLayoutsVisibility(status: Status, errorCode: String?) {
                binding.chatErrorView.root.visibility = View.GONE
                binding.chatEmptyView.root.visibility = View.GONE
                binding.loginLayout.root.visibility = View.GONE
                binding.progressDialog.visibility = View.GONE
                binding.paginationProgressDialog.visibility = View.GONE
                binding.swipeToRefreshLayout.isRefreshing = false

                when (status) {
                        Status.SUCCESS -> {
                                binding.dialogsList.visibility = View.VISIBLE
                                binding.swipeToRefreshLayout.visibility = View.VISIBLE
                        }
                        Status.EMPTY -> {
                                if (dialogsAdapter.isEmpty)
                                        binding.chatEmptyView.root.visibility = View.VISIBLE
                        }

                        Status.LOADING -> {
                                binding.progressDialog.visibility = View.VISIBLE
                        }

                        Status.LOADING_PAGINATION -> {
                                binding.paginationProgressDialog.visibility = View.VISIBLE
                                binding.dialogsList.visibility = View.VISIBLE
                        }

                        Status.REFRESHING -> {
                                binding.swipeToRefreshLayout.isRefreshing = true
                                binding.swipeToRefreshLayout.visibility = View.VISIBLE
                        }

                        Status.ERROR -> {
                                if (dialogsAdapter.isEmpty) {
                                        if (errorCode == AUTH_REQUIRED) {
                                                binding.loginLayout.root.visibility = View.VISIBLE
                                                AccountToken.setTokenIsInvalid(true)
                                        } else {
                                                binding.chatErrorView.root.visibility = View.VISIBLE
                                        }
                                }
                        }
                        else -> {}
                }
        }

        private fun setLoginVisibility() {
                if (AccountToken.isLoggedIn(context) && this.isAdded && !AccountToken.isSessionExpired(
                                requireContext()
                        ) && !AccountToken.isTokenInvalid()
                ) {
                        binding.loginLayout.root.visibility = View.GONE
                } else {
                        binding.loginLayout.root.visibility = View.VISIBLE
                        binding.chatErrorView.root.visibility = View.GONE
                        binding.chatEmptyView.root.visibility = View.GONE

                }
        }

        private fun startLogin() {
                val intent = Intent(context, AuthenticationActivity::class.java)
                val requestCode = AuthenticationActivity.REQUEST_SIGN_IN_MESSAGING
                requireActivity().startActivityForResult(intent, requestCode)
        }

        override fun onRefresh() {
                viewModel.fetchLatestConversations()
        }

        override fun updateList(hasUpdates: Boolean) {
                if (hasUpdates && isAdded) {
                        onRefresh()
                }
        }

        override fun resetConversationUnreadCount(conversationId: String) {
                if (isAdded) {
                        viewModel.resetUnreadCountById(conversationId)
                        dialogsAdapter.resetUnreadCountById(conversationId)
                }
        }

        fun scrollToPosition(position: Int) {
                if (isAdded)
                        binding.dialogsList.scrollToPosition(position)
        }

        private fun isTokenChanged(): Boolean {
                return (AccountToken.getCurrentToken(Avito.context)?.token != viewModel.initToken)
        }

        override fun format(date: Date?): String? {
                return if (DateFormatter.isToday(date)) {
                        DateFormatter.format(date, DateFormatter.Template.TIME)
                } else if (DateFormatter.isYesterday(date)) {
                        this.resources.getString(R.string.yesterday)
                } else if (DateFormatter.isCurrentYear(date)) {
                        DateFormatter.format(
                                date,
                                DateFormatter.Template.STRING_DAY_MONTH
                        )
                } else {
                        DateFormatter.format(
                                date,
                                DateFormatter.Template.STRING_DAY_MONTH_YEAR
                        )
                }
        }
}


