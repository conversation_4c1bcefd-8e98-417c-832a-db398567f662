package se.scmv.morocco.messaging.utils

import android.app.NotificationManager
import android.content.Context
import android.os.Build
import com.apollographql.apollo3.api.Optional
import com.google.firebase.messaging.FirebaseMessagingService
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import se.scmv.morocco.Avito
import se.scmv.morocco.RegisterFirebaseTokenMutation
import se.scmv.morocco.core.getRequestToken
import se.scmv.morocco.getApolloClientWithAuth
import se.scmv.morocco.messaging.common.Constants
import se.scmv.morocco.type.DevicePlatform
import se.scmv.morocco.type.FirebaseTokenInput
import se.scmv.morocco.utils.Log
import se.scmv.morocco.utils.Utils
import java.net.SocketTimeoutException

object MessagingUtils {

        @JvmStatic
        fun registerFirebaseToken(newToken: String, oldToken: String? = null) {
                try {
                        getRequestToken()?.let {
                                val coroutineScope =
                                        CoroutineScope(SupervisorJob() + Dispatchers.IO)
                                val errorHandler = CoroutineExceptionHandler { _, error ->
                                        Log.e(
                                                "registerFirebaseToken",
                                                "Error: ${error.localizedMessage}"
                                        )
                                }
                                coroutineScope.launch(errorHandler) {
                                        val token = FirebaseTokenInput(
                                                DevicePlatform.ANDROID,
                                                Optional.presentIfNotNull(
                                                        listOf(newToken)
                                                ),
                                                oldToken?.let {
                                                        Optional.presentIfNotNull(
                                                                listOf(it)
                                                        )
                                                } ?: Optional.Absent

                                        )

                                        getApolloClientWithAuth(it).mutation(
                                                RegisterFirebaseTokenMutation(token)
                                        ).toFlow().collect {
                                                Utils.savePreference(
                                                        Avito.context,
                                                        Constants.FIREBASE_MESSAGING_TOKEN, newToken
                                                )
                                        }
                                }
                        }
                } catch (se: SocketTimeoutException) {
                        android.util.Log.e("registerFirebaseToken", "Error: ${se.message}")
                } catch (ex: Throwable) {
                        android.util.Log.e("registerFirebaseToken", "Error ${ex.message}")
                }
        }

        @JvmStatic
        fun unRegisterFirebaseToken(oldToken: String) {
                try {
                        getRequestToken()?.let {
                                val coroutineScope =
                                        CoroutineScope(SupervisorJob() + Dispatchers.IO)
                                val errorHandler = CoroutineExceptionHandler { _, error ->
                                        Log.e(
                                                "unRegisterFirebaseToken",
                                                "Error: ${error.localizedMessage}"
                                        )
                                }
                                coroutineScope.launch(errorHandler) {
                                        val token = FirebaseTokenInput(
                                                DevicePlatform.ANDROID,
                                                Optional.Absent,
                                                Optional.presentIfNotNull(
                                                        listOf(oldToken)
                                                )
                                        )

                                        getApolloClientWithAuth(it).mutation(
                                                RegisterFirebaseTokenMutation(token)
                                        ).toFlow().collect {
                                                Utils.removePreference(
                                                        Avito.context,
                                                        Constants.FIREBASE_MESSAGING_TOKEN
                                                )
                                        }
                                }
                        }
                } catch (se: SocketTimeoutException) {
                        android.util.Log.e("registerFirebaseToken", "Error: ${se.message}")
                } catch (ex: Throwable) {
                        android.util.Log.e("registerFirebaseToken", "Error ${ex.message}")
                }
        }


        @OptIn(DelicateCoroutinesApi::class)
        @JvmStatic
        fun clearMessagingNotification(context: Context?) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M)
                        context?.let { it.getSystemService(FirebaseMessagingService.NOTIFICATION_SERVICE) as NotificationManager }
                                .also { notificationManager ->
                                        notificationManager?.activeNotifications?.forEach { notification ->
                                                if (notification.id == Constants.NOTIFICATION_ID) {
                                                        notificationManager.cancel(
                                                                notification.tag,
                                                                notification.id
                                                        )
                                                }
                                        }
                                }
        }
}