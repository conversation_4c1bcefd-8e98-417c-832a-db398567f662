package se.scmv.morocco.messaging.common

enum class Status {
    SUCCESS,
    ERROR,
    LOADING_PAGINATION,
    LOADING,
    EMPTY,
    REFRESHING,
    SUBSCRIPTION_EVENT,
    ADD_NEW_ITEM,
    UPDATE_ITEM
}

enum class MessageSender(val id: String) {
    ME("0"), PARTNER("1");

    companion object {
        fun getSenderId(isMine: Boolean): MessageSender {
            return if (isMine) ME else PARTNER
        }
    }
}