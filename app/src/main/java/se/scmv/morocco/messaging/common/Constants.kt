package se.scmv.morocco.messaging.common

object Constants {
        //error code
        const val AUTH_REQUIRED = "AUTH_REQUIRED"
        const val PAGE_SIZE = 10

        //Tagging event properties
        const val CONVERSATION_ID = "conversation_id"
        const val SUBCATEGORY_ID = "subcategory_id"
        const val CITY_ID = "city_id"
        const val AREA_ID = "area_id"
        const val AD_TYPE = "ad_type"

        const val ACCOUNT_TYPE_SHOP = "pro"
        const val ACCOUNT_TYPE_PRIVATE = "private"
        const val CHAT_LISTING = "chatlisting"
        const val FIRST_CONTACT = "first_contact"
        const val CHAT_CONVERSATION = "chatconversation"

        const val SEND_MESSAGE = "send_message"
        const val BLOCK_CONVERSATION = "block_conversation"
        const val UNBLOCK_CONVERSATION = "unblock_conversation"
        const val DELETE_CONVERSATION = "delete_conversation"

        const val SCREEN_VIEW = "screen_view"
        const val ELEMENT_CLICKED = "element_clicked"
        const val ELEMENT_NAME = "element_name"
        const val ELEMENT_SOURCE = "element_source"
        const val SELLER_TYPE = "seller_type"
        const val PAGE_NAME = "page_name"
        const val CONTENT_TYPE = "content_type"


        //prefs
        const val PERFORM_MESSAGING_LIST_REFRESH = "perform_messaging_list_refresh"
        const val FIREBASE_MESSAGING_TOKEN = "firebase_messaging_token"
        const val CURRENTLY_OPENED_CONVERSATION_ID = "currently_opened_conversation_id"

        //push notification
        const val NOTIFICATION_CHANNEL_ID = "se.scmv.morocco.notification.DIRECT_MESSAGES"
        const val NOTIFICATION_ID = 101
        const val MESSAGING_NOTIFICATION_TYPE = "new_chat_message"
        const val MESSAGING_NOTIFICATION_TYPE_LABEL_FOREGROUND = "notification_type"
        const val MESSAGING_NOTIFICATION_CONVERSATION_ID_LABEL_FOREGROUND = "conversation_id"
        const val MESSAGING_NOTIFICATION_TYPE_LABEL_BACKGROUND = "notification_type"
        const val MESSAGING_NOTIFICATION_CONVERSATION_ID_LABEL_BACKGROUND = "conversation_id"
        const val AD_VIEW_OPENED_FROM_MESSAGING = "ad_view_opened_from_messaging"


        val MESSAGING_SUPPORTED_FILE_MIME_TYPES = arrayOf(
                "application/pdf",
                "application/doc",
                "application/docx",
                "application/ms-doc",
                "application/xls",
                "application/xlsx",
                "application/ppt",
                "application/pptx",
                "application/odt",
                "application/ods",
        )
}