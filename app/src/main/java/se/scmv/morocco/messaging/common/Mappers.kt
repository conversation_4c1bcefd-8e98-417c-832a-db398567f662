package se.scmv.morocco.messaging.common

import android.content.Context
import com.stfalcon.chatkit.commons.models.MessageContentType
import com.stfalcon.chatkit.commons.models.Status
import se.scmv.morocco.GetChatConversationByIdQuery
import se.scmv.morocco.GetConversationByIdQuery
import se.scmv.morocco.GetConversationsListQuery
import se.scmv.morocco.R
import se.scmv.morocco.core.formattedDate
import se.scmv.morocco.messaging.model.Dialog
import se.scmv.morocco.messaging.model.Message
import se.scmv.morocco.messaging.model.User
import se.scmv.morocco.utils.isNotEmpty
import java.util.Date
import java.util.Locale
import java.util.UUID

fun getConversations(
        myChat: List<GetConversationsListQuery.Conversation?>?,
        context: Context
): ArrayList<Dialog> {
        val chats: ArrayList<Dialog> = ArrayList()
        myChat?.let {
                for (i in it.indices) {
                        if (it[i] != null)
                                chats.add(
                                        getConversation(
                                                it[i],
                                                it[i]?.lastMessage?.time.formattedDate(),
                                                context
                                        )
                                )
                }
        }

        return chats
}

fun getConversation(
        conversation: GetConversationsListQuery.Conversation?,
        lastMessageCreatedAt: Date,
        context: Context
): Dialog {
        return Dialog(
                id = conversation!!.id,
                dialogName = conversation.ad?.title
                        ?: context.getString(R.string.annonce_nest_plus_disponible),
                dialogPhoto = conversation.ad?.media?.defaultImage?.paths?.smallThumbnail,
                lastMessage = getMessage(
                        lastMessageCreatedAt,
                        conversation.lastMessage
                ),
                unreadCount = conversation.unreadCount,
                partnerName = if (conversation.partner?.__typename == "StoreProfile") conversation.partner!!.onStoreProfile?.name.toString() else conversation.partner?.onPrivateProfile?.name.toString(),
                avatarUrl = if (conversation.partner?.__typename == "StoreProfile") conversation.partner!!.onStoreProfile?.logo?.defaultPath.toString() else "",
                blocked = conversation.isBlockedByMe
        )
}

fun getConversation(
        conversation: GetConversationByIdQuery.GetMyChatConversation?,
        lastMessageCreatedAt: Date,
        context: Context
): Dialog {
        return Dialog(
                id = conversation!!.id,
                dialogName = conversation.ad?.title
                        ?: context.getString(R.string.annonce_nest_plus_disponible),
                dialogPhoto = conversation.ad?.media?.defaultImage?.paths?.smallThumbnail,
                lastMessage = getMessage(
                        lastMessageCreatedAt,
                        conversation.lastMessage
                ),
                unreadCount = conversation.unreadCount,
                partnerName = if (conversation.partner?.__typename == "StoreProfile") conversation.partner!!.onStoreProfile?.name.toString() else conversation.partner?.onPrivateProfile?.name.toString(),
                avatarUrl = if (conversation.partner?.__typename == "StoreProfile") conversation.partner!!.onStoreProfile?.logo?.defaultPath.toString() else "",
                blocked = conversation.isBlockedByMe
        )
}

fun getMessage(
        date: Date,
        lastMessage: GetConversationsListQuery.LastMessage?
): Message {
        return Message(
                id = lastMessage?.id ?: "",
                user = User("", "", "", false),
                text = getLastMessageTextWithAttachment(lastMessage),
                createdAt = date,
                status = Status.SUCCESS
        )
}

fun getLastMessageTextWithAttachment(lastMessage: GetConversationsListQuery.LastMessage?): String {
        val _attachement =
                lastMessage?.attachment?.type?.substringBefore("/")
                        ?.replaceFirstChar {
                                if (it.isLowerCase()) it.titlecase(
                                        Locale.getDefault()
                                ) else it.toString()
                        } ?: ""
        val _lastMessage = lastMessage?.text ?: ""

        return if (_attachement.isNotEmpty() && _lastMessage.isNotEmpty()) {
                _attachement + " · " + _lastMessage
        } else {
                _attachement + _lastMessage
        }


}

fun getMessage(
        date: Date,
        lastMessage: GetConversationByIdQuery.LastMessage?
): Message {
        return Message(
                id = lastMessage?.id ?: "",
                user = User("", "", "", false),
                text = lastMessage?.text ?: "",
                createdAt = date,
                status = Status.SUCCESS
        )
}

fun createMessageFromSubscription(
        id: String,
        date: Date,
        lastMessage: String
): Message {
        return Message(
                id = id,
                user = User("", "", "", false),
                text = lastMessage,
                createdAt = date,
                status = Status.SUCCESS
        )
}

fun getMessages(
        messages: List<GetChatConversationByIdQuery.Message>,
        context: Context
): List<Message> {
        val messagesList: MutableList<Message> = mutableListOf()
        for (msg in messages) {
                val _message = getMessage(msg, msg.time.formattedDate(), context)
                messagesList.add(_message)
                if (msg.attachment != null && msg.text.isNotEmpty()) {
                        if (msg.attachment!!.type.contains("image")) {
                                var ms = msg.copy(id = msg.id + "img")
                                val _messageImg =
                                        getAttachmentMessage(ms, msg.time.formattedDate())
                                messagesList.add(_messageImg)
                        }
                }
        }
        return messagesList
}

fun getMessage(
        message: GetChatConversationByIdQuery.Message?,
        lastMessageCreatedAt: Date,
        context: Context

): Message {
        return Message(
                id = message?.id ?: UUID.randomUUID().toString(),
                user = User(
                        MessageSender.getSenderId(message != null && message.isMine).id,
                        "",
                        "",
                        false
                ),
                text = message?.text.toString(),
                createdAt = lastMessageCreatedAt,
                status = com.stfalcon.chatkit.commons.models.Status.SUCCESS
        ).apply {
                message?.attachment?.let {
                        if (it.type.contains("image")) {
                                image = Message.Image(
                                        it.url,
                                        MessageContentType.Type.REMOTE
                                )
                        } else {
                                file = Message.File(
                                        it.url,
                                        context.getString(R.string.attachment)
                                )
                        }
                }
        }
}

fun getAttachmentMessage(
        message: GetChatConversationByIdQuery.Message?,
        lastMessageCreatedAt: Date

): Message {
        val _message = Message(
                id = message?.id ?: "",
                user = User(
                        MessageSender.getSenderId(message != null && message.isMine).id,
                        "",
                        "",
                        false
                ),
                text = message?.text.toString(),
                createdAt = lastMessageCreatedAt,
                status = com.stfalcon.chatkit.commons.models.Status.SUCCESS
        )


        return _message
}