package se.scmv.morocco.messaging.ui.conversation

import android.app.NotificationManager
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.apollographql.apollo3.api.ApolloResponse
import com.apollographql.apollo3.api.DefaultUpload
import com.apollographql.apollo3.api.Optional
import com.apollographql.apollo3.api.content
import com.google.firebase.messaging.FirebaseMessagingService
import com.stfalcon.chatkit.commons.models.MessageContentType
import com.stfalcon.chatkit.commons.models.Status
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import se.scmv.morocco.Avito
import se.scmv.morocco.BlockConversationMutation
import se.scmv.morocco.ClearConversationMutation
import se.scmv.morocco.GetChatConversationByIdQuery
import se.scmv.morocco.InboxChattingSubscription
import se.scmv.morocco.MakeConversationAsReadMutation
import se.scmv.morocco.SendChatMessageMutation
import se.scmv.morocco.UnblockConversationMutation
import se.scmv.morocco.core.formattedDate
import se.scmv.morocco.core.getRequestToken
import se.scmv.morocco.core.presentIfAbsent
import se.scmv.morocco.getApolloClientWithAuth
import se.scmv.morocco.getApolloClientWithAuthWebSocket
import se.scmv.morocco.login.models.AccountToken
import se.scmv.morocco.messaging.analytics.MessagingAnalyticsUtils
import se.scmv.morocco.messaging.common.Constants
import se.scmv.morocco.messaging.common.Constants.PAGE_SIZE
import se.scmv.morocco.messaging.common.MessageSender
import se.scmv.morocco.messaging.common.Resource
import se.scmv.morocco.messaging.model.Message
import se.scmv.morocco.messaging.model.User
import se.scmv.morocco.type.SendChatMessageInput
import se.scmv.morocco.utils.Utils
import se.scmv.morocco.utils.isNotEmpty
import se.scmv.morocco.utils.isNotNull
import java.io.File
import java.net.SocketTimeoutException
import java.util.UUID

class ConversationViewModel : ViewModel() {

        private val _chatConversation =
                MutableLiveData<Resource<GetChatConversationByIdQuery.GetMyChatConversation>>()
        val chatConversation: LiveData<Resource<GetChatConversationByIdQuery.GetMyChatConversation>> =
                _chatConversation

        private val _sendMessageUiState = MutableSharedFlow<Resource<Message>>()
        val sendMessageUiState: SharedFlow<Resource<Message>> = _sendMessageUiState

        private val _subscriptionMessage = MutableSharedFlow<Message>()
        val subscriptionMessage: SharedFlow<Message> = _subscriptionMessage

        private val _conversationAction = MutableSharedFlow<ConversationAction>()
        val conversationAction: SharedFlow<ConversationAction> = _conversationAction

        private val unsentMessages = mutableListOf<Message>()

        var _conversationId: String? = null
        var blocked: Boolean = false
        var _oldestMessageTime: Optional<String?> = Optional.Absent

        var currentIndex: Int = -1


        var hasToMarkConversationAsRead = false

        private val _saveToSharedPref = MutableLiveData("")
        val saveToSharedPref: LiveData<String> = _saveToSharedPref


        private var sendMessageJob: Job? = null

        init {
                if (AccountToken.isLoggedIn(Avito.context) && !AccountToken.isSessionExpired(Avito.context)) {
                        _chatConversation.value = Resource.loading()
                        subscribe()
                }
        }

        fun getChatConversationById(
                id: String,
                pageSize: Int = PAGE_SIZE
        ) {
                _conversationId = id
                _oldestMessageTime = Optional.Absent
                _chatConversation.postValue(Resource.loading(null))
                getChatConversation(id, pageSize)
        }

        private fun getChatConversation(
                id: String,
                pageSize: Int = PAGE_SIZE,
                beforeTime: Optional<String?> = Optional.Absent,
                afterTime: Optional<String?> = Optional.Absent
        ) {
                viewModelScope.launch(Dispatchers.IO) {
                        AccountToken.getCurrentToken(Avito.context)?.let { accountToken ->
                                fetchChatConversationById(
                                        id,
                                        pageSize,
                                        beforeTime = beforeTime,
                                        afterTime = afterTime,
                                        accountToken.requestToken
                                ).catch { }.collect {
                                        if (it?.data?.getMyChatConversation?.messages.isNotEmpty()) {
                                                it?.data?.getMyChatConversation?.let { conversation ->
                                                        _chatConversation.postValue(
                                                                Resource.addNewItem(
                                                                        conversation
                                                                )
                                                        )
                                                }
                                                _oldestMessageTime = Optional.presentIfNotNull(
                                                        it?.data?.getMyChatConversation?.messages?.last()?.time
                                                )
                                                it?.data?.getMyChatConversation?.unreadCount?.let { unreadCount ->
                                                        if (unreadCount > 0)
                                                                _conversationId?.let { id ->
                                                                        markConversationAsRead(
                                                                                id
                                                                        )
                                                                }

                                                }
                                        } else {
                                                it?.data?.getMyChatConversation?.let { conversation ->
                                                        _chatConversation.postValue(
                                                                Resource.success(
                                                                        conversation
                                                                )
                                                        )

                                                }


                                        }
                                        _conversationId?.let { id -> clearNotification(id) }
                                        blocked = it?.data?.getMyChatConversation?.isBlockedByMe
                                                ?: false
                                }
                        }
                }
        }

        fun conversationIdMatch(id: String): Boolean {
                return id == _conversationId
        }

        fun saveCurrentConversationId() {
                Utils.savePreference(
                        Avito.context,
                        Constants.CURRENTLY_OPENED_CONVERSATION_ID,
                        _conversationId
                )
        }

        fun resetCurrentConversationId() {
                Utils.removePreference(Avito.context, Constants.CURRENTLY_OPENED_CONVERSATION_ID)
        }


        fun clearNotification(conversationId: String) {
                Avito.context?.let { it.getSystemService(FirebaseMessagingService.NOTIFICATION_SERVICE) as NotificationManager }
                        .also { notificationManager ->
                                notificationManager?.cancel(
                                        conversationId,
                                        Constants.NOTIFICATION_ID
                                )
                        }

        }

        private fun fetchChatConversationById(
                id: String,
                pageSize: Int = PAGE_SIZE,
                beforeTime: Optional<String?> = Optional.Absent,
                afterTime: Optional<String?> = Optional.Absent,
                requestToken: String
        ): Flow<ApolloResponse<GetChatConversationByIdQuery.Data>?> = getApolloClientWithAuth(
                requestToken
        ).query(GetChatConversationByIdQuery(id, pageSize, beforeTime, afterTime)).toFlow()

        @OptIn(DelicateCoroutinesApi::class)
        private fun markConversationAsRead(id: String) {
                try {
                        getRequestToken()?.let {
                                val coroutineScope =
                                        CoroutineScope(SupervisorJob() + Dispatchers.IO)
                                val errorHandler = CoroutineExceptionHandler { _, error ->
                                        se.scmv.morocco.utils.Log.e(
                                                "markConversationAsRead",
                                                "Error: ${error.localizedMessage}"
                                        )
                                }
                                coroutineScope.launch(errorHandler) {

                                        getApolloClientWithAuth(it).mutation(
                                                MakeConversationAsReadMutation(id)
                                        ).execute()
                                }
                        }
                } catch (se: SocketTimeoutException) {
                        Log.e("markConversationAsRead", "Error: ${se.message}")
                } catch (ex: Throwable) {
                        Log.e("markConversationAsRead", "Error ${ex.message}")
                }

        }

        private fun subscribe() {
                viewModelScope.launch(Dispatchers.IO) {
                        try {
                                //_subscribed.postValue(true)
                                getApolloClientWithAuthWebSocket().subscription(
                                        InboxChattingSubscription(AccountToken.getCurrentToken(Avito.context).token)
                                ).toFlow().catch {
                                        Log.d("TAG----", "error: ")
                                }.collect {
                                        onSubscriptionMessageReceived(it)
                                }
                        } catch (e: Exception) {
                                Log.d("TAG----", "error: ")
                                //_subscribed.postValue(false)
                        }
                }
        }

        private fun onSubscriptionMessageReceived(response: ApolloResponse<InboxChattingSubscription.Data>) {
                if (response.data.isNotNull()) {
                        response.data?.subscribe?.event?.onMessageReceived?.let { receivedMessage ->
                                viewModelScope.launch {
                                        _subscriptionMessage.emit(
                                                Message(
                                                        conversationId = receivedMessage.conversationId,
                                                        id = receivedMessage.id,
                                                        user = User(
                                                                MessageSender.PARTNER.id,
                                                                "",
                                                                "",
                                                                false
                                                        ),
                                                        text = receivedMessage.text ?: "",
                                                        createdAt = receivedMessage.time.formattedDate(),
                                                        status = Status.SUCCESS
                                                ).apply {
                                                        receivedMessage.attachment?.let {
                                                                image = Message.Image(
                                                                        it.url,
                                                                        type = MessageContentType.Type.REMOTE
                                                                )
                                                        }
                                                }
                                        )
                                        hasToMarkConversationAsRead = currentIndex == 0
                                }
                        }
                }
        }

        fun loadMoreMessages() {
                _chatConversation.postValue(Resource.loadingPagination(null))
                if (_oldestMessageTime != Optional.Absent)
                        _conversationId?.let {
                                getChatConversation(
                                        it,
                                        PAGE_SIZE,
                                        beforeTime = _oldestMessageTime
                                )
                        }
        }

        fun onSubmitChatMessage(text: String) {
                submitChatMessage(newSendingMessage(text))
        }

        fun onImageAdded(imageFile: File) {
                val message = newSendingMessage("").apply {
                        image = Message.Image(
                                imageFile.absolutePath,
                                MessageContentType.Type.LOCAL
                        )
                }
                submitChatMessage(message)
        }

        fun onFileAdded(file: File, fileName: String) {
                val message = newSendingMessage("").apply {
                        this.file = Message.File(
                                path = file.absolutePath,
                                name = fileName
                        )
                }
                submitChatMessage(message)
        }

        fun cancelSendMessageJob() {
                sendMessageJob?.cancel()
        }

        fun onResendChatMessage(id: String) {
                unsentMessages.find {
                        it.id == id
                }?.also {
                        submitChatMessage(it.apply { status = Status.SENDING })
                }
        }

        private fun submitChatMessage(
                message: Message
        ) {

                getRequestToken()?.let {
                        val errorHandler = CoroutineExceptionHandler { _, error ->
                                se.scmv.morocco.utils.Log.e(
                                        "submitChatMessage",
                                        "Error: ${error.localizedMessage}"
                                )
                        }
                        viewModelScope.launch(errorHandler + Dispatchers.IO) {
                                _sendMessageUiState.emit(
                                        Resource.loading(
                                                data = message
                                        )
                                )

                                getApolloClientWithAuth(it).mutation(
                                        SendChatMessageMutation(message.toSendChatMessageInput())
                                ).toFlow()
                                        .catch {
                                                postSendMessageError(
                                                        message = message.apply {
                                                                status = Status.ERROR
                                                        },
                                                        exception = MessageNotSentException(),
                                                )
                                        }.collect {
                                                it.data?.sendChatMessage?.let { chatMessage ->
                                                        if (chatMessage.success) {
                                                                _sendMessageUiState.emit(
                                                                        Resource.success(
                                                                                data = message.apply {
                                                                                        createdAt =
                                                                                                chatMessage.conversation.message.time.formattedDate()
                                                                                        status =
                                                                                                Status.SUCCESS
                                                                                        chatMessage.conversation.message.attachment?.let { attachment ->
                                                                                                if (attachment.type.contains(
                                                                                                                "image"
                                                                                                        )
                                                                                                ) {
                                                                                                        message.image =
                                                                                                                Message.Image(
                                                                                                                        url = attachment.url,
                                                                                                                        type = MessageContentType.Type.REMOTE
                                                                                                                )
                                                                                                } else {
                                                                                                        message.file =
                                                                                                                Message.File(
                                                                                                                        path = attachment.url,
                                                                                                                        name = attachment.type // TODO piece jointe
                                                                                                                )
                                                                                                }
                                                                                        }
                                                                                }
                                                                        )
                                                                )
                                                                unsentMessages.remove(message)
                                                        } else {
                                                                postSendMessageError(
                                                                        message = message.apply {
                                                                                status =
                                                                                        Status.ERROR
                                                                        },
                                                                        exception = MessageNotSentException(),
                                                                )
                                                        }
                                                } ?: kotlin.run {
                                                        postSendMessageError(
                                                                message = message.apply {
                                                                        status = Status.ERROR
                                                                },
                                                        )
                                                }
                                        }
                        }
                }.also {
                        sendMessageJob = it
                }
        }

        fun firstVisibleItemChanged(index: Int) {
                currentIndex = index
        }

        private fun postSendMessageError(
                message: Message,
                exception: Exception = Exception(),
        ) {
                unsentMessages.presentIfAbsent(message)
                viewModelScope.launch {
                        _sendMessageUiState.emit(
                                Resource.error(
                                        exception = exception,
                                        msg = "",
                                        data = message
                                )
                        )
                }
        }

        fun clearConversation(isItFromAdDetail: Boolean = false) {
                _conversationId?.let { clearConversation(it, isItFromAdDetail) }
        }

        fun blockConversation(isItFromAdDetail: Boolean = false) {
                _conversationId?.let { blockConversation(it, isItFromAdDetail) }
        }

        fun unblockConversation(isItFromAdDetail: Boolean = false) {
                _conversationId?.let { unblockConversation(it, isItFromAdDetail) }
        }

        private fun clearConversation(id: String, isItFromAdDetail: Boolean = false) {
                getRequestToken()?.let {
                        val errorHandler = CoroutineExceptionHandler { _, error ->
                                se.scmv.morocco.utils.Log.e(
                                        "clearConversation",
                                        "Error: ${error.localizedMessage}"
                                )
                        }
                        viewModelScope.launch(errorHandler) {

                                getApolloClientWithAuth(it).mutation(
                                        ClearConversationMutation(id)
                                ).toFlow()
                                        .catch {
                                                _conversationAction.emit(
                                                        ConversationAction.ClearConv(
                                                                id = id,
                                                                cleared = false
                                                        )
                                                )
                                        }
                                        .collect {
                                                _conversationAction.emit(
                                                        ConversationAction.ClearConv(
                                                                id = id,
                                                                cleared = it.data?.clearConversation?.success
                                                                        ?: false
                                                        )
                                                )

                                                if (isItFromAdDetail)
                                                        _saveToSharedPref.value = "$id:clear"
                                                MessagingAnalyticsUtils.trackDeleteConversation(
                                                        Constants.CHAT_CONVERSATION
                                                )


                                        }
                        }
                }
        }

        private fun blockConversation(id: String, isItFromAdDetail: Boolean = false) {
                getRequestToken()?.let {
                        val errorHandler = CoroutineExceptionHandler { _, error ->
                                se.scmv.morocco.utils.Log.e(
                                        "blockConversation",
                                        "Error: ${error.localizedMessage}"
                                )
                        }
                        viewModelScope.launch(errorHandler) {

                                getApolloClientWithAuth(it).mutation(
                                        BlockConversationMutation(id)
                                ).toFlow().catch {
                                        _conversationAction.emit(
                                                ConversationAction.BlockConv(
                                                        id = id,
                                                        blocked = false
                                                )
                                        )
                                }.collect {
                                        it.data?.blockConversation?.let { blockConversation ->
                                                _conversationAction.emit(
                                                        ConversationAction.BlockConv(
                                                                id = id,
                                                                blocked = blockConversation.success
                                                        )
                                                )
                                                blocked = blockConversation.success
                                                if (isItFromAdDetail && blocked)
                                                        _saveToSharedPref.value = "$id:block"
                                                MessagingAnalyticsUtils.trackBlockConversation(
                                                        true,
                                                        Constants.CHAT_CONVERSATION
                                                )

                                        }
                                }
                        }
                }
        }

        private fun unblockConversation(id: String, isItFromAdDetail: Boolean = false) {
                getRequestToken()?.let {
                        val errorHandler = CoroutineExceptionHandler { _, error ->
                                se.scmv.morocco.utils.Log.e(
                                        "unblockConversation",
                                        "Error: ${error.localizedMessage}"
                                )
                        }
                        viewModelScope.launch(errorHandler) {

                                getApolloClientWithAuth(it).mutation(
                                        UnblockConversationMutation(id)
                                ).toFlow().catch {
                                        _conversationAction.emit(
                                                ConversationAction.UnblockConv(
                                                        id = id,
                                                        unblocked = false
                                                )
                                        )
                                }.collect {
                                        it.data?.unblockConversation?.let { unblockConversation ->
                                                _conversationAction.emit(
                                                        ConversationAction.UnblockConv(
                                                                id = id,
                                                                unblocked = unblockConversation.success
                                                        )
                                                )
                                                blocked = !unblockConversation.success
                                                if (isItFromAdDetail && !blocked)
                                                        _saveToSharedPref.value = "$id:unblock"
                                                MessagingAnalyticsUtils.trackBlockConversation(
                                                        false,
                                                        Constants.CHAT_CONVERSATION
                                                )

                                        }
                                }
                        }
                }
        }

        private fun Message.toSendChatMessageInput(): SendChatMessageInput {
                val attachmentPath = imageUrl ?: filePath
                val attachment: File? = attachmentPath?.let {
                        File(it)
                }
                val attachmentContentType = attachment?.let {
                        if (imageUrl.isNotNull()) {
                                "image/${it.extension}"
                        } else if (filePath.isNotNull()) {
                                if (it.extension == "txt") {
                                        "text/plain"
                                } else {
                                        "application/${it.extension}"
                                }
                        } else "application/octet-stream"
                } ?: "application/octet-stream"
                return SendChatMessageInput(
                        conversationId = Optional.presentIfNotNull(_conversationId),
                        text = Optional.presentIfNotNull(text),
                        attachment = attachment?.let {
                                Optional.presentIfNotNull(
                                        DefaultUpload.Builder()
                                                .content(it)
                                                .contentType(attachmentContentType)
                                                .fileName(it.name)
                                                .build()
                                )
                        } ?: Optional.Absent
                )
        }

        private fun newSendingMessage(text: String): Message {
                return Message(
                        id = UUID.randomUUID().toString(),
                        text = text,
                        user = User(MessageSender.ME.id, "", "", false),
                        status = Status.SENDING,
                )
        }

        fun isUnsent(message: Message): Boolean {
                return unsentMessages.contains(message)
        }

        /*fun onDeleteChatMessage(message: Message) {
                unsentMessages.remove(message)
        }*/


        override fun onCleared() {
                if (hasToMarkConversationAsRead)
                        _conversationId?.let { markConversationAsRead(it) }
                super.onCleared()
        }

        fun hasToMarkConversationAsRead() {
                hasToMarkConversationAsRead = true
        }

}

class MessageNotSentException : Exception()

sealed class ConversationAction {
        class ClearConv(val id: String, val cleared: Boolean) : ConversationAction()
        class BlockConv(val id: String, val blocked: Boolean) : ConversationAction()
        class UnblockConv(val id: String, val unblocked: Boolean) : ConversationAction()
}