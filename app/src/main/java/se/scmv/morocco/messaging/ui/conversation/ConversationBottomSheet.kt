package se.scmv.morocco.messaging.ui.conversation

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.widget.PopupMenu
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.bumptech.glide.Glide
import com.stfalcon.chatkit.commons.ImageLoader
import com.stfalcon.chatkit.messages.MessageInput.AttachmentsListener
import com.stfalcon.chatkit.messages.MessageInput.GONE
import com.stfalcon.chatkit.messages.MessageInput.InputListener
import com.stfalcon.chatkit.messages.MessageInput.TypingListener
import com.stfalcon.chatkit.messages.MessageInput.VISIBLE
import com.stfalcon.chatkit.messages.MessagesListAdapter
import id.zelory.compressor.Compressor
import id.zelory.compressor.constraint.size
import kotlinx.coroutines.Dispatchers
import se.scmv.morocco.GetChatConversationByIdQuery
import se.scmv.morocco.R
import se.scmv.morocco.activities.MainActivity
import se.scmv.morocco.utils.Keys
import se.scmv.morocco.core.formattedDate
import se.scmv.morocco.core.setupChatkitImageLoader
import se.scmv.morocco.core.showConfirmationDialog
import se.scmv.morocco.databinding.FragmentMessagingConversationBinding
import se.scmv.morocco.messaging.analytics.MessagingAnalyticsUtils
import se.scmv.morocco.messaging.analytics.MessagingAnalyticsUtils.trackConversationScreen
import se.scmv.morocco.messaging.common.Attachment
import se.scmv.morocco.messaging.common.MessageSender
import se.scmv.morocco.messaging.common.Status
import se.scmv.morocco.messaging.common.UploadAttachmentBaseBottomSheet
import se.scmv.morocco.messaging.common.currentOpenedConversationId
import se.scmv.morocco.messaging.common.getMessages
import se.scmv.morocco.messaging.model.Message
import se.scmv.morocco.messaging.model.User
import se.scmv.morocco.messaging.ui.messaginglist.ChatListUpdateListener
import se.scmv.morocco.utils.Keys.PAGINATION_LOADER
import se.scmv.morocco.utils.Utils
import se.scmv.morocco.utils.isNotEmpty
import java.io.File

private const val CONVERSATION_ID = "se.scmv.morocco.messaging.ui.conversation.conversationId"
private const val fromAdDetails_ = "se.scmv.morocco.messaging.ui.conversation.fromAdDetails"

class ConversationBottomSheet(
        private val conversationActionsListener: ConversationActionsListener? = null,
        private val chatListUpdateListener: ChatListUpdateListener? = null
) : UploadAttachmentBaseBottomSheet(), InputListener, AttachmentsListener,
        TypingListener,
        MessagesListAdapter.OnLoadMoreListener,
        MessagesListAdapter.OnFirstVisibleItemChangedListener,
        MessagesListAdapter.OnMessageViewClickListener<Message> {

        companion object {
                fun newInstance(
                        conversationId: String,
                        fromAdDetails: Boolean = false,
                        conversationActionsListener: ConversationActionsListener? = null,
                        chatListUpdateListener: ChatListUpdateListener? = null
                ): ConversationBottomSheet {
                        currentOpenedConversationId = conversationId
                        val args = Bundle()
                        args.putString(CONVERSATION_ID, conversationId)
                        val fragment = ConversationBottomSheet(
                                conversationActionsListener = conversationActionsListener,
                                chatListUpdateListener = chatListUpdateListener
                        )
                        args.putBoolean(fromAdDetails_, fromAdDetails)
                        fragment.arguments = args
                        return fragment
                }
        }

        private var _binding: FragmentMessagingConversationBinding? = null
        private val binding: FragmentMessagingConversationBinding get() = _binding!!

        private lateinit var imageLoader: ImageLoader
        private lateinit var messagesAdapter: MessagesListAdapter<Message>

        private val viewModel: ConversationViewModel by viewModels()

        private var hasPerformedUpdates = false
        private var isItFromAdDetail = false
        private var adsListId = ""

        override fun onCreateView(
                inflater: LayoutInflater, container: ViewGroup?,
                savedInstanceState: Bundle?
        ): View {
                _binding = DataBindingUtil.inflate(
                        inflater,
                        R.layout.fragment_messaging_conversation,
                        container,
                        false
                )
                setupImageLoader()
                initAdapter()
                return binding.root
        }

        override fun onResume() {
                super.onResume()
                viewModel.saveCurrentConversationId()
        }

        override fun onPause() {
                super.onPause()
                viewModel.resetCurrentConversationId()
        }

        override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
                super.onViewCreated(view, savedInstanceState)
                binding.lifecycleOwner = viewLifecycleOwner

                arguments?.getString(CONVERSATION_ID)?.let {
                        viewModel.getChatConversationById(it)
                }
                arguments?.getBoolean(fromAdDetails_)?.let {
                        isItFromAdDetail = it
                }


                binding.setupRecyclerView()
                binding.setupInput()
                binding.setUpToolbar()
                binding.scrollDown.setOnClickListener {
                        binding.scrollDown.visibility = View.GONE
                        messagesAdapter.scrollToPosition(0)
                        viewModel.hasToMarkConversationAsRead()
                }

                binding.adsDetailContainer.setOnClickListener {
                        if (adsListId.isNotEmpty())
                                openAd(adsListId)

                }

                initObservers()
                trackConversationScreen()
        }

        override fun onDestroyView() {
                chatListUpdateListener?.updateList(hasPerformedUpdates)
                if (viewModel.hasToMarkConversationAsRead) {
                        viewModel._conversationId?.let {
                                chatListUpdateListener?.resetConversationUnreadCount(
                                        it
                                )
                        }
                }
                _binding = null
                deleteTemporaryFile(requireContext())
                super.onDestroyView()
        }


        private fun setupImageLoader() {
                imageLoader = setupChatkitImageLoader()
        }

        private fun initAdapter() {
                messagesAdapter = MessagesListAdapter(MessageSender.ME.id, imageLoader)
                messagesAdapter.setLoadMoreListener(this)
                messagesAdapter.setFirstVisibleItemListener(this)
                messagesAdapter.registerViewClickListener(R.id.cancelChatImage, this)
                messagesAdapter.registerViewClickListener(R.id.cancel_image_message_loading, this)
                messagesAdapter.registerViewClickListener(R.id.messageStatus, this)
                messagesAdapter.registerViewClickListener(R.id.fileMessageContainer, this)
        }

        private fun FragmentMessagingConversationBinding.setupInput() {
                input.setInputListener(this@ConversationBottomSheet)
                input.setTypingListener(this@ConversationBottomSheet)
                input.setAttachmentsListener(this@ConversationBottomSheet)
                messagesAdapter.setOnMessageClickListener {
                        if (!it.imageUrl.isNullOrEmpty() && it.status == com.stfalcon.chatkit.commons.models.Status.SUCCESS) {
                                displayImage(it.imageUrl)
                        }
                }

        }

        private fun displayImage(imageUrl: String?) {
                imageUrl?.let {
                        PreviewImageBottomSheet.newInstance(it).show(
                                childFragmentManager,
                                PreviewImageBottomSheet::class.java.canonicalName
                        )
                }
        }

        private fun FragmentMessagingConversationBinding.setupRecyclerView() {
                messagesList.setAdapter(messagesAdapter)
        }

        private fun FragmentMessagingConversationBinding.setUpToolbar() {
                toolbar.setNavigationOnClickListener {
                        dismiss()
                }
                menuButton.setOnClickListener {
                        onAdMenuClicked(it)
                }
        }

        private fun initObservers() {
                lifecycleScope.launchWhenStarted {
                        viewModel.subscriptionMessage.collect { it: Message ->
                                var scroll = true

                                if (viewModel.currentIndex != 0) {
                                        binding.scrollDown.visibility = View.VISIBLE
                                        scroll = false
                                }
                                if (it.conversationId == viewModel._conversationId) {
                                        if (it.hasAttachment() && it.text.isNotEmpty()) {
                                                val textMessage = Message(
                                                        id = it.id + "txt",
                                                        user = it.user,
                                                        text = it.text,
                                                        createdAt = it.createdAt,
                                                        status = it.status
                                                )
                                                messagesAdapter.addToStart(textMessage, scroll)
                                        }
                                        messagesAdapter.addToStart(it, scroll)
                                }

                        }
                }

                viewModel.chatConversation.observe(viewLifecycleOwner) {
                        setupLayoutsVisibility(it.status, it.errorCode)
                        messagesAdapter.deleteById(PAGINATION_LOADER)
                        if (it.status == Status.LOADING_PAGINATION) {
                                messagesAdapter.addToEnd(
                                        listOf(
                                                Message(
                                                        PAGINATION_LOADER,
                                                        User(
                                                                MessageSender.PARTNER.id,
                                                                "",
                                                                "",
                                                                false
                                                        ),
                                                        "",
                                                        viewModel._oldestMessageTime.getOrNull()
                                                                .formattedDate(),
                                                        status = com.stfalcon.chatkit.commons.models.Status.SUCCESS,
                                                        paginationLoaderVisible = true
                                                )
                                        ), false
                                )
                        }

                        it.data?.let { data: GetChatConversationByIdQuery.GetMyChatConversation ->
                                messagesAdapter.addToEnd(
                                        getMessages(
                                                data.messages.filterNotNull(),
                                                requireContext()
                                        ), false
                                )
                                binding.input.visibility = if (data.isBlockedByMe) GONE else VISIBLE
                                data.ad?.let { ad ->
                                        setAdDetailSticky(
                                                ad.title,
                                                ad.price,
                                                ad.media.defaultImage?.paths,
                                                ad.listId
                                        )
                                }
                                if (data.ad == null)
                                        binding.adsTitle.text =
                                                getString(R.string.annonce_nest_plus_disponible)


                        }
                }

                lifecycleScope.launchWhenStarted {
                        viewModel.sendMessageUiState.collect {
                                when (it.status) {
                                        Status.LOADING -> onSendMessageLoading(it.message, it.data)
                                        Status.SUCCESS -> it.data?.let { data ->
                                                onSendMessageSuccess(data)
                                        }
                                        Status.ERROR -> onSendMessageError(it.message, it.data)
                                        else -> {}
                                }
                        }
                }
                lifecycleScope.launchWhenStarted {
                        viewModel.conversationAction.collect {
                                onConversationUiStateChanged(it)
                        }
                }
                viewModel.saveToSharedPref.observe(viewLifecycleOwner) {
                        if (!it.isNullOrEmpty())
                                Utils.addValueToSetPreference(
                                        requireContext(),
                                        Utils.CONVERSATIONS_ACTION,
                                        it
                                )
                }
        }


        private fun setAdDetailSticky(
                title: String?,
                price: GetChatConversationByIdQuery.Price?,
                paths: GetChatConversationByIdQuery.Paths?,
                listId: String
        ) {
                binding.adsTitle.text = title ?: getString(R.string.annonce_nest_plus_disponible)
                binding.adsPrix.text =
                        price?.withCurrency ?: getString(R.string.price_not_specified)

                paths?.smallThumbnail?.let {
                        if (it.isNotEmpty())
                                Glide.with(requireContext())
                                        .load(it)
                                        .into(binding.adsPicture)
                }


                if (listId.isNotEmpty())
                        adsListId = listId

        }

        private fun setupLayoutsVisibility(status: Status, errorCode: String?) {
                binding.isLoading = status == Status.LOADING
        }

        override fun onSubmit(input: CharSequence?): Boolean {
                if (input.isNullOrEmpty()) return false
                viewModel.onSubmitChatMessage(input.toString())
                hasPerformedUpdates = true
                return true
        }

        override fun onAddAttachments() {
                pickImage()
        }

        override fun onAttachmentPicked(attachment: Attachment) {
                when (attachment) {
                        is Attachment.Image -> lifecycleScope.launchWhenStarted {
                                File(attachment.path).apply {
                                        Compressor.compress(
                                                requireContext(),
                                                this,
                                                Dispatchers.IO
                                        ) {
                                                size(4_194_304) // maxFileSize 4 MB
                                        }.also { viewModel.onImageAdded(it) }
                                }
                        }
                        is Attachment.File -> viewModel.onFileAdded(
                                File(attachment.path),
                                attachment.name
                        )
                }
        }

        override fun onStartTyping() {}

        override fun onStopTyping() {}

        override fun onLoadMore(page: Int, totalItemsCount: Int) {
                viewModel.loadMoreMessages()
        }

        override fun onMessageViewClick(view: View?, message: Message?) {
                when (view?.id) {
                        R.id.messageStatus -> onTextMessageStatusClicked(message)
                        R.id.cancelChatImage -> onImageMessageCancelClicked(message)
                        R.id.cancel_image_message_loading -> onImageMessageCancelClicked(message)
                        R.id.fileMessageContainer -> message?.file?.path?.let {
                                openWebPage(Uri.parse(it))
                        }
                        else -> {}
                }
        }

        private fun onSendMessageLoading(message: String?, data: Message?) {
                messagesAdapter.upsert(data, true)
        }

        private fun onSendMessageSuccess(message: Message) {
                arguments?.getString(CONVERSATION_ID)?.let { conversationId ->
                        MessagingAnalyticsUtils.trackMessageSent(
                                conversationId
                        )
                }
                messagesAdapter.upsert(message, true)
        }


        private fun onSendMessageError(exception: String?, data: Message?) {
                messagesAdapter.update(data)
        }


        private fun onTextMessageStatusClicked(message: Message?) {
                message?.let {
                        viewModel.onResendChatMessage(it.id)
                }
        }

        private fun onImageMessageCancelClicked(message: Message?) {
                viewModel.cancelSendMessageJob()
                message?.let { messagesAdapter.delete(it) }
        }

        private fun onAdMenuClicked(view: View) {
                val popupMenu = PopupMenu(requireContext(), view)
                popupMenu.menuInflater.inflate(
                        if (viewModel.blocked) {
                                R.menu.messaging_list_blocked_item_menu
                        } else {
                                R.menu.messaging_list_unblocked_item_menu
                        },
                        popupMenu.menu
                )
                popupMenu.setOnMenuItemClickListener {
                        when (it.itemId) {
                                R.id.block_user -> {
                                        showConfirmationDialog(
                                                title = R.string.app_name,
                                                description = getString(R.string.mc_inbox_block_user_dialog_message),
                                                onConfirm = {
                                                        viewModel.blockConversation(
                                                                isItFromAdDetail
                                                        )
                                                },
                                                onDecline = {}
                                        )
                                        true
                                }
                                R.id.unblock_user -> {
                                        viewModel.unblockConversation(isItFromAdDetail)
                                        true
                                }
                                R.id.clear_conversation -> {
                                        showConfirmationDialog(
                                                title = R.string.app_name,
                                                description = getString(R.string.mc_inbox_delete_conversation_dialog_message),
                                                onConfirm = {
                                                        viewModel.clearConversation(
                                                                isItFromAdDetail
                                                        )
                                                },
                                                onDecline = {}
                                        )
                                        true
                                }
                                else -> {
                                        false
                                }
                        }
                }
                popupMenu.show()
        }

        private fun onConversationUiStateChanged(state: ConversationAction) {
                when (state) {
                        is ConversationAction.ClearConv -> if (state.cleared) {
                                dismiss()
                                conversationActionsListener?.onConversationActionTriggered(
                                        ConversationAction.ClearConv(
                                                state.id,
                                                state.cleared
                                        )
                                )
                        } else {
                                // show error
                        }
                        is ConversationAction.BlockConv -> if (state.blocked) {
                                binding.input.visibility = View.GONE
                                conversationActionsListener?.onConversationActionTriggered(
                                        ConversationAction.BlockConv(
                                                state.id,
                                                state.blocked
                                        )
                                )
                        } else {
                                // show error
                        }
                        is ConversationAction.UnblockConv -> if (state.unblocked) {
                                binding.input.visibility = View.VISIBLE
                                conversationActionsListener?.onConversationActionTriggered(
                                        ConversationAction.UnblockConv(
                                                state.id,
                                                state.unblocked
                                        )
                                )
                        } else {
                                // show error
                        }
                }
        }

        override fun onFirstVisibleItemChanged(index: Int) {
                if (index == 0 && binding.scrollDown.visibility == View.VISIBLE) {
                        binding.scrollDown.visibility = View.GONE
                        viewModel.hasToMarkConversationAsRead()
                }
                viewModel.firstVisibleItemChanged(index)
        }

        private fun deleteTemporaryFile(context: Context) {
                val folder = context.getExternalFilesDir("Temp")
                if (folder != null) {
                        if (deleteDirectory(folder)) {
                                Log.i("Deleted ", " deleteDirectory was called")
                        }
                }
        }

        fun deleteDirectory(path: File): Boolean {
                if (path.exists()) {
                        val files = path.listFiles() ?: return false
                        for (file in files) {
                                if (file.isDirectory) {
                                        deleteDirectory(file)
                                } else {
                                        val wasSuccessful = file.delete()
                                        if (wasSuccessful) {
                                                Log.i("Deleted ", "successfully")
                                        }
                                }
                        }
                }
                return path.delete()
        }

        private fun openWebPage(deeplink: Uri?) {
                val intent = Intent(Intent.ACTION_VIEW, deeplink)
                if (intent.resolveActivity(requireActivity().packageManager) != null) {
                        startActivity(intent)
                } else {
                        Toast.makeText(requireContext(), "can't open file", Toast.LENGTH_LONG)
                                .show()
                }
        }

        private fun openAd(listId: String) {
                if (listId.isNotEmpty()) {
                        val intent = Intent(requireContext(), MainActivity::class.java).apply {
                                putExtra(Keys.AD_ID, listId)
                                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        }
                        startActivity(intent)
                }
        }
}

interface ConversationActionsListener {
        fun onConversationActionTriggered(conversationAction: ConversationAction)

}