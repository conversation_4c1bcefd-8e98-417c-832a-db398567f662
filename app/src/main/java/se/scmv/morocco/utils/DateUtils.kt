package se.scmv.morocco.utils

import android.content.Context
import se.scmv.morocco.R
import se.scmv.morocco.utils.Constants.DATE_FORMAT_ISO_8601
import java.text.DateFormat
import java.text.ParseException
import java.text.SimpleDateFormat
import java.time.Duration
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.*
import kotlin.math.floor

/**
 * Created by h<PERSON><PERSON><PERSON><PERSON><PERSON> on 6/14/17.
 */
object DateUtils {
    private const val HOUR_MINUTE_SEPARATOR = ":"
    const val DATE_FORMAT_YEAR_MONTH_DAY = "yyyy-MM-dd"
    const val DATE_FORMAT_DAY_MONTH_YEAR = "dd/MM/yyyy"
    const val DATE_FORMAT_YEAR = "yyyy"
    const val DATE_FORMAT_YEAR_MONTH_DAY_HOUR_MINUTES_SECONDS = "yyyy-MM-dd HH:mm:ss"
    const val HOUR_MINUTES = "HH:mm"

    @JvmStatic
    fun getCurrentDateWithFormat(format: String?): String {
        val date = Date()
        return SimpleDateFormat(format).format(date)
    }

    fun getDateWithFormat(date: Date, format: String): String {
        return SimpleDateFormat(format, Locale.getDefault()).format(date)
    }

    //PS : Note If the any Exception happens  the original string date will be returned
    fun getYearFromStringWithFormat(dateToParse: String?, format: String?): String? {
        var dateToReturn = dateToParse
        val formatter: DateFormat = SimpleDateFormat(format, Locale.FRANCE)
        val YearFormatter = SimpleDateFormat(DATE_FORMAT_YEAR, Locale.FRANCE)
        try {
            val date = formatter.parse(dateToParse)
            dateToReturn = YearFormatter.format(date)
        } catch (e: ParseException) {
            e.printStackTrace()
            //If the any Exception happens the original string date will be returned
        }
        return dateToReturn
    }

    /**
     * @param stringDate Example 2019-03-25 16:02:18
     * @return number of days between the given date and now
     * @throws ParseException
     */
    @JvmStatic
    @Throws(ParseException::class)
    fun getDaysDiffFromTimestamp(stringDate: String?): Int {
        val formatter: DateFormat =
            SimpleDateFormat(
                DATE_FORMAT_YEAR_MONTH_DAY_HOUR_MINUTES_SECONDS,
                Locale.FRANCE
            )
        val date = formatter.parse(stringDate)
        val now = Date()
        val difference = Math.abs(now.time - date.time)
        val differenceDates = difference / (24 * 60 * 60 * 1000)
        return differenceDates.toInt()
    }

    /**
     * @param stringDate Example 2019-03-25 16:02:18
     * @return number of minutes between the given date and now
     * @throws ParseException
     */
    @JvmStatic
    @Throws(ParseException::class)
    fun getMinutesDiffFromTimestamp(stringDate: String?): Int {
        val formatter: DateFormat =
            SimpleDateFormat(
                DATE_FORMAT_YEAR_MONTH_DAY_HOUR_MINUTES_SECONDS,
                Locale.FRANCE
            )
        val date: Date = formatter.parse(stringDate)
        val now = Date()
        val difference = Math.abs(now.time - date.time)
        val differenceDates = difference / (60 * 1000)


        return differenceDates.toInt()
    }


    @JvmStatic
    fun formatDateLocalization(
        date: String,
        dateFormat: String = Constants.DISPLAY_DATE_FORMAT
    ): String {
        return try {
            val formatter: DateTimeFormatter = DateTimeFormatter.ofPattern(
                    dateFormat,
                    Locale.getDefault()
                )
            val zDate = Instant.parse(date)
            val dateTime = zDate.atZone(ZoneId.systemDefault())
            val formattedDate = dateTime.format(formatter)
            formattedDate.toString()

        } catch (e: Exception) {
            ""
        }
    }

    @JvmStatic
    fun formatDateLocalization(
        date: Date,
        format: String = DATE_FORMAT_YEAR_MONTH_DAY_HOUR_MINUTES_SECONDS
    ): String {
        return try {
            val formatter: DateTimeFormatter = DateTimeFormatter.ofPattern(
                format,
                Locale.getDefault()
            )
            val localDateTime = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(date.time),
                ZoneId.systemDefault()
            ).atZone(ZoneId.systemDefault())
            localDateTime.format(formatter)
        } catch (e: Exception) {
            date.toString()
        }
    }

    @JvmStatic
    fun formatDateLocalizationForMessaging(date: String): String {
        return try {
            val formatter: DateTimeFormatter =
                DateTimeFormatter.ofPattern(
                    Constants.DATE_FORMAT,
                    Locale.getDefault()
                )
            val zDate = Instant.parse(date)
            val dateTime = zDate.atZone(ZoneId.systemDefault())
            val formattedDate = dateTime.format(formatter)
            formattedDate.toString()

        } catch (e: Exception) {
            ""
        }

    }

    @JvmStatic
    fun formatStringToDate(date: String, formatPattern: String): Date {
        val format = SimpleDateFormat(formatPattern, Locale.getDefault())
        return try {
            format.parse(date)
        } catch (e: ParseException) {
            e.printStackTrace()
            Date()
        }
    }

    fun isStartDateInFuture(startDate: String): Boolean {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.getDefault())
        val currentDate = Date()
        val parsedStartDate = dateFormat.parse(startDate)

        return parsedStartDate.after(currentDate)
    }

    fun getRelativeTimeSpanString(date: String, context: Context): String {
        val adInsertionInstant = ZonedDateTime.parse(
            date,
            DateTimeFormatter.ISO_DATE_TIME
        ).toInstant()
        val currentTime = Instant.now().atZone(ZoneId.systemDefault()).toInstant()
        val timeDifference = Duration.between(adInsertionInstant, currentTime)
        val yearsPassed = timeDifference.toDays() / 365
        var daysRemaining = timeDifference.toDays() % 365
        val monthsPassed = floor(daysRemaining / 30.44).toLong()
        daysRemaining = (daysRemaining - monthsPassed * 30.44).toLong()
        val weeksPassed = daysRemaining / 7
        daysRemaining %= 7
        val hoursPassed = timeDifference.toHours() % 24
        val minutesPassed = timeDifference.toMinutes() % 60
        val secondsPassed = timeDifference.seconds % 60
        return if (yearsPassed > 0) {
            context.resources.getQuantityString(
                R.plurals.time_passed_years,
                yearsPassed.toInt(),
                yearsPassed.toInt()
            )
        } else if (monthsPassed > 0) {
            context.resources.getQuantityString(
                R.plurals.time_passed_months,
                monthsPassed.toInt(),
                monthsPassed.toInt()
            )
        } else if (weeksPassed > 0) {
            context.resources.getQuantityString(
                R.plurals.time_passed_weeks,
                weeksPassed.toInt(),
                weeksPassed.toInt()
            )
        } else if (daysRemaining > 0) {
            context.resources.getQuantityString(
                R.plurals.time_passed_days,
                daysRemaining.toInt(),
                daysRemaining.toInt()
            )
        } else if (hoursPassed > 0) {
            context.resources.getQuantityString(
                R.plurals.time_passed_hours,
                hoursPassed.toInt(),
                hoursPassed.toInt()
            )
        } else if (minutesPassed > 0) {
            context.resources.getQuantityString(
                R.plurals.time_passed_minutes,
                minutesPassed.toInt(),
                minutesPassed.toInt()
            )
        } else {
            context.resources.getQuantityString(
                R.plurals.time_passed_seconds,
                secondsPassed.toInt(),
                secondsPassed.toInt()
            )
        }
    }

    fun dateUntil(
        date: String,
        context: Context,
        format: String = DATE_FORMAT_ISO_8601
    ): String {
        // Define the formatter
        val formatter = DateTimeFormatter.ofPattern(format)

        // Parse the input date string
        val inputDateTime = LocalDateTime.parse(date, formatter)

        // Get the current date and time
        val now = LocalDateTime.now()

        // Calculate the difference in various units
        val hoursUntil = ChronoUnit.HOURS.between(now, inputDateTime)
        val daysUntil = ChronoUnit.DAYS.between(now, inputDateTime)
        val weeksUntil = daysUntil / 7
        val monthsUntil = ChronoUnit.MONTHS.between(now, inputDateTime)

        return when {
            hoursUntil < 0 || hoursUntil == 0L -> context.resources.getString(R.string.now)
            hoursUntil < 24 -> context.resources.getQuantityString(
                R.plurals.hours,
                hoursUntil.toInt(),
                hoursUntil.toInt()
            )

            daysUntil < 7 -> "$daysUntil " + context.getString(R.string.days)
            monthsUntil < 1 -> "$weeksUntil " + context.getString(R.string.weeks)
            else -> "$monthsUntil" + context.getString(R.string.months)
        }
    }
}