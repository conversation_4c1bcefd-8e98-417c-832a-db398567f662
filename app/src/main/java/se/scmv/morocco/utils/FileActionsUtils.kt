package se.scmv.morocco.utils

import android.app.DownloadManager
import android.content.ActivityNotFoundException
import android.content.ContentResolver
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Environment
import android.widget.Toast
import androidx.core.content.FileProvider
import se.scmv.morocco.Avito
import se.scmv.morocco.R
import java.io.File

object FileActionsUtils {
    fun viewFile(context: Context, fileName: String) {
        val file =
            File(Environment.getExternalStorageDirectory().path + "/" + Environment.DIRECTORY_DOWNLOADS + "/" + fileName)
        val uri = FileProvider.getUriForFile(context, context.packageName + ".imagesprovider", file)
        val openFile = Intent(Intent.ACTION_VIEW)
        openFile.setDataAndType(uri, UriUtils.getMimeType(file))
        openFile.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_ACTIVITY_NO_HISTORY)
        try {
            context.startActivity(openFile)
        } catch (e: ActivityNotFoundException) {
            Toast.makeText(
                context,
                context.getString(R.string.mc_no_app_found_to_open_this_file),
                Toast.LENGTH_LONG
            ).show()
        }
    }


    private fun openDownloadedAttachment(
        context: Context,
        attachmentUri: Uri,
        attachmentMimeType: String
    ) {
        var attachmentUri: Uri? = attachmentUri
        if (attachmentUri != null) {
            if (ContentResolver.SCHEME_FILE == attachmentUri.scheme) {
                val file = File(attachmentUri.path)
                attachmentUri = FileProvider.getUriForFile(
                    context,
                    context.packageName + ".imagesprovider",
                    file
                )
            }
            val openAttachmentIntent = Intent(Intent.ACTION_VIEW)
            openAttachmentIntent.setDataAndType(attachmentUri, attachmentMimeType)
            openAttachmentIntent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            try {
                context.startActivity(openAttachmentIntent)
            } catch (e: ActivityNotFoundException) {
                Toast.makeText(
                    context,
                    context.getString(R.string.mc_no_app_found_to_open_this_file),
                    Toast.LENGTH_LONG
                ).show()
            }
        }
    }

    fun downloadFile(context: Context, url: String?, fileName: String?) {
        val request = DownloadManager.Request(Uri.parse(url))
            .setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED) // Visibility of the download Notification
            .setTitle(fileName) // Title of the Download Notification
            .setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, fileName)
            .setDescription(context.getString(R.string.mc_message_file_downloading)) // Description of the Download Notification
            .setAllowedOverMetered(true) // Set if download is allowed on Mobile network
            .setAllowedOverRoaming(true) // Set if download is allowed on roaming network
        val downloadManager = context.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
        val downloadID =
            downloadManager.enqueue(request) // enqueue puts the download request in the queue.

        // using query method
        var finishDownload = false
        var progress: Int
        while (!finishDownload) {
            val cursor = downloadManager.query(DownloadManager.Query().setFilterById(downloadID))
            if (cursor.moveToFirst()) {
                val status = cursor.getInt(cursor.getColumnIndex(DownloadManager.COLUMN_STATUS))
                when (status) {
                    DownloadManager.STATUS_FAILED -> {
                        finishDownload = true
                    }

                    DownloadManager.STATUS_PAUSED -> {}
                    DownloadManager.STATUS_PENDING -> {}
                    DownloadManager.STATUS_RUNNING -> {
                        val total =
                            cursor.getLong(cursor.getColumnIndex(DownloadManager.COLUMN_TOTAL_SIZE_BYTES))
                        if (total >= 0) {
                            val downloaded =
                                cursor.getLong(cursor.getColumnIndex(DownloadManager.COLUMN_BYTES_DOWNLOADED_SO_FAR))
                            progress = ((downloaded * 100L) / total).toInt()
                            // if you use downloadmanger in async task, here you can use like this to display progress.
                            // Don't forget to do the division in long to get more digits rather than double.
                            //  publishProgress((int) ((downloaded * 100L) / total));
                        }
                    }

                    DownloadManager.STATUS_SUCCESSFUL -> {
                        progress = 100
                        // if you use aysnc task
                        // publishProgress(100);
                        finishDownload = true
                        Toast.makeText(
                            Avito.context,
                            context.getString(R.string.mc_message_file_downloaded),
                            Toast.LENGTH_SHORT
                        ).show()
                        val downloadLocalUri = cursor.getString(
                            cursor.getColumnIndex(
                                DownloadManager.COLUMN_LOCAL_URI
                            )
                        )
                        val downloadMimeType = cursor.getString(
                            cursor.getColumnIndex(
                                DownloadManager.COLUMN_MEDIA_TYPE
                            )
                        )
                        if (downloadLocalUri != null) {
                            openDownloadedAttachment(
                                context,
                                Uri.parse(downloadLocalUri),
                                downloadMimeType
                            )
                        }
                    }
                }
            }
            cursor.close()
        }
    }
}
