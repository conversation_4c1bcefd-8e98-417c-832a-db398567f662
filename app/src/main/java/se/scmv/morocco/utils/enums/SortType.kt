package se.scmv.morocco.utils.enums

public enum class SortType(val value: String) {
    NON("Non"),
    ASC("ASC"),
    DESC("DESC");

    companion object {
        fun fromString(value: String?): SortType {
            return when (value) {
                NON.value -> NON
                ASC.value -> ASC
                DESC.value -> DESC
                else -> NON // default value
            }
        }
    }
}