package se.scmv.morocco.utils

import se.scmv.morocco.Avito
import se.scmv.morocco.Avito.Companion.context
import se.scmv.morocco.utils.Constants.PRIVATE_FLAVOR
import se.scmv.morocco.utils.Constants.SHOPS_FLAVOR

class FlavorUtils {


    companion object {
        fun changeAppFlavor() {
            if (Avito.FLAVOR != null && Avito.FLAVOR == PRIVATE_FLAVOR) {
                Utils.savePreference(context, Utils.APP_FLAVOR, SHOPS_FLAVOR)
                Utils.rebirthApp(context)
            } else if (Avito.FLAVOR != null && Avito.FLAVOR == SHOPS_FLAVOR) {
                Utils.savePreference(context, Utils.APP_FLAVOR, PRIVATE_FLAVOR)
                Utils.rebirthApp(context)
            }
        }
        fun setShopsFlavor() {
            Utils.savePreference(context, Utils.APP_FLAVOR, SHOPS_FLAVOR)
            Utils.rebirthApp(context)
        }

        fun resetFlavor() {
            Utils.savePreference(context, Utils.APP_FLAVOR, PRIVATE_FLAVOR)
            Utils.rebirthApp(context)
        }
        
        fun isShopFlavor() : Boolean {
            return Avito.FLAVOR.equals(SHOPS_FLAVOR)
        }

        fun isPrivateFlavor() : Boolean {
            return Avito.FLAVOR.equals(PRIVATE_FLAVOR)
        }
    }
}