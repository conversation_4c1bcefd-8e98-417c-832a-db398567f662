package se.scmv.morocco.utils

import android.content.Context
import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.graphics.drawable.Drawable
import android.widget.TextView
import androidx.core.content.ContextCompat
import se.scmv.morocco.R
import se.scmv.morocco.dao.CategoryRecord
import se.scmv.morocco.utils.enums.EnumCategory

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 20/09/16.
 * This a drawable utilities class.
 */
object DrawablesUtils {
    /**
     * This returns the default image holder as a drawable.
     *
     * @param context    current context
     * @param categoryId the category ID, [EnumCategory]
     * @return the corresponding drawable
     */
    @JvmStatic
    fun getImagePlaceholder(context: Context?, categoryId: Int): Drawable? {
        // convert categoryId to EnumCategory
        val enumCatId = EnumCategory.fromId(categoryId)
        if (enumCatId != null) {
            val drawable = R.drawable.ic_no_image_placeholder
            return context?.let { ContextCompat.getDrawable(it, drawable) }
        }
        return null
    }

    /**
     * This returns the default image holder as a drawable.
     *
     * @param category the DAO category class, [CategoryRecord]
     * @return the corresponding drawable
     */
    @JvmStatic
    fun getCategoryPlaceholder(category: CategoryRecord): Int {
        // convert categoryId to EnumCategory
        val enumCatId = EnumCategory.fromId(category.categoryId)
            ?: return R.drawable.ic_favorite_vide
        return when (enumCatId) {
            EnumCategory.IMMO -> R.drawable.ic_favorite_immobilier
            EnumCategory.VEHICULES -> R.drawable.ic_favorite_vehicules
            EnumCategory.MAISON_JARDIN -> R.drawable.ic_favorite_maison_jardin
            EnumCategory.LOISIR -> R.drawable.ic_favorite_loisirs
            EnumCategory.INFO_MULTIMEDIA -> R.drawable.ic_favorite_multimedia
            EnumCategory.EMPLOI_SERVICE -> R.drawable.ic_favorite_services
            EnumCategory.AUTRE -> R.drawable.ic_favorite_autres
            EnumCategory.HABILLEMENT -> R.drawable.ic_favorite_habillement
            EnumCategory.ENTREPRISES -> R.drawable.ic_favorite_entreprises
            else -> R.drawable.ic_favorite_vide
        }
    }

    @JvmStatic
    fun getCategoryPlaceholder(categoryID: Int): Int {
        // convert categoryId to EnumCategory
        return when {
            categoryID >= EnumCategory.IMMO.category && categoryID < EnumCategory.VEHICULES.category -> R.drawable.ic_favorite_immobilier
            categoryID >= EnumCategory.VEHICULES.category && categoryID < EnumCategory.MAISON_JARDIN.category -> R.drawable.ic_favorite_vehicules
            categoryID >= EnumCategory.MAISON_JARDIN.category && categoryID < EnumCategory.LOISIR.category -> R.drawable.ic_favorite_maison_jardin
            categoryID >= EnumCategory.LOISIR.category && categoryID < EnumCategory.INFO_MULTIMEDIA.category -> R.drawable.ic_favorite_loisirs
            categoryID >= EnumCategory.INFO_MULTIMEDIA.category && categoryID < EnumCategory.EMPLOI_SERVICE.category -> R.drawable.ic_favorite_multimedia
            categoryID >= EnumCategory.EMPLOI_SERVICE.category && categoryID < EnumCategory.AUTRE.category -> R.drawable.ic_favorite_services
            categoryID >= EnumCategory.AUTRE.category && categoryID < EnumCategory.HABILLEMENT.category -> R.drawable.ic_favorite_autres
            categoryID >= EnumCategory.HABILLEMENT.category && categoryID < EnumCategory.ENTREPRISES.category -> R.drawable.ic_favorite_habillement
            else -> R.drawable.ic_favorite_autres
        }
    }

    fun setTextViewDrawableColor(textView: TextView, color: Int) {
        for (drawable in textView.compoundDrawables) {
            if (drawable != null) {
                drawable.colorFilter = PorterDuffColorFilter(
                    ContextCompat.getColor(textView.context, color),
                    PorterDuff.Mode.SRC_IN
                )
            }
        }
    }
}