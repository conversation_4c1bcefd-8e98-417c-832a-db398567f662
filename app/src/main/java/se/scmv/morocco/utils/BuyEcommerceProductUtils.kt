package se.scmv.morocco.utils

import android.content.Context
import android.content.Intent
import android.os.Bundle
import se.scmv.morocco.domain.models.AdDetails
import se.scmv.morocco.ecomerce.checkout.PaymentDetailsActivity

object BuyEcommerceProductUtils {

    @JvmStatic
    fun buyEcommerceProduct(
        context: Context,
        ad: AdDetails.Details,
        adUuid: String
    ) {
//        buyEcommerceProductAnalytics(context, ad)
        val openVas = Intent(context, PaymentDetailsActivity::class.java)
        if (ad.category?.parent?.id?.toInt() != null && ad.media?.mediaCount!! > 0) {
            openVas.putExtras(
                bumpAdExtrasEcommerce(
                    context = context,
                    ad = ad,
                    adUuid = adUuid
                )
            )
            context.startActivity(openVas)
        }
    }

    private fun bumpAdExtrasEcommerce(
        context: Context,
        ad: AdDetails.Details,
        adUuid: String
    ): Bundle {
        return getBundleOfEcommerce(
            listId = ad.listId.toString(),
            sellerId = adUuid,
            adId = ad.adId.toString(),
            adTitle = ad.title,
            adCity = ad.cityArea?.name,
            adDate = DateUtils.formatDateLocalization(ad.listTimeString.toString()),
            adPrice = ad.price.getCurrentPrice().toString(),
            adType = ad.type?.key?.name,
            adCategoryParent = ad.category?.parent?.id?.toInt()!!,
            cityId = ad.cityArea!!.id.toInt(),
            categoryId = ad.category!!.id.toInt(),
            adImagePath = ad.media!!.defaultImage!!.paths!!.standard.orEmpty(),
            hasPictures = ad.media?.mediaCount!! > 0
        )
    }

    private fun getBundleOfEcommerce(
        listId: String,
        sellerId: String,
        adId: String,
        adTitle: String?,
        adCity: String?,
        adDate: String?,
        adPrice: String?,
        adType: String?,
        adCategoryParent: Int,
        cityId: Int,
        categoryId: Int,
        adImagePath: String?,
        hasPictures: Boolean
    ): Bundle {
        val openVas = Bundle()
        openVas.putString(Constants.VAS_AD_LIST_ID, listId)
        openVas.putString(Constants.VAS_AD_ID, adId)
        openVas.putString(Constants.SELLER_ID, sellerId)
        openVas.putString(Constants.VAS_AD_TITLE, adTitle)
        openVas.putString(Constants.VAS_AD_CITY, adCity)
        openVas.putString(Constants.VAS_AD_TYPE, adType)
        openVas.putString(Constants.VAS_AD_DATE, adDate)
        openVas.putString(Constants.VAS_AD_PRICE, adPrice)
        openVas.putInt(Constants.VAS_AD_CITY_ID, cityId)
        openVas.putInt(Constants.VAS_AD_CATEGORY_ID, categoryId)
        openVas.putInt(Constants.VAS_AD_CATEGORY_PARENT, adCategoryParent)
        openVas.putString(Constants.VAS_AD_IMAGE_PATH, adImagePath)
        openVas.putBoolean(Constants.VAS_AD_HAS_PICTURE, hasPictures)
        return openVas
    }
}