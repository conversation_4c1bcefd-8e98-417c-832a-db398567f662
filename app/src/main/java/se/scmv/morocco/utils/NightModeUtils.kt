package se.scmv.morocco.utils

import android.content.Context
import android.content.res.Configuration
import androidx.appcompat.app.AppCompatDelegate

class NightModeUtils(val context: Context?) {

        companion object {
                fun isNightMode(context: Context?): Boolean {

                        return when (context?.resources?.configuration?.uiMode?.and(Configuration.UI_MODE_NIGHT_MASK)) {
                                16 -> {
                                        return false
                                }
                                32 -> {
                                        return true
                                }
                                else -> false
                        }
                }

        }

        fun setPreferredTheme() {
                if (Utils.getStringPreference(context, Utils.DARK_MODE_ENABLED) != null) {
                        if (Utils.getStringPreference(context, Utils.DARK_MODE_ENABLED) == "true")
                                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES)

                }
        }

        fun switchTheme() {

                when (context?.resources?.configuration?.uiMode?.and(Configuration.UI_MODE_NIGHT_MASK)) {
                        16 -> {
                                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES)
                                Utils.savePreference(context, Utils.DARK_MODE_ENABLED, "true")

                        }
                        32 -> {
                                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
                                Utils.savePreference(context, Utils.DARK_MODE_ENABLED, "false")

                        }
                }
        }

}