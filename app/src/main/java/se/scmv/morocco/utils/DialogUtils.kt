package se.scmv.morocco.utils

import android.app.ProgressDialog
import android.content.Context
import android.content.DialogInterface
import android.os.Build
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.res.ResourcesCompat
import se.scmv.morocco.R
import se.scmv.morocco.activities.BaseActivity
import se.scmv.morocco.dialogs.ConfirmDialogFragment
import se.scmv.morocco.dialogs.DialogCancelAiFragment

/**
 * Created by husa<PERSON><PERSON><PERSON>m on 12/5/16.
 */
class DialogUtils {
        var progressDialog: ProgressDialog?
                private set

        /**
         * Method to return whether the progress dialog has been canceled or not
         * @return
         */
        var isCanceled: Boolean
                private set

        /**
         * Contructor that takes the context of the dialog fragment, its title and the message to display
         * @param context
         * @param title
         * @param message
         */
        constructor(context: Context?, title: CharSequence?, message: CharSequence?) {
                progressDialog = ProgressDialog.show(context, title, message)
                progressDialog?.setCancelable(true)
                isCanceled = false
        }

        constructor(
                context: Context?,
                title: CharSequence?,
                message: CharSequence?,
                isCancelable: Boolean?
        ) {
                progressDialog = ProgressDialog.show(context, title, message)
                if (isCancelable != null) {
                        progressDialog?.setCancelable(isCancelable)
                }
                isCanceled = false
        }

        /**
         * Constructor that takes this time the message as an integer
         * The integer is the resource id of the -string- message
         * @param context
         * @param title
         * @param message
         */
        constructor(context: Context, title: CharSequence?, message: Int) : this(
                context,
                title,
                context.getString(message)
        )

        /**
         * Method to dismiss the progress dialog
         */
        fun dismiss() {
                try {
                        if (progressDialog != null) progressDialog?.dismiss()
                } catch (e: Exception) {
                        e.printStackTrace()
                }
        }

        /**
         * Method to set a cancel listener on the progress dialog
         */
        fun setOnCancelListener() {
                progressDialog?.setOnCancelListener {
                        try {
                                if (progressDialog != null && progressDialog?.isShowing == true) progressDialog?.dismiss()
                        } catch (e: Exception) {
                                e.printStackTrace()
                        }
                        isCanceled = true
                }
        }

        /**
         * Method that determines whether or not the dialog is cancelable
         * by default the dialog is cancelable
         * @param cancelable
         */
        fun setCancelable(cancelable: Boolean?) {
                if (progressDialog != null) cancelable?.let { progressDialog?.setCancelable(it) }
        }

        /**
         * Method that returns whether the dialog is visible or not
         * @return
         */
        val isShowing: Boolean
                get() = progressDialog?.isShowing == true

        companion object {
                // TODO: create an Orion Custom Dialog following re-branding guidelines
                /**
                 * Method called when back is pressed
                 * Displays a dialog fragment to confirm the action
                 */
                fun confirmBackToLandingPage(
                        ctx: Context,
                        title: Int,
                        message: Int,
                        positive: Int,
                        negative: Int,
                        positiveonClickListener: DialogInterface.OnClickListener,
                        negativeonClickListener: DialogInterface.OnClickListener?
                ) {
                        if (title == R.string.draft_ad_abort_ai_title) {
                                val bookmarkIcon = ResourcesCompat.getDrawable(
                                        ctx.resources,
                                        R.drawable.ic_bookmark_fill,
                                        ctx.theme
                                )
                                val dialog = DialogCancelAiFragment(
                                        ctx,
                                        bookmarkIcon,
                                        title,
                                        message,
                                        object : DialogCancelAiFragment.OnDialogActionListener {
                                                override fun onPositiveAction() {
                                                        positiveonClickListener.onClick(null, 0)
                                                }

                                                override fun onNegativeAction() {
                                                        negativeonClickListener?.onClick(null, 0)
                                                }
                                        })
                                dialog.show(
                                        (ctx as AppCompatActivity).supportFragmentManager,
                                        "CancelAiConfirmDialog"
                                )
                        } else {
                                var builder: AlertDialog.Builder? = null
                                builder =
                                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                                                AlertDialog.Builder(ctx, R.style.AlertDialogCustom)
                                        } else {
                                                AlertDialog.Builder(ctx)
                                        }
                                builder.setMessage(message)
                                        .setTitle(title)
                                        .setNegativeButton(negative, negativeonClickListener)
                                        .setPositiveButton(positive, positiveonClickListener)
                                        .create()
                                        .show()
                        }
                }

                @JvmStatic
                fun displayFragmentDialog(
                        ctx: BaseActivity,
                        onDialogActionListener: ConfirmDialogFragment.OnDialogActionListener?
                ) {
                        val dialogFragment = ConfirmDialogFragment()
                        dialogFragment.setIcon(R.drawable.ic_ai_dialog)
                        dialogFragment.setMessage(ctx.getString(R.string.ai_confirm_dialog_message))
                        dialogFragment.setPositiveAction(ctx.getString(R.string.ai_confirm_dialog_action_insert))
                        dialogFragment.setNegativeAction(ctx.getString(R.string.ai_confirm_dialog_action_search))
                        dialogFragment.dialogActionListener = onDialogActionListener
                        dialogFragment.isCancelable = false
                        ctx.let {
                                it.supportFragmentManager.let {
                                        if (!it.isDestroyed) {
                                                dialogFragment.show(
                                                        ctx.supportFragmentManager,
                                                        "AiConfirmDialog"
                                                )
                                        }
                                }
                        }
                }

                @JvmStatic
                fun displayFragmentDialog(
                        ctx: AppCompatActivity,
                        text: String?,
                        positive: String?,
                        negative: String?,
                        onDialogActionListener: ConfirmDialogFragment.OnDialogActionListener?
                ) {
                        val dialogFragment = ConfirmDialogFragment()
                        dialogFragment.setIcon(R.drawable.drawable_phone_verification)
                        dialogFragment.setMessage(text)
                        dialogFragment.setPositiveAction(positive)
                        dialogFragment.setNegativeAction(negative)
                        dialogFragment.dialogActionListener = onDialogActionListener
                        dialogFragment.isCancelable = false
                        dialogFragment.show(ctx.supportFragmentManager, "AiConfirmDialog")
                }
        }
}