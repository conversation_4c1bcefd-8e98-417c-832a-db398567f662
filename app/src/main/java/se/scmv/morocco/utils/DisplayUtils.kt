package se.scmv.morocco.utils

import android.content.Context
import android.util.TypedValue
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import se.scmv.morocco.R

object DisplayUtils {
        @JvmStatic
        fun dpToPx(context: Context, dp: Float): Int {
                val displayMetrics = context.resources.displayMetrics
                return TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp, displayMetrics)
                        .toInt()
        }

        @JvmStatic
        fun initializeBadgeView(ctx: Context?, container: ConstraintLayout) {
                val textView = container.findViewById<TextView>(R.id.new_feature_badge)
                textView.visibility = if (Utils.getBooleanPreference(
                                ctx,
                                Keys.CHAT_IS_NEW
                        )
                ) View.VISIBLE else View.INVISIBLE
        }
}