package se.scmv.morocco.utils

 import android.content.Context
 import androidx.annotation.Keep
 import se.scmv.morocco.GetMyStoreInfoQuery
 import se.scmv.morocco.type.StoreType

object StoreUtils {

        fun getStoreInfoFromPref(ctx: Context): GetMyStoreInfoQuery.GetMyStoreInfo {
                val pref = ctx.getSharedPreferences("main.ShopsAvito", Context.MODE_PRIVATE)
                val key = "storeInfo"
                val objectString = pref.getString(key, null)
                return SharedGson.get().fromJson(objectString, GetMyStoreInfo::class.java).toQuery()
        }

        fun saveStoreInfoInPref(ctx: Context, storeInfo: GetMyStoreInfoQuery.GetMyStoreInfo) {
                val sharedPreferences = ctx.getSharedPreferences("main.ShopsAvito", Context.MODE_PRIVATE)
                val editor = sharedPreferences.edit()
                val objectString = SharedGson.get().toJson(storeInfo.toModel())
                editor.putString("storeInfo", objectString)
                editor.apply()
        }


        fun getRestrictionCities(ctx: Context): List<String?>{
                val storeInfo = getStoreInfoFromPref(ctx)
                return storeInfo.locations.map { location ->  location?.city?.id}
        }
}

// TODO We're using these classes because R8 removes some graphql classes,
//  so gson deserialization fails and the app crashes.
//  This is a bad practice, we should remove these classes
//  and find a better way to store the current store infos.
private fun GetMyStoreInfoQuery.GetMyStoreInfo.toModel() = GetMyStoreInfo(
    name = name,
    email = email,
    category = Category(id = category.id, name = category.name),
    phones = phones.mapNotNull { phone -> phone?.number?.let { Phone(it)}},
    description = Description(short = description.short, long = description.long),
    locations = locations.mapNotNull { location ->
        location?.let {
            Location(
                city = location.city?.let { city ->
                    City(
                        id = city.id,
                        name = city.name
                    )
                },
                address = location.address
            )
        } },
    startDate = startDate,
    expiryDate = expiryDate,
    logo = logo?.defaultPath?.let { Logo(defaultPath = it) },
    points = Points(count = points.count, expiryDate = points.expiryDate),
    website = website,
    offersDelivery = offersDelivery,
    extraMediaAllowed = extraMediaAllowed,
    membership = Membership(id = membership.id, name = membership.name),
    isVerifiedSeller = isVerifiedSeller,
)

private fun GetMyStoreInfo.toQuery(): GetMyStoreInfoQuery.GetMyStoreInfo = GetMyStoreInfoQuery.GetMyStoreInfo(
    name = name,
    email = email,
    category = GetMyStoreInfoQuery.Category(id = category.id, name = category.name, trackingValue = category.name),
    phones = phones.mapNotNull { phone -> phone?.number?.let { GetMyStoreInfoQuery.Phone(it)}},
    description = GetMyStoreInfoQuery.Description(short = description.short, long = description.long),
    locations = locations.mapNotNull { location ->
        location?.let {
            GetMyStoreInfoQuery.Location(
                city = location.city?.let { city ->
                    GetMyStoreInfoQuery.City(
                        id = city.id,
                        name = city.name,
                        // TODO implement this
                        trackingValue = ""
                    )
                },
                address = location.address
            )
        } },
    startDate = startDate,
    expiryDate = expiryDate,
    logo = logo?.defaultPath?.let {
        GetMyStoreInfoQuery.Logo(defaultPath = it)
    },
    points = GetMyStoreInfoQuery.Points(count = points.count, expiryDate = points.expiryDate),
    website = website,
    offersDelivery = offersDelivery,
    extraMediaAllowed = extraMediaAllowed,
    membership = GetMyStoreInfoQuery.Membership(id = membership.id, name = membership.name),
    isVerifiedSeller = isVerifiedSeller,
)

@Keep
private data class GetMyStoreInfo(
    val name: String,
    val email: String,
    val category: Category,
    val phones: List<Phone?>,
    val description: Description,
    val locations: List<Location?>,
    val startDate: String,
    val expiryDate: String,
    val logo: Logo?,
    val points: Points,
    val website: String?,
    val offersDelivery: Boolean,
    val extraMediaAllowed: Boolean,
    val membership: Membership,
    val isVerifiedSeller: Boolean,
)

@Keep
private data class Category(
    val id: String,
    val name: String,
)

@Keep
private data class Phone(
    val number: String,
)

@Keep
private data class Description(
    val short: String?,
    val long: String?,
)

@Keep
private data class Location(
    val city: City?,
    val address: String?,
)

@Keep
private data class City(
    val id: String,
    val name: String,
)

@Keep
private data class Logo(
    val defaultPath: String,
)

@Keep
private data class Points(
    val count: Int,
    val expiryDate: String,
)

@Keep
private data class Membership(
    val id: String,
    val name: String,
)