package se.scmv.morocco.utils;

import static se.scmv.morocco.delivery.DeliveryConstants.DELIVERY_REMOTE_CONF_PRICE;

import java.util.HashMap;
import java.util.Map;

import se.scmv.morocco.Avito;
import se.scmv.morocco.R;

/**
 * Created by me<PERSON><PERSON><PERSON><PERSON> on 5/16/16.
 */
public class Keys {

    public static String IS_SORTING_KEY = "IS_SORTING_KEY";

    public static String SEARCH_CATEGORY_KEY = "SEARCH_CATEGORY_KEY";
    public static String SEARCH_CITY_KEY = "SEARCH_CITY_KEY";

    public static String AD_KEY = "AD_KEY";
    public static String LIST_ID_KEY = "LIST_ID_KEY";
    public static String AD_ID = "adId";
    public static String LIST_ID_KEY_FROM_NOTIFICATION = "LIST_ID_KEY_FROM_NOTIFICATION";
    public static String LIST_ID_KEY_BUMP_FROM_NOTIFICATION = "LIST_ID_KEY_BUMP_FROM_NOTIFICATION";
    public static String IS_COMING_FROM_DEEPLINK = "isComingFromDeepLink";
    public static String AD_ID_KEY = "AD_ID_KEY";
    public static String AD_CATEGORY_KEY = "AD_CATEGORY_KEY";
    public static String AD_CATEGORY_TYPE_KEY = "AD_CATEGORY_TYPE_KEY";
    public static String AD_TYPE_KEY = "AD_TYPE_KEY";

    public static String AD_EDIT_STATUS = "AD_EDIT_STATUS";
    public static String AD_EDIT_TO_AD_IMAGE_STEP = "AD_EDIT_TO_AD_IMAGE_STEP";

    public static String CONVERSATION_FLAG = "CONVERSATION_FLAG";
    public static int FROM_AD_VIEW = 1;

    public static String AD_IS_ACTIVE = "AD_IS_ACTIVE";
    public static String CHAT_IS_NEW = "chat_is_new";

    public static String FIRST_RUN = "first_run";

    //Properties
    public static final String AD_SOURCE = "AD_SOURCE";

    //messaging
    public static final String IS_ATTACHMENTS = "is_attachments";
    public static final String IS_DISPLAY_MESSAGE_STATUS = "is_display_message_status";
    public static final String IS_USER_TYPING_INDICATOR = "is_user_typing_indicator";
    public static final String IS_DISPLAY_AVATARS = "is_display_avatars";
    public static final String IS_LOCATION_MESSAGE = "is_location_message";
    public static final String KEY_NOTIFICATION_MC_COUNT = "key_notification_mc_count";
    public static final String GENERATED_SESSION_ID = "GENERATED_SESSION_ID";
    public static final String SEARCH_UUID = "search-uuid";
    public static final String PAGINATION_LOADER = "pagination_loader";

    //recommended ads
    public static final String REC_ALLOWED_CATEGORIES = "rec_allowed_categories";

    //Cognition
    public static final String IS_COGNITION_ENABLED = "is_cognition_enabled";

    // Draft Ad
    public static final String DRAFT_AD = "draft_ad_insert";
    public static final String DRAFT_AD_IMAGES = "draft_ad_images";

    //InAPP Update
    public static final String NEW_IN_APP_UPDATE_MINIMUM_ALLOWED_APP_VERSION = "new_in_app_update_minimum_allowed_app_version";
    public static final String IN_APP_UPDATE_SPECIFIC_APP_VERSIONS_TO_UPDATE = "in_app_update_specific_app_versions_to_update";
    public static final String IN_APP_UPDATE_MINIMUM_ALLOWED_ANDROID_VERSION = "in_app_update_minimum_allowed_android_version";
    public static final String IN_APP_UPDATE_SPECIFIC_ANDROID_VERSIONS_TO_UPDATE = "in_app_update_specific_android_versions_to_update";
    public static final String IN_APP_UPDATE_REQUEST_APP_UPDATE_ONCE_EVERY = "in_app_update_request_app_update_once_every";
    public static final String IN_APP_UPDATE_CANCEL_UPDATE_SNACKBAR_TEXT_AR = "in_app_update_cancel_update_snackbar_text_ar";
    public static final String IN_APP_UPDATE_CANCEL_UPDATE_SNACKBAR_TEXT = "in_app_update_cancel_update_snackbar_text";
    public static final String IN_APP_UPDATE_SIDE_MENU_UPDATE_ITEM_TEXT_AR = "in_app_update_side_menu_update_item_text_ar";
    public static final String IN_APP_UPDATE_SIDE_MENU_UPDATE_ITEM_TEXT = "in_app_update_side_menu_update_item_text";
    public static final String IN_APP_UPDATE_RECOMMENDED_UPDATE_TOGGLE = "in_app_update_recommended_update_toggle";
    public static final String IN_APP_UPDATE_MANDATORY_UPDATE_TOGGLE = "in_app_update_mandatory_update_toggle";
    public static final String IN_APP_UPDATE_FLEXIBLE_UPDATE_TOGGLE = "in_app_update_flexible_update_toggle";
    public static final String IN_APP_UPDATE_IMMEDIATE_UPDATE_TOGGLE = "in_app_update_immediate_update_toggle";
    public static final String IN_APP_UPDATE_WEEK_OF_YEAR = "in_app_update_weekOfYear";
    public static final String IN_APP_UPDATE_DAY_OF_YEAR = "in_app_update_dayOfYear";
    public static final String IN_APP_UPDATE_HOUR_OF_DAY = "in_app_update_hourOfDay";

    //Delivery
    public static final String DELIVERY_APPLY_ALL_CITIES = "delivery_apply_all_cities";
    //Search Suggestions
    public static final String SEARCH_EXPERIMENT_VARIATION = "avito.android.search.suggestions";

    //VAS
    public static final String BEST_SELLER_PACK_CATEGORY = "vas_best_seller_pack_category";
    public static final String BUMP_PACK_COLOR = "bump_pack_color";
    public static final String STAR_PACK_COLOR = "star_pack_color";
    public static final String STAR_BUMP_PACK_COLOR = "star_bump_pack_color";
    public static final String PREMIUM_PACK_COLOR = "premium_pack_color";
    public static final String INSERTION_PACK_COLOR = "insertion_pack_color";
    //VAS FR
    public static final String POPUP_BUMP_PITCH = "vas_bump_popup";
    public static final String POPUP_STAR_BUMP_PITCH = "vas_star_bump_popup";
    public static final String POPUP_STAR_PITCH = "vas_star_popup";
    public static final String POPUP_PREMIUM_PITCH = "vas_premium_popup";

    public static final String BUMP_PITCH = "vas_bump_pitch";
    public static final String STAR_PITCH = "vas_star_pitch";
    public static final String STAR_BUMP_PITCH = "vas_star_bump_pitch";
    public static final String PREMIUM_PITCH = "vas_premium_pitch";

    public static final String PREMIUM_BENEFITS = "vas_premium_benefits";
    public static final String STAR_BENEFITS = "vas_star_benefits";
    public static final String STAR_BUMP_BENEFITS = "vas_bump_star_benefits";
    public static final String BUMP_BENEFITS = "vas_bump_benefits";

    public static final String PREMIUM_TITLE = "vas_premium_title";
    public static final String STAR_TITLE = "vas_star_title";
    public static final String STAR_BUMP_TITLE = "vas_bump_star_title";
    public static final String BUMP_TITLE = "vas_bump_title";
    public static final String PAID_INSERTION_TITLE = "vas_paid_insertion_title";
    public static final String INSERTION_TITLE = "vas_insertion_title";
    public static final String INSERTION_UI_PRICE = "vas_insertion_ui_price";


    //VAS AR
    public static final String POPUP_BUMP_PITCH_AR = "vas_bump_popup_ar";
    public static final String POPUP_STAR_BUMP_PITCH_AR = "vas_star_bump_popup_ar";
    public static final String POPUP_STAR_PITCH_AR = "vas_star_popup_ar";
    public static final String POPUP_PREMIUM_PITCH_AR = "vas_premium_popup_ar";

    public static final String BUMP_PITCH_AR = "vas_bump_pitch_ar";
    public static final String STAR_PITCH_AR = "vas_star_pitch_ar";
    public static final String STAR_BUMP_PITCH_AR = "vas_star_bump_pitch_ar";
    public static final String PREMIUM_PITCH_AR = "vas_premium_pitch_ar";

    public static final String PREMIUM_BENEFITS_AR = "vas_premium_benefits_ar";
    public static final String STAR_BENEFITS_AR = "vas_star_benefits_ar";
    public static final String STAR_BUMP_BENEFITS_AR = "vas_bump_star_benefits_ar";
    public static final String BUMP_BENEFITS_AR = "vas_bump_benefits_ar";

    public static final String PREMIUM_TITLE_AR = "vas_premium_title_ar";
    public static final String STAR_TITLE_AR = "vas_star_title_ar";
    public static final String STAR_BUMP_TITLE_AR = "vas_star_bump_title_ar";
    public static final String BUMP_TITLE_AR = "vas_bump_title_ar";
    public static final String PAID_INSERTION_TITLE_AR = "vas_paid_insertion_title_ar";
    public static final String INSERTION_TITLE_AR = "vas_insertion_title_ar";
    public static final String INSERTION_UI_PRICE_AR = "vas_insertion_ui_price_ar";

    // Ad Insert number of pictures
    public static final String AI_MAX_PICTURES_COUNT = "ad_insert_max_pictures_count";

    //InRead Toggles
    public static final String INREAD_IS_ENABLED = "inread_is_enabled";
    public static final String INREAD_SHOW_VIDEO_DISMISS_CTA = "inread_show_video_dismiss_cta";

    //Touching Point Ads
    public final static String ACTIVE_TOUCHING_POINT_ADS = "active_touching_point_ads";

    //Email Verification
    public final static String EMAIL_VERIFICATION = "email_verification";

    //ECOMMERCE
    public static final String ECOMMERCE_TOGGLE = "ecommerce_enabled";
    public static final String SUPPORT_TEAM_NUMBER = "support_team_number";

    // ad view video
    public static final String VIDEO_FIRST = "video_first_position";

    //Remote Config
    public static final String IMMONEUF_MIX_SEARCH_ALLOWED_ENABLED = "immoneuf_mix_search_enabled";

    public static final String FILTER_CONFIG_WORKER_INTERVAL = "filter_worker_interval";
    
    //TikTok Remote Config - Supports multiple App IDs (comma-separated format for SDK >= 1.3.1)
    public static final String TIKTOK_APP_IDS_LIST = "tiktok_app_ids_list";

    public static Map<String, Object> getDefaultConfigs() {
        Map<String, Object> defaults = new HashMap<>();
        defaults.put(REC_ALLOWED_CATEGORIES, Avito.Companion.getContext().getString(R.string.rec_allowed_categories));
        defaults.put(CHAT_IS_NEW, true);
        defaults.put(IS_ATTACHMENTS, true);
        defaults.put(IS_DISPLAY_MESSAGE_STATUS, false);
        defaults.put(IS_USER_TYPING_INDICATOR, false);
        defaults.put(IS_DISPLAY_AVATARS, false);
        defaults.put(IS_LOCATION_MESSAGE, false);
        defaults.put(IS_COGNITION_ENABLED, false);
        defaults.putAll(getRemoteConfDefaultValues());
        defaults.put(NEW_IN_APP_UPDATE_MINIMUM_ALLOWED_APP_VERSION, 1091);
        defaults.put(IN_APP_UPDATE_SPECIFIC_APP_VERSIONS_TO_UPDATE, 1);
        defaults.put(IN_APP_UPDATE_MINIMUM_ALLOWED_ANDROID_VERSION, 1);
        defaults.put(IN_APP_UPDATE_SPECIFIC_ANDROID_VERSIONS_TO_UPDATE, 1);
        defaults.put(IN_APP_UPDATE_REQUEST_APP_UPDATE_ONCE_EVERY, "day");
        defaults.put(IN_APP_UPDATE_SIDE_MENU_UPDATE_ITEM_TEXT_AR, "تحديث");
        defaults.put(IN_APP_UPDATE_SIDE_MENU_UPDATE_ITEM_TEXT, "Mettre à jour");
        defaults.put(IN_APP_UPDATE_CANCEL_UPDATE_SNACKBAR_TEXT_AR, "تم إلغاء التحديث");
        defaults.put(IN_APP_UPDATE_CANCEL_UPDATE_SNACKBAR_TEXT, "La mise à jour a été annulée");
        defaults.put(IN_APP_UPDATE_RECOMMENDED_UPDATE_TOGGLE, false);
        defaults.put(IN_APP_UPDATE_MANDATORY_UPDATE_TOGGLE, false);
        defaults.put(IN_APP_UPDATE_FLEXIBLE_UPDATE_TOGGLE, false);
        defaults.put(IN_APP_UPDATE_IMMEDIATE_UPDATE_TOGGLE, false);
        defaults.put(SEARCH_EXPERIMENT_VARIATION, true);
        defaults.put(DELIVERY_APPLY_ALL_CITIES, true);
        defaults.put(ECOMMERCE_TOGGLE, true);
        defaults.put(SUPPORT_TEAM_NUMBER, "0663531981");

        defaults.put(AI_MAX_PICTURES_COUNT, Constants.AI_DEFAULT_MAX_PICTURES_COUNT);

        defaults.put(POPUP_BUMP_PITCH, "Avec l’option “Renouvellement d’annonce” Votre annonce sera republiée  automatiquement chaque jour et sera en tête de la liste;Votre annonce sera vue par 9 fois plus d’acheteurs potentiels qu’une annonce normale;Gagnez plus de temps en activant l’option “Renouvellement d’annonce”");
        defaults.put(POPUP_STAR_BUMP_PITCH, "Votre annonce sera re-publiée automatiquement chaque jour et en première position avec un arrière-plan unique et attractif;Votre annonce sera vue par 12 fois plus d’acheteurs potentiels qu’une annonce normale");
        defaults.put(POPUP_STAR_PITCH, "Affichez votre annonce dans la liste avec un arrière-plan différent et attractif;Votre annonce sera vue par 6 fois plus d’acheteurs potentiels qu’une annonce normale");
        defaults.put(POPUP_PREMIUM_PITCH, "Affichez votre annonce dans la section Premium, un emplacement exclusif et privilégié en grand format en tête de la liste d’annonces.;Votre annonce sera vue par 15 fois plus d’acheteurs potentiels qu’une annonce normale;Soyez démarqués sur la liste avec un arrière-plan différent et attractif");

        defaults.put(BUMP_PITCH, "Votre annonce sera placée en tête de liste automatiquement tous les jours pour une meilleure visibilité");
        defaults.put(STAR_PITCH, "Votre annonce sera affichée tout en étant en surbrillance avec un arrière-plan de couleur Jaune, pour une meilleure visibilité");
        defaults.put(STAR_BUMP_PITCH, "Votre annonce sera placée en tête de liste automatiquement tous les jours tout en étant en surbrillance avec un arrière-plan de couleur Jaune, pour une meilleure visibilité");
        defaults.put(PREMIUM_PITCH, "Affichez votre annonce dans la section Premium, un emplacement exclusif et privilégié en grand format en tête de la liste d’annonces");

        defaults.put(PREMIUM_BENEFITS, "15x plus de vues");
        defaults.put(BUMP_BENEFITS, "9x plus de vues");
        defaults.put(STAR_BENEFITS, "6x plus de vues");
        defaults.put(STAR_BUMP_BENEFITS, "12x plus de vues");

        defaults.put(INSERTION_TITLE, "Annonce gratuite");
        defaults.put(INSERTION_UI_PRICE, "Déposer sans Pack");
        defaults.put(PREMIUM_TITLE, "Annonce Premium");
        defaults.put(STAR_TITLE, "Annonce Star");
        defaults.put(STAR_BUMP_TITLE, "Tête de liste + Star");
        defaults.put(BUMP_TITLE, "Tête de liste");
        defaults.put(PAID_INSERTION_TITLE, "Insertion (sans pack)");


        defaults.put(POPUP_BUMP_PITCH_AR, " مع خيار \"تجديد الإعلان يوميا\" سيتم إعادة نشر إعلانك تلقائيًا كل يوم ليكون على رأس القائمة;سيشاهد إعلانك 9 مرات أكثر من طرف المشترين المحتملين; وفر المزيد من الوقت بتفعيل خيار \"تجديد الإعلان يوميا\"");
        defaults.put(POPUP_STAR_BUMP_PITCH_AR, "سيتم إعادة نشر إعلانك تلقائيًا كل يوم وليكون في المركز الأول بخلفية فريدة وجذابة.;سيتم إعادة نشر إعلانك تلقائيًا كل يوم وليكون في المركز الأول بخلفية فريدة وجذابة.");
        defaults.put(POPUP_STAR_PITCH_AR, " وفر المزيد من الوقت بتفعيل خيار \"تجديد الإعلان يوميا\";سيشاهد إعلانك 6 مرات أكثر من طرف المشترين المحتملين من الإعلان العادي");
        defaults.put(POPUP_PREMIUM_PITCH_AR, "إعرض إعلانك في قسم  (الإعلان المتميز) ، وهو موقع حصري ومميز بحجمة الكبير في أعلى قائمة الإعلانات.;سيشاهد إعلانك 15 مرة أكثر من طرف المشترين المحتملين;سيشاهد إعلانك 15 مرة أكثر من طرف المشترين المحتملين");

        defaults.put(BUMP_PITCH_AR, "سيتم إعادة نشر إعلانك تلقائيًا كل يوم وسيكون على رأس قائمة الإعلانات");
        defaults.put(STAR_PITCH_AR, "عرض إعلانك في القائمة بخلفية مختلفة وجذابة");
        defaults.put(STAR_BUMP_PITCH_AR, "سيتم إعادة نشر إعلانك تلقائيًا كل يوم في المركز الأول بخلفية مميزة وجذابة");
        defaults.put(PREMIUM_PITCH_AR, "اعرض إعلانك في قسم (الإعلان المتميز)، وهو قسم حصري ومميز بحجم كبير في أعلى قائمة الإعلانات");

        defaults.put(PREMIUM_BENEFITS_AR, "15x عدد المشاهدات");
        defaults.put(BUMP_BENEFITS_AR, "9x عدد المشاهدات");
        defaults.put(STAR_BENEFITS_AR, "6x عدد المشاهدات");
        defaults.put(STAR_BUMP_BENEFITS_AR, "12x عدد المشاهدات");

        defaults.put(INSERTION_TITLE_AR, "إعلان مجاني");
        defaults.put(INSERTION_UI_PRICE_AR, "إعلان بدون باقة");
        defaults.put(PREMIUM_TITLE_AR, "إعلان متميز");
        defaults.put(STAR_TITLE_AR, "إعلان نجم");
        defaults.put(STAR_BUMP_TITLE_AR, "إعلان نجم + تجديد الإعلان");
        defaults.put(BUMP_TITLE_AR, "تجديد الإعلان");
        defaults.put(PAID_INSERTION_TITLE_AR, "وضع الإعلان");

        defaults.put(BEST_SELLER_PACK_CATEGORY, "special");
        defaults.put(BUMP_PACK_COLOR, "#2e6bff");
        defaults.put(STAR_PACK_COLOR, "#e77652");
        defaults.put(STAR_BUMP_PACK_COLOR, "#8e83ff");
        defaults.put(PREMIUM_PACK_COLOR, "#f5a623");
        defaults.put(INSERTION_PACK_COLOR, "#2d2d2d");

        defaults.put(DELIVERY_REMOTE_CONF_PRICE, 30);

        defaults.put(INREAD_IS_ENABLED, false);
        defaults.put(INREAD_SHOW_VIDEO_DISMISS_CTA, true);
        defaults.put(VIDEO_FIRST, true);

        return defaults;
    }

    private static Map<String, Object> getRemoteConfDefaultValues() {
        HashMap<String, Object> map = new HashMap<>();
        map.put(IMMONEUF_MIX_SEARCH_ALLOWED_ENABLED, false);
        return map;
    }
}
