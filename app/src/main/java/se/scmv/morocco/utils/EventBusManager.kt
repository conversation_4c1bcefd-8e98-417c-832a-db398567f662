package se.scmv.morocco.utils

import org.greenrobot.eventbus.EventBus
import se.scmv.morocco.events.BusEvent

/**
 * Created by amine on 03/02/15.
 */
class EventBusManager private constructor() {
        private val mBus: EventBus = EventBus.getDefault()

        fun register(`object`: Any?) {
                if (!mBus.isRegistered(`object`)) mBus.register(`object`)
        }

        fun unregister(`object`: Any?) {
                mBus.unregister(`object`)
        }

        fun <T : BusEvent?> postEvent(t: T) {
                mBus.post(t)
        }

        fun <T : BusEvent?> postSticky(t: T) {
                mBus.postSticky(t)
        }

        fun <T : BusEvent?> removeStickyEvent(t: T) {
                mBus.removeStickyEvent(t)
        }

        companion object {
                private var mInstance: EventBusManager? = null

                @JvmStatic
                val instance: EventBusManager?
                        get() {
                                if (mInstance == null) {
                                        mInstance = EventBusManager()
                                }
                                return mInstance
                        }
        }

}