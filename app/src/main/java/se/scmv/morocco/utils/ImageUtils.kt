package se.scmv.morocco.utils

import android.content.ContentValues
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.media.ExifInterface
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.view.View
import se.scmv.morocco.Avito.Companion.context
import java.io.*
import java.text.SimpleDateFormat
import java.util.*

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 9/9/16.
 */
object ImageUtils {
        private const val SLASH = "/"

        /**
         * Returns Layout content as screenshot (Image).
         *
         * @param view
         * @return
         */
        fun getViewContentAsBitmap(view: View?): Bitmap? {
                var headerBitmap: Bitmap? = null
                try {
                        if (view != null) {
                                val width = view.width
                                val height = view.height
                                headerBitmap =
                                        Bitmap.createBitmap(width, height, Bitmap.Config.RGB_565)
                                view.draw(Canvas(headerBitmap))
                        }
                } catch (e: Exception) {
                        e.printStackTrace()
                }
                return headerBitmap
        }

        /**
         * Saves bitmap image in given fileName.
         *
         * @param bitmap
         * @param fileName
         */
        @Suppress("DEPRECATION")
        fun saveBitmapInFile(bitmap: Bitmap, fileName: String): File? {
                val imageOutStream: OutputStream
                val imageFile: File
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        val values = ContentValues().apply {
                                put(MediaStore.Images.Media.DISPLAY_NAME, fileName)
                                put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg")
                                put(
                                        MediaStore.Images.Media.RELATIVE_PATH,
                                        Environment.DIRECTORY_PICTURES
                                )
                        }
                        context?.contentResolver.run {
                                val uri =
                                        context?.contentResolver?.insert(
                                                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                                                values
                                        )
                                                ?: return null
                                imageFile = File(uri.path, fileName)
                                imageOutStream = context?.contentResolver?.openOutputStream(uri)
                                        ?: return null
                        }
                } else {
                        val imagePath =
                                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES).absolutePath
                        imageFile = File(imagePath, fileName)
                        imageOutStream = FileOutputStream(imageFile)
                }
                imageOutStream.use { bitmap.compress(Bitmap.CompressFormat.JPEG, 100, it) }
                imageOutStream.close()
                return imageFile
        }

        fun saveImageToStorage(
                bitmap: Bitmap,
                filename: String
        ): File? {
                val imageOutStream: OutputStream
                val imageFile: File
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        val values = ContentValues().apply {
                                put(MediaStore.Images.Media.DISPLAY_NAME, filename)
                                put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg")
                                put(
                                        MediaStore.Images.Media.RELATIVE_PATH,
                                        Environment.DIRECTORY_PICTURES
                                )
                        }
                        context?.contentResolver.run {
                                val uri =
                                        context?.contentResolver?.insert(
                                                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                                                values
                                        )
                                                ?: return null
                                imageFile = File(uri.path, filename)
                                imageOutStream = context?.contentResolver?.openOutputStream(uri)
                                        ?: return null
                        }
                } else {
                        val imagePath =
                                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES).absolutePath
                        imageFile = File(imagePath, filename)
                        imageOutStream = FileOutputStream(imageFile)
                }
                imageOutStream.use { bitmap.compress(Bitmap.CompressFormat.JPEG, 100, it) }
                imageOutStream.close()
                return imageFile
        }

        fun copyDataToFile(src: Uri?, dest: File, context: Context) {
                val mimeType = src?.let { context.contentResolver.getType(it) }
                var inputStream: InputStream? = null
                var fileOutputStream: FileOutputStream? = null
                try {
                        inputStream = src?.let { context.contentResolver.openInputStream(it) }
                        fileOutputStream = FileOutputStream(dest)
                        if (mimeType != null && mimeType.startsWith("image")) {
                                //Bitmap bitmap = BitmapFactory.decodeStream(inputStream);
                                val options = BitmapFactory.Options()
                                options.inPreferredConfig = Bitmap.Config.RGB_565
                                var bitmap: Bitmap? =
                                        BitmapFactory.decodeStream(inputStream, null, options)
                                                ?: throw IOException("Cannot decode image")
                                bitmap?.compress(Bitmap.CompressFormat.JPEG, 100, fileOutputStream)

                                // free some memory
                                if (bitmap != null) {
                                        bitmap.recycle()
                                        bitmap = null
                                }
                        } else {
                                val buffer = ByteArray(1024) // We'll read in one kB at a time
                                var len: Int
                                while (inputStream?.read(buffer).also { len = it!! }!! > 0) {
                                        fileOutputStream.write(buffer, 0, len)
                                }
                        }
                        // copying exif meta data
                        val originalExif = src?.let {
                                MediaCompressor.getPath(context, it)?.let { ExifInterface(it) }
                        }
                        val newExif = ExifInterface(dest.absolutePath)
                        newExif.setAttribute(
                                ExifInterface.TAG_ORIENTATION,
                                originalExif?.getAttribute(ExifInterface.TAG_ORIENTATION)
                        )
                        newExif.saveAttributes()
                } catch (e: Exception) {
                        e.printStackTrace()
                } finally {
                        if (fileOutputStream != null) {
                                try {
                                        fileOutputStream.close()
                                } catch (e: IOException) {
                                        e.printStackTrace()
                                }
                        }
                        if (inputStream != null) {
                                try {
                                        inputStream.close()
                                } catch (e: Exception) {
                                        e.printStackTrace()
                                }
                        }
                }
        }

        @JvmStatic
        fun buildAvitoImagePath(imagePath: String, rootPath: String?): String {
                return StringBuilder(rootPath)
                        .append(imagePath.substring(0, 2))
                        .append(SLASH)
                        .append(imagePath)
                        .toString()
        }

        @Throws(IOException::class)
        fun createImageFile(): File {
                // Create an image file name
                val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.FRANCE).format(Date())
                val imageFileName = "JPEG_" + timeStamp + "_"
                val storageDir =
                        Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)
                storageDir.mkdirs()
                return File.createTempFile(
                        imageFileName,  /* prefix */
                        ".jpg",  /* suffix */
                        storageDir /* directory */
                )
        }
}