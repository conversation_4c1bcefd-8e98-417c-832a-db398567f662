package se.scmv.morocco.utils

import android.app.ActivityManager
import android.content.Context
import com.bumptech.glide.Glide
import com.bumptech.glide.MemoryCategory
import com.google.firebase.crashlytics.FirebaseCrashlytics
import java.lang.ref.WeakReference

object MemoryUtils {
        @JvmStatic
        fun trimMemoryIfRequired(contextWeakReference: WeakReference<Context>) {
                try {
                        val memoryPercentageUsed: Int
                        if (contextWeakReference.get() != null) {
                                val activityManager = contextWeakReference.get()
                                        ?.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
                                val memoryInfo = ActivityManager.MemoryInfo()
                                activityManager.getMemoryInfo(memoryInfo)
                                memoryPercentageUsed = (Runtime.getRuntime()
                                        .totalMemory() * 100 / memoryInfo.threshold).toInt()
                                if (memoryPercentageUsed in 70..79) {
                                        contextWeakReference.get()?.let {
                                                Glide.get(it).bitmapPool.clearMemory()
                                        }
                                } else if (memoryPercentageUsed >= 80) {
                                        contextWeakReference.get()?.let {
                                                Glide.get(it).clearMemory()
                                                Glide.get(it).setMemoryCategory(MemoryCategory.LOW)
                                        }

                                }
                        }
                } catch (ae: ArithmeticException) {
                        ae.message?.let { FirebaseCrashlytics.getInstance().log(it) }
                        ae.printStackTrace()
                }

        }
}