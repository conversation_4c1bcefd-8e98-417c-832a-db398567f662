package se.scmv.morocco.utils

import android.app.Activity
import android.content.Context
import android.net.Uri
import android.provider.MediaStore
import android.util.Base64
import android.util.DisplayMetrics
import org.json.JSONException
import org.json.JSONObject
import java.io.UnsupportedEncodingException
import java.nio.charset.StandardCharsets
import java.util.*

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 9/20/16.
 *
 *
 * This is a String Utilities.
 * source: https://commons.apache.org/proper/commons-lang/apidocs/src-html/org/apache/commons/lang3/StringUtils.html
 */
object StringUtils {


        /**
         *
         * Checks if the CharSequence contains only Unicode digits.
         * A decimal point is not a Unicode digit and returns false.
         *
         *
         *
         * `null` will return `false`.
         * An empty CharSequence (length()=0) will return `false`.
         *
         *
         *
         * Note that the method does not allow for a leading sign, either positive or negative.
         * Also, if a String passes the numeric test, it may still generate a NumberFormatException
         * when parsed by Integer.parseInt or Long.parseLong, e.g. if the value is outside the range
         * for int or long respectively.
         *
         *
         * <pre>
         * StringUtils.isNumeric(null)   = false
         * StringUtils.isNumeric("")     = false
         * StringUtils.isNumeric("  ")   = false
         * StringUtils.isNumeric("123")  = true
         * StringUtils.isNumeric("\u0967\u0968\u0969")  = true
         * StringUtils.isNumeric("12 3") = false
         * StringUtils.isNumeric("ab2c") = false
         * StringUtils.isNumeric("12-3") = false
         * StringUtils.isNumeric("12.3") = false
         * StringUtils.isNumeric("-123") = false
         * StringUtils.isNumeric("+123") = false
        </pre> *
         *
         * @param cs the CharSequence to check, may be null
         * @return `true` if only contains digits, and is non-null
         * @since 3.0 Changed "" to return false and not true
         */
        @JvmStatic
        fun isNumeric(cs: CharSequence): Boolean {
                if (isEmpty(cs)) {
                        return false
                }
                val sz = cs.length
                for (i in 0 until sz) {
                        if (!Character.isDigit(cs[i])) {
                                return false
                        }
                }
                return true
        }

        /**
         *
         * Checks if the CharSequence contains a year in it.
         * `null` will return `false`.
         * An empty or blank CharSequence (length()=0 or CharSequence of spaces) will return `false`.
         *
         * @param cs the CharSequence to check, may be null
         * @return `true` if at least contains a year in it and not null or blank
         * @since 3.0 Changed "" to return false and not true
         */
        @JvmStatic
        fun hasYear(cs: CharSequence?): Boolean {
                if (cs.isNullOrBlank()) {
                        return false
                }
                val matcher = "\\b(19|20)\\d{2}".toRegex()
                return matcher.containsMatchIn(cs)
        }

        /**
         *
         * Checks if a CharSequence is empty ("") or null.
         *
         *
         * <pre>
         * StringUtils.isEmpty(null)      = true
         * StringUtils.isEmpty("")        = true
         * StringUtils.isEmpty(" ")       = false
         * StringUtils.isEmpty("bob")     = false
         * StringUtils.isEmpty("  bob  ") = false
        </pre> *
         *
         *
         *
         * NOTE: This method changed in Lang version 2.0.
         * It no longer trims the CharSequence.
         * That functionality is available in isBlank().
         *
         * @param cs the CharSequence to check, may be null
         * @return `true` if the CharSequence is empty or null
         * @since 3.0 Changed signature from isEmpty(String) to isEmpty(CharSequence)
         */
        fun isEmpty(cs: CharSequence?): Boolean {
                return cs == null || cs.isEmpty()
        }

        /**
         * Method that capitalizes first letter of word (or phrase)
         *
         * @param word
         * @return
         */
        fun capitalizeFirstLetter(word: String?): String? {
                return if (word != null && word.isNotEmpty()) word.substring(0, 1)
                        .uppercase(Locale.ROOT) + word.substring(1) else word
        }

        @JvmStatic
        fun getImageUrlLinkPerDeviseDensity(
                context: Context,
                baseUrl: String?,
                path: String?,
                vararg pathsToAppend: String?
        ): String {
                val resultUrl = StringBuilder()
                resultUrl.append(baseUrl)
                resultUrl.append(path)
                if (pathsToAppend.isNotEmpty()) {
                        for (element in pathsToAppend) {
                                resultUrl.append(element)
                        }
                }
                return appendPngPerDeviseDensity(context, resultUrl.toString())
        }

        fun getPaymentMethodUrlLinkPerDeviseDensity(
                context: Context,
                baseUrl: String?,
                path: String,
                vararg pathsToAppend: String?
        ): String {
                val resultUrl = StringBuilder()
                resultUrl.append(baseUrl)
                resultUrl.append(path.substring(1))
                if (pathsToAppend.isNotEmpty()) {
                        for (element in pathsToAppend) {
                                resultUrl.append(element)
                        }
                }
                return appendPngPerDeviseDensity(context, resultUrl.toString())
        }

        fun appendPngPerDeviseDensity(context: Context, url: String?): String {
                val resultUrl = StringBuilder(url)
                val densityDpi = context.resources.displayMetrics.densityDpi
                if (densityDpi <= DisplayMetrics.DENSITY_MEDIUM) {
                        resultUrl.append("_m.png")
                } else if (densityDpi > DisplayMetrics.DENSITY_MEDIUM && densityDpi <= DisplayMetrics.DENSITY_HIGH) {
                        resultUrl.append("_h.png")
                } else if (densityDpi > DisplayMetrics.DENSITY_HIGH && densityDpi <= DisplayMetrics.DENSITY_XHIGH) {
                        resultUrl.append("_x.png")
                } else if (densityDpi > DisplayMetrics.DENSITY_XHIGH && densityDpi <= DisplayMetrics.DENSITY_XXHIGH) {
                        resultUrl.append("_xx.png")
                } else if (densityDpi > DisplayMetrics.DENSITY_XXHIGH && densityDpi <= DisplayMetrics.DENSITY_XXXHIGH) {
                        resultUrl.append("_xxx.png")
                } else resultUrl.append("_h.png")
                return resultUrl.toString()
        }

        /**
         * @param aiResponse Example  {"status":201,"code":"AD_INSERT_SUCCESS","message":"","data":{"accountId":"1873033","adId":"********"}}
         * @return 1873033
         */
        @JvmStatic
        fun extractReturnedParamFromTheResponse(param: String?, aiResponse: String?): String {
                try {
                        val jsonObject = JSONObject(aiResponse)
                        val dataJsonObject = jsonObject.getJSONObject("data")
                        return dataJsonObject.getString(param)
                } catch (e: JSONException) {
                        e.printStackTrace()
                }
                return "0"
        }

        @JvmStatic
        @Throws(Exception::class)
        fun getSessionId(JWTEncoded: String): String? {
                try {
                        val split = JWTEncoded.split("\\.".toRegex()).toTypedArray()
                        return JSONObject(getJson(split[1]))["sessionId"].toString()
                } catch (e: UnsupportedEncodingException) {
                }
                return null
        }

        @Throws(UnsupportedEncodingException::class)
        private fun getJson(strEncoded: String): String {
                val decodedBytes = Base64.decode(strEncoded, Base64.URL_SAFE)
                return String(decodedBytes, StandardCharsets.UTF_8)
        }


        @JvmStatic
        fun extractReturnedLapzoneFromTheResponse(aiResponse: String?): String {
                try {
                        val jsonObject = JSONObject(aiResponse)
                        val dataJsonObject = jsonObject.getJSONObject("data")
                        return dataJsonObject.getString("lapZone")
                } catch (e: JSONException) {
                        e.printStackTrace()
                }
                return "0"
        }

        private var uniqueID: String? = null
        private const val PREF_UNIQUE_ID = "PREF_UNIQUE_ID"

        @JvmStatic
        @Synchronized
        fun getUniqueID(context: Context): String? {
                if (uniqueID == null) {
                        val sharedPrefs = context.getSharedPreferences(
                                PREF_UNIQUE_ID, Context.MODE_PRIVATE
                        )
                        uniqueID = sharedPrefs.getString(PREF_UNIQUE_ID, null)
                        if (uniqueID == null) {
                                uniqueID = UUID.randomUUID().toString()
                                val editor = sharedPrefs.edit()
                                editor.putString(PREF_UNIQUE_ID, uniqueID)
                                editor.apply()
                        }
                }
                return uniqueID
        }

        fun getRealPathFromURIPath(contentURI: Uri, activity: Activity): String? {
                val cursor = activity.contentResolver.query(contentURI, null, null, null, null)
                return if (cursor == null) {
                        contentURI.path
                } else {
                        cursor.moveToFirst()
                        val idx = cursor.getColumnIndex(MediaStore.Images.ImageColumns.DATA)
                        if (idx != -1)
                                cursor.getString(idx)
                        else contentURI.toString()
                }
        }


        fun isStore(uuid: String): Boolean {
                return uuid.startsWith("S", true)
        }

        fun extractUserIdFrom(uuid: String): String {
                return uuid.substring(1)
        }

        fun getLastThirtyDaysMonth(): List<String> {
                try {
                        val listofdays = ArrayList<String>()
                        var i = -30
                        while (i < 1) {
                                val cal = GregorianCalendar()
                                cal.add(Calendar.DATE, i)
                                val test = cal.time.toLocaleString()
                                listofdays.add(test.substring(0, 7).replace(",", ""))
                                i++
                        }
                        return listofdays
                } catch (e: Exception) {
                        val listofdays = ArrayList<String>()
                        var i = -30
                        while (i < 1) {
                                listofdays.add("")
                                i++
                        }
                        return listofdays
                }
        }

        @JvmStatic
        @Throws(Exception::class)
        fun getUserName(JWTEncoded: String): String? {
                try {
                        val split = JWTEncoded.split("\\.".toRegex()).toTypedArray()
                        if (split.size >= 2) {
                                val payload = getJson(split[1])
                                val jsonObject = JSONObject(payload)
                                if (jsonObject.has("name")) {
                                        return jsonObject.getString("name")
                                }
                        }
                } catch (e: JSONException) {
                        // Handle JSON parsing error
                        e.printStackTrace() // Log the error for debugging
                } catch (e: UnsupportedEncodingException) {
                        // Handle UnsupportedEncodingException
                        e.printStackTrace() // Log the error for debugging
                }
                return null
        }
}