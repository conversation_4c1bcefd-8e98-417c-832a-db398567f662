package se.scmv.morocco.utils

import se.scmv.morocco.dao.CityRecord
import se.scmv.morocco.dao.DaoManager

/**
 * Created by h<PERSON><PERSON><PERSON><PERSON><PERSON> on 10/11/16.
 */
/**
 * Class that does the correspondance between ids and names of cities and categories_fr
 * Can be extended to map other things in the future
 */
class Mapping {
        private val dao = DaoManager.getInstance()

        /**
         * Method that returns the name of a given city via its id
         * @param id
         * @return
         */
        fun getCitybyId(id: Int, lang: String): String {
                val city = dao.getById(CityRecord::class.java, CityRecord.CITY_ID, id.toLong())
                return if (city != null && !city.isEmpty()) {
                        if (lang == "fr") {
                                if (city[0] != null) city[0]?.label?.frLabel.toString() else ""
                        } else {
                                if (city[0] != null) city[0]?.label?.arLabel.toString() else ""
                        }
                } else ""
        }
}