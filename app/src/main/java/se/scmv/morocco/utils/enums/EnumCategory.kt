package se.scmv.morocco.utils.enums

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 9/19/16.
 *
 *
 * This class is a representation of categories.json
 */
enum class EnumCategory(val category: Int) {
    DEFAULT(0),
    IMMO(1000),
    VEHICULES(2000),
    MAISON_JARDIN(3000),
    LOISIR(4000),
    INFO_MULTIMEDIA(5000),
    EMPLOI_SERVICE(6000),
    AUTRE(7000),
    HABILLEMENT(8000),
    ENTREPRISES(9000);

    companion object {
        fun fromId(categoryId: Int): EnumCategory? {
            for (cat in entries) {
                if (categoryId == cat.category) {
                    return cat
                }
            }
            return null
        }
    }
}
