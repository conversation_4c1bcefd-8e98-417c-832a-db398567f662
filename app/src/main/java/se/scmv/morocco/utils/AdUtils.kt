package se.scmv.morocco.utils

import java.time.LocalDate
import java.time.temporal.ChronoUnit

/**
 * Created by h<PERSON><PERSON><PERSON><PERSON><PERSON> on 3/2/17.
 */
object AdUtils {

        /**
         * @param adPrice
         * @return
         */
        fun getPriceForDisplay(adPrice: Int?, currency: String, fallback: String): String {
                return if (adPrice != null) Utils.getSpaceSeparatedPrice(adPrice) + " " + currency else fallback
        }
}

fun Any.isDateOlderThanOneWeek(date: LocalDate): Boolean {
        val currentDate = LocalDate.now()
        val daysBetween = ChronoUnit.DAYS.between(date, currentDate)
        return daysBetween > 7
}

fun String.splitDate(): LocalDate {
        val parts = this.split("-")
        val year = parts[0].toInt()
        val month = parts[1].toInt()
        val day = parts[2].substringBefore("T").toInt()

        return LocalDate.of(year, month, day)
}