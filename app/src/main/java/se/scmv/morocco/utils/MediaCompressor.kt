package se.scmv.morocco.utils

import android.annotation.SuppressLint
import android.content.ContentUris
import android.content.Context
import android.database.Cursor
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.media.ExifInterface
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.DocumentsContract
import android.provider.MediaStore
import se.scmv.morocco.utils.ConnectivityUtils.isConnectedFast
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.IOException

object MediaCompressor {
        private fun calculateInSampleSize(
                options: BitmapFactory.Options,
                reqWidth: Int,
                reqHeight: Int
        ): Int {
                // Raw height and width of image
                val height = options.outHeight
                val width = options.outWidth
                var inSampleSize = 1
                if (height > reqHeight || width > reqWidth) {
                        val halfHeight = height / 2
                        val halfWidth = width / 2
                        // Calculate the largest inSampleSize value that is a power of 2 and keeps both
                        // height and width larger than the requested height and width.
                        while (halfHeight / inSampleSize >= reqHeight
                                && halfWidth / inSampleSize >= reqWidth
                        ) {
                                inSampleSize *= 2
                        }
                }
                return inSampleSize
        }

        private fun decodeSampledBitmapFromFile(
                path: String,
                reqWidth: Int,
                reqHeight: Int
        ): Bitmap? {
                // First decode with inJustDecodeBounds=true to check dimensions
                return try {
                        val options = BitmapFactory.Options()
                        options.inJustDecodeBounds = true
                        BitmapFactory.decodeFile(path, options)
                        // Calculate inSampleSize
                        options.inSampleSize = calculateInSampleSize(options, reqWidth, reqHeight)

                        // Decode bitmap with inSampleSize set
                        options.inJustDecodeBounds = false
                        BitmapFactory.decodeFile(path, options)
                } catch (e: OutOfMemoryError) {
                        null
                }
        }

        fun compressImage(context: Context?, path: String, reqWidth: Int, reqHeight: Int): Bitmap? {
                val quality: Int
                quality = if (context?.let { isConnectedFast(it) } == true) 100 else 70
                val bitmap = decodeSampledBitmapFromFile(path, reqWidth, reqHeight)
                if (bitmap != null) {
                        var os: FileOutputStream? = null
                        return try {
                                val exif = ExifInterface(path)
                                val bitmapRotated = rotateBitmap(bitmap, exif)
                                if (bitmapRotated != null) {
                                        os = FileOutputStream(File(path))
                                        bitmapRotated.compress(
                                                Bitmap.CompressFormat.JPEG,
                                                quality,
                                                os
                                        )
                                        bitmapRotated
                                } else if (bitmap != null) {
                                        os = FileOutputStream(File(path))
                                        bitmap.compress(Bitmap.CompressFormat.JPEG, quality, os)
                                        bitmap
                                } else {
                                        null
                                }
                        } catch (e: FileNotFoundException) {
                                e.printStackTrace()
                                null
                        } catch (e: Exception) {
                                e.printStackTrace()
                                null
                        } finally {
                                if (os != null) try {
                                        os.close()
                                } catch (e: IOException) {
                                        e.printStackTrace()
                                }
                        }
                }
                return null
        }

        /**
         * This allows to rotate a bitmap image.
         *
         * @param bitmap        the image
         * @param exifInterface exif interface of the bitmap to extract the orientation angle
         * @return a new rotated butmap
         */
        fun rotateBitmap(bitmap: Bitmap, exifInterface: ExifInterface): Bitmap? {
                // get the angle of rotation
                val matrix = Matrix()
                val orientation = exifInterface.getAttributeInt(
                        ExifInterface.TAG_ORIENTATION,
                        ExifInterface.ORIENTATION_UNDEFINED
                )
                when (orientation) {
                        ExifInterface.ORIENTATION_NORMAL -> return bitmap
                        ExifInterface.ORIENTATION_FLIP_HORIZONTAL -> matrix.setScale(-1f, 1f)
                        ExifInterface.ORIENTATION_ROTATE_180 -> matrix.setRotate(180f)
                        ExifInterface.ORIENTATION_FLIP_VERTICAL -> {
                                matrix.setRotate(180f)
                                matrix.postScale(-1f, 1f)
                        }
                        ExifInterface.ORIENTATION_TRANSPOSE -> {
                                matrix.setRotate(90f)
                                matrix.postScale(-1f, 1f)
                        }
                        ExifInterface.ORIENTATION_ROTATE_90 -> matrix.setRotate(90f)
                        ExifInterface.ORIENTATION_TRANSVERSE -> {
                                matrix.setRotate(-90f)
                                matrix.postScale(-1f, 1f)
                        }
                        ExifInterface.ORIENTATION_ROTATE_270 -> matrix.setRotate(-90f)
                        else -> return bitmap
                }

                // rotate th bitmap
                return try {
                        val bmRotated = Bitmap.createBitmap(
                                bitmap,
                                0,
                                0,
                                bitmap.width,
                                bitmap.height,
                                matrix,
                                true
                        )
                        bitmap.recycle()
                        bmRotated
                } catch (e: OutOfMemoryError) {
                        e.printStackTrace()
                        null
                }
        }

        @SuppressLint("NewApi")
        fun getPath(context: Context, uri: Uri): String? {
                val isKitKat = Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT

                // DocumentProvider
                if (isKitKat && DocumentsContract.isDocumentUri(context, uri)) {
                        // ExternalStorageProvider
                        if (isExternalStorageDocument(uri)) {
                                val docId = DocumentsContract.getDocumentId(uri)
                                val split = docId.split(":".toRegex()).toTypedArray()
                                val type = split[0]
                                if ("primary".equals(type, ignoreCase = true)) {
                                        return Environment.getExternalStorageDirectory()
                                                .toString() + "/" + split[1]
                                }

                                // TODO handle non-primary volumes
                        } else if (isDownloadsDocument(uri)) {
                                val id = DocumentsContract.getDocumentId(uri)
                                val contentUri = ContentUris.withAppendedId(
                                        Uri.parse("content://downloads/public_downloads"),
                                        java.lang.Long.valueOf(id)
                                )
                                return getDataColumn(context, contentUri, null, null)
                        } else if (isMediaDocument(uri)) {
                                val docId = DocumentsContract.getDocumentId(uri)
                                val split = docId.split(":".toRegex()).toTypedArray()
                                val type = split[0]
                                var contentUri: Uri? = null
                                if ("image" == type) {
                                        contentUri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
                                } else if ("video" == type) {
                                        contentUri = MediaStore.Video.Media.EXTERNAL_CONTENT_URI
                                } else if ("audio" == type) {
                                        contentUri = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI
                                }
                                val selection = "_id=?"
                                val selectionArgs = arrayOf(
                                        split[1]
                                )
                                return getDataColumn(context, contentUri, selection, selectionArgs)
                        }
                } else if ("content".equals(uri.scheme, ignoreCase = true)) {
                        return getDataColumn(context, uri, null, null)
                } else if ("file".equals(uri.scheme, ignoreCase = true)) {
                        return uri.path
                }
                return null
        }

        /**
         * Get the value of the data column for this Uri. This is useful for
         * MediaStore Uris, and other file-based ContentProviders.
         *
         * @param context       The context.
         * @param uri           The Uri to query.
         * @param selection     (Optional) Filter used in the query.
         * @param selectionArgs (Optional) Selection arguments used in the query.
         * @return The value of the _data column, which is typically a file path.
         */
        private fun getDataColumn(
                context: Context,
                uri: Uri?,
                selection: String?,
                selectionArgs: Array<String>?
        ): String? {
                var cursor: Cursor? = null
                val column = "_data"
                val projection = arrayOf(
                        column
                )
                try {
                        cursor = uri?.let {
                                context.contentResolver.query(
                                        it, projection, selection, selectionArgs,
                                        null
                                )
                        }
                        if (cursor != null && cursor.moveToFirst()) {
                                val column_index = cursor.getColumnIndexOrThrow(column)
                                return cursor.getString(column_index)
                        }
                } finally {
                        cursor?.close()
                }
                return null
        }

        /**
         * @param uri The Uri to check.
         * @return Whether the Uri authority is ExternalStorageProvider.
         */
        private fun isExternalStorageDocument(uri: Uri): Boolean {
                return "com.android.externalstorage.documents" == uri.authority
        }

        /**
         * @param uri The Uri to check.
         * @return Whether the Uri authority is DownloadsProvider.
         */
        private fun isDownloadsDocument(uri: Uri): Boolean {
                return "com.android.providers.downloads.documents" == uri.authority
        }

        /**
         * @param uri The Uri to check.
         * @return Whether the Uri authority is MediaProvider.
         */
        private fun isMediaDocument(uri: Uri): Boolean {
                return "com.android.providers.media.documents" == uri.authority
        }
}