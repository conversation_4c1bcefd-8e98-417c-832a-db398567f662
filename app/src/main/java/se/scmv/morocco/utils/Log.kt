package se.scmv.morocco.utils

import android.util.Log
import se.scmv.morocco.BuildConfig

object Log {
        val DEBUG = BuildConfig.IS_DEVELOPER_MODE

        @JvmStatic
        fun d(tag: String?, message: String?) {
                if (DEBUG && tag != null && message != null) Log.d(tag, message)
        }

        @JvmStatic
        fun e(tag: String?, message: String?) {
                if (DEBUG && tag != null && message != null) Log.e(tag, message)
        }

        @JvmStatic
        fun i(tag: String?, message: String?) {
                if (DEBUG && tag != null && message != null) Log.i(tag, message)
        }
}