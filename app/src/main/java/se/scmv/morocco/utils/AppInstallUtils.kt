package se.scmv.morocco.utils

import se.scmv.morocco.Avito
import se.scmv.morocco.BuildConfig

class AppInstallUtils {

        companion object {

                private const val PREF_VERSION_CODE_KEY = "app_version_code"
                private const val DOES_NOT_EXIST = -1

                /**
                 * This method helps identify the status of the app run
                 * New Install - Update - Regular Run
                 */
                fun checkFirstRun() {
                        val oldVersionCodeKey =
                                Utils.getIntPreference(Avito.context, "version_code")
                        if (oldVersionCodeKey != DOES_NOT_EXIST) {
                                Utils.removePreference(Avito.context, "version_code")
                        }
                        Utils.removePreference(Avito.context, Keys.ACTIVE_TOUCHING_POINT_ADS)
                        val currentVersionCode: Int = BuildConfig.VERSION_CODE

                        Utils.savePreference(
                                Avito.context,
                                PREF_VERSION_CODE_KEY,
                                currentVersionCode
                        )
                }

        }
}