package se.scmv.morocco.utils;

import static se.scmv.morocco.delivery.DeliveryConstants.DELIVERY_REMOTE_CONF_PRICE;

import android.app.Activity;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.graphics.Typeface;
import android.os.Build;
import android.text.Html;
import android.text.Spanned;
import android.view.View;

import androidx.appcompat.app.AlertDialog;
import androidx.core.content.res.ResourcesCompat;

import com.google.android.gms.common.ConnectionResult;
import com.google.android.gms.common.GoogleApiAvailability;
import com.google.firebase.remoteconfig.FirebaseRemoteConfig;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

import se.scmv.morocco.Avito;
import se.scmv.morocco.R;
import se.scmv.morocco.common.lang.LocaleManager;


public class Utils {

    public static final String APP_ENV = "app_environment";
    public static final String USER_LANGUAGE = "userLang";
    public static final String DARK_MODE_ENABLED = "darkMode";
    public static final String APP_FLAVOR = "appFlavor";
    public static final String PREVIOUS_LANG = "previous_lang";
    public static final String PREF_IS_CONFIG_LOADED = "is_config_loaded";
    public static final String PREF_DELIVERY_ADDRESS = "delivery_address";

    public static final String PREF_IS_FIRST_LAUNCHED_AFTER_INSTALL = "is_first_installed";
    // Loading Categories - Ad Types - Regions - Sectors from config
    public static final String CATEGORIES_AD_TYPES_LOADED = "is_categories_loaded_from_config";
    public static final String REGIONS_SECTORS_LOADED = "is_regions_loaded_from_config";

    // New messaging
    public static final String CONVERSATIONS_ACTION = "conversations_action_block_unblock_clear";

    //App Instance Id
    public static final String FIREBASE_ANALYTICS_INSTANCE_ID = "FIREBASE_ANALYTICS_INSTANCE_ID";
    // special for Avito
    public static final String PREFS_FAVORITE_ADS_SIZE = "fav_size";
    public static final List<Locale> SUPPORTED_LOCALES =
            Arrays.asList(
                    new Locale("fr", "MA"),
                    new Locale("ar", "MA")
            );

    private static final String APP_PREFS = "avito_prefs";
    private static final AtomicInteger sNextGeneratedId = new AtomicInteger(1);

    public static SharedPreferences getSharedPreferences(Context ctx) {
        if (ctx == null)
            return null;

        return ctx.getSharedPreferences(APP_PREFS, Context.MODE_PRIVATE);
    }

    public static final String md5(final String s) {
        final String MD5 = "MD5";
        try {
            // Create MD5 Hash
            MessageDigest digest = java.security.MessageDigest
                    .getInstance(MD5);
            digest.update(s.getBytes());
            byte[] messageDigest = digest.digest();

            // Create Hex String
            StringBuilder hexString = new StringBuilder();
            for (byte aMessageDigest : messageDigest) {
                String h = Integer.toHexString(0xFF & aMessageDigest);
                while (h.length() < 2)
                    h = "0" + h;
                hexString.append(h);
            }
            return hexString.toString();

        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return "";
    }

    public static boolean isProbablyArabic(String s) {
        for (int i = 0; i < s.length(); ) {
            int c = s.codePointAt(i);
            if (c >= 0x0600 && c <= 0x06E0)
                return true;
            i += Character.charCount(c);
        }
        return false;
    }

    public static void savePreference(Context ctx, String key, Object value) {
        if (ctx == null)
            return;

        SharedPreferences prefs = ctx.getSharedPreferences(APP_PREFS, Context.MODE_PRIVATE);
        if (value instanceof Boolean)
            prefs.edit().putBoolean(key, (Boolean) value).commit();
        else if (value instanceof Integer)
            prefs.edit().putInt(key, (Integer) value).commit();
        else if (value instanceof Long)
            prefs.edit().putLong(key, (Long) value).commit();
        else if (value instanceof Float)
            prefs.edit().putFloat(key, (Float) value).commit();
        else if (value instanceof String)
            prefs.edit().putString(key, (String) value).commit();

    }

    public static void addValueToSetPreference(Context ctx, String key, Object value) {
        if (ctx == null)
            return;
        SharedPreferences prefs = ctx.getSharedPreferences(APP_PREFS, Context.MODE_PRIVATE);
        if (value instanceof String) {
            Set<String> currentSet = prefs.getStringSet(key, null);
            Set<String> newSet = new HashSet<>();
            newSet.add((String) value);
            if (currentSet != null)
                newSet.addAll(currentSet);
            prefs.edit().putStringSet(key, newSet).commit();
        }
    }

    public static void removePreference(Context ctx, String key) {
        if (ctx == null)
            return;

        SharedPreferences prefs = ctx.getSharedPreferences(APP_PREFS, Context.MODE_PRIVATE);
        prefs.edit().remove(key).commit();
    }

    public static void rebirthApp(Context ctx) {
        PackageManager packageManager = ctx.getPackageManager();
        Intent intent = packageManager.getLaunchIntentForPackage(ctx.getPackageName());
        ComponentName componentName = intent.getComponent();
        Intent mainIntent = Intent.makeRestartActivityTask(componentName);
        ctx.startActivity(mainIntent);
        Runtime.getRuntime().exit(0);
    }

    public static Typeface getFont(Context context, int font) {
        try {
            return ResourcesCompat.getFont(context, font);
        } catch (Exception e) {
            Log.e("DEBUG", "GOOGLE PLAY NOT INSTALLED");
            return Typeface.DEFAULT;
        }
    }

    public static String getStringPreference(Context ctx, String key) {
        if (ctx == null)
            return null;

        SharedPreferences prefs = ctx.getSharedPreferences(APP_PREFS, Context.MODE_PRIVATE);
        return prefs.getString(key, null);
    }

    public static Long getLongPreference(Context ctx, String key){
        if (ctx == null)
            return null;

        SharedPreferences prefs = ctx.getSharedPreferences(APP_PREFS, Context.MODE_PRIVATE);
        return prefs.getLong(key, 1);
    }

    public static Set<String> getSetPreference(Context ctx, String key) {
        if (ctx == null)
            return null;
        SharedPreferences prefs = ctx.getSharedPreferences(APP_PREFS, Context.MODE_PRIVATE);
        return prefs.getStringSet(key, null);
    }

    public static String getVasPackTitle(Context ctx, String vasCategory) {
        if (ctx == null)
            return null;
        if (LocaleManager.INSTANCE.isFr()) {
            switch (vasCategory) {
                case "bump":
                    return getStringPreference(ctx, Keys.BUMP_TITLE);
                case "highlight":
                    return getStringPreference(ctx, Keys.STAR_TITLE);
                case "gallery":
                    return getStringPreference(ctx, Keys.PREMIUM_TITLE);
                case "special":
                    return getStringPreference(ctx, Keys.STAR_BUMP_TITLE);
                case "insertion":
                    return getStringPreference(ctx, Keys.PAID_INSERTION_TITLE);
                default:
                    return "";
            }
        } else {
            switch (vasCategory) {
                case "bump":
                    return getStringPreference(ctx, Keys.BUMP_TITLE_AR);
                case "highlight":
                    return getStringPreference(ctx, Keys.STAR_TITLE_AR);
                case "gallery":
                    return getStringPreference(ctx, Keys.PREMIUM_TITLE_AR);
                case "special":
                    return getStringPreference(ctx, Keys.STAR_BUMP_TITLE_AR);
                case "insertion":
                    return getStringPreference(ctx, Keys.PAID_INSERTION_TITLE_AR);
                default:
                    return "";
            }
        }
    }

    public static String getNonNullStringPreference(Context ctx, String key) {
        if (ctx == null)
            return "";

        SharedPreferences prefs = ctx.getSharedPreferences(APP_PREFS, Context.MODE_PRIVATE);
        return prefs.getString(key, "");
    }

    public static String getStringPreferenceWithDefaultValue(Context ctx, String key, String defaultValue) {
        if (ctx == null)
            return null;

        SharedPreferences prefs = ctx.getSharedPreferences(APP_PREFS, Context.MODE_PRIVATE);
        return prefs.getString(key, defaultValue);
    }

    //TODO All Preferences helpers should be refactored(Avoid getting Context of Activity,Fragment.....) and get The Application Context
    public static int getIntPreference(Context ctx, String key) {
        if (ctx == null)
            return -1;

        SharedPreferences prefs = ctx.getSharedPreferences(APP_PREFS, Context.MODE_PRIVATE);
        return prefs.getInt(key, -1);
    }

    public static boolean getBooleanPreference(Context ctx, String key) {
        if (ctx == null)
            return false;

        SharedPreferences prefs = ctx.getSharedPreferences(APP_PREFS, Context.MODE_PRIVATE);
        return prefs.getBoolean(key, false);
    }

    public static boolean getBooleanPreference(Context ctx, String key, boolean defaultValue) {
        if (ctx == null)
            return defaultValue;

        SharedPreferences prefs = ctx.getSharedPreferences(APP_PREFS, Context.MODE_PRIVATE);
        return prefs.getBoolean(key, defaultValue);
    }

    public static String getStringResourceByName(Context context, String aString) {
        try {
            String packageName = context.getPackageName();
            int resId = context.getResources().getIdentifier(aString, "string", packageName);
            return context.getResources().getString(resId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return aString;
    }

    /**
     * Returns an HTML formatted text.
     *
     * @param message message to format
     * @return html result
     */
    public static Spanned fromHtml(String message) {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
            return Html.fromHtml(message, Html.FROM_HTML_MODE_LEGACY);
        } else {
            return Html.fromHtml(message);
        }
    }

    /**
     * Check the device to make sure it has the Google Play Services APK. If
     * it doesn't, display a dialog that allows users to download the APK from
     * the Google Play Store or enable it in the device's system settings.
     */
    public static boolean checkPlayServices(Activity activity) {

        final int PLAY_SERVICES_RESOLUTION_REQUEST = 9400;

        GoogleApiAvailability apiAvailability = GoogleApiAvailability.getInstance();
        int resultCode = apiAvailability.isGooglePlayServicesAvailable(activity);
        if (resultCode != ConnectionResult.SUCCESS) {
            if (apiAvailability.isUserResolvableError(resultCode)) {
                apiAvailability.getErrorDialog(activity, resultCode, PLAY_SERVICES_RESOLUTION_REQUEST).show();
            } else {
                Log.d(activity.getLocalClassName(), "This device is not supported.");
                //activity.finish();
            }
            return false;
        }
        return true;
    }

    /**
     * Shows an error dialog
     *
     * @param context context
     */
    public static void showNoInternetDialog(Context context) {
        new AlertDialog.Builder(context)
                .setIcon(R.mipmap.ic_launcher)
                .setTitle(R.string.app_name)
                .setMessage(R.string.offline_message)
                .setCancelable(false)
                .setPositiveButton(android.R.string.ok, null)
                .show();
    }

    /**
     * return price formated instance ".".
     * ForEx if input is "12345678" then output="12.345.678"
     *
     * @param price
     * @return
     */
    public static String getSpaceSeparatedPrice(int price) {
        DecimalFormat fmt = new DecimalFormat();
        DecimalFormatSymbols fmts = new DecimalFormatSymbols();
        fmts.setGroupingSeparator('\u00A0');
        fmt.setGroupingSize(3);
        fmt.setGroupingUsed(true);
        fmt.setDecimalFormatSymbols(fmts);
        return fmt.format(price);
    }

    /**
     * Generate a value suitable for use in {.
     * This value will not collide with ID values generated at build time by aapt for R.id.
     *
     * @return a generated ID value
     */
    public static int generateViewId() {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.JELLY_BEAN_MR1) {
            for (; ; ) {
                final int result = sNextGeneratedId.get();
                // aapt-generated IDs have the high byte nonzero; clamp to the range under that.
                int newValue = result + 1;
                if (newValue > 0x00FFFFFF) newValue = 1; // Roll over to 1, not 0.
                if (sNextGeneratedId.compareAndSet(result, newValue)) {
                    return result;
                }
            }

        } else {
            return View.generateViewId();
        }

    }

    public static String generateSessionId() {
        return getNonNullStringPreference(Avito.Companion.getContext(), Keys.GENERATED_SESSION_ID);
    }

    public static void updateRemoteConfigPreferences(Context ctx, FirebaseRemoteConfig remoteConfig) {
        Utils.savePreference(ctx, Keys.CHAT_IS_NEW, remoteConfig.getBoolean(Keys.CHAT_IS_NEW));
        Utils.savePreference(ctx, Keys.IS_ATTACHMENTS, remoteConfig.getBoolean(Keys.IS_ATTACHMENTS));
        Utils.savePreference(ctx, Keys.IS_DISPLAY_MESSAGE_STATUS, remoteConfig.getBoolean(Keys.IS_DISPLAY_MESSAGE_STATUS));
        Utils.savePreference(ctx, Keys.IS_USER_TYPING_INDICATOR, remoteConfig.getBoolean(Keys.IS_USER_TYPING_INDICATOR));
        Utils.savePreference(ctx, Keys.IS_DISPLAY_AVATARS, remoteConfig.getBoolean(Keys.IS_DISPLAY_AVATARS));
        Utils.savePreference(ctx, Keys.IS_LOCATION_MESSAGE, remoteConfig.getBoolean(Keys.IS_LOCATION_MESSAGE));
        Utils.savePreference(ctx, Keys.REC_ALLOWED_CATEGORIES, remoteConfig.getString(Keys.REC_ALLOWED_CATEGORIES));

        Utils.savePreference(ctx, Keys.IS_COGNITION_ENABLED, remoteConfig.getBoolean(Keys.IS_COGNITION_ENABLED));
        Utils.savePreference(ctx, Keys.NEW_IN_APP_UPDATE_MINIMUM_ALLOWED_APP_VERSION, remoteConfig.getString(Keys.NEW_IN_APP_UPDATE_MINIMUM_ALLOWED_APP_VERSION));
        Utils.savePreference(ctx, Keys.IN_APP_UPDATE_SPECIFIC_APP_VERSIONS_TO_UPDATE, remoteConfig.getString(Keys.IN_APP_UPDATE_SPECIFIC_APP_VERSIONS_TO_UPDATE));
        Utils.savePreference(ctx, Keys.IN_APP_UPDATE_MINIMUM_ALLOWED_ANDROID_VERSION, remoteConfig.getString(Keys.IN_APP_UPDATE_MINIMUM_ALLOWED_ANDROID_VERSION));
        Utils.savePreference(ctx, Keys.IN_APP_UPDATE_SPECIFIC_ANDROID_VERSIONS_TO_UPDATE, remoteConfig.getString(Keys.IN_APP_UPDATE_SPECIFIC_ANDROID_VERSIONS_TO_UPDATE));
        Utils.savePreference(ctx, Keys.IN_APP_UPDATE_REQUEST_APP_UPDATE_ONCE_EVERY, remoteConfig.getString(Keys.IN_APP_UPDATE_REQUEST_APP_UPDATE_ONCE_EVERY));
        Utils.savePreference(ctx, Keys.IN_APP_UPDATE_CANCEL_UPDATE_SNACKBAR_TEXT_AR, remoteConfig.getString(Keys.IN_APP_UPDATE_CANCEL_UPDATE_SNACKBAR_TEXT_AR));
        Utils.savePreference(ctx, Keys.IN_APP_UPDATE_CANCEL_UPDATE_SNACKBAR_TEXT, remoteConfig.getString(Keys.IN_APP_UPDATE_CANCEL_UPDATE_SNACKBAR_TEXT));

        Utils.savePreference(ctx, Keys.IN_APP_UPDATE_SIDE_MENU_UPDATE_ITEM_TEXT, remoteConfig.getString(Keys.IN_APP_UPDATE_SIDE_MENU_UPDATE_ITEM_TEXT));
        Utils.savePreference(ctx, Keys.IN_APP_UPDATE_SIDE_MENU_UPDATE_ITEM_TEXT_AR, remoteConfig.getString(Keys.IN_APP_UPDATE_SIDE_MENU_UPDATE_ITEM_TEXT_AR));
        Utils.savePreference(ctx, Keys.IN_APP_UPDATE_MANDATORY_UPDATE_TOGGLE, remoteConfig.getBoolean(Keys.IN_APP_UPDATE_MANDATORY_UPDATE_TOGGLE));
        Utils.savePreference(ctx, Keys.IN_APP_UPDATE_IMMEDIATE_UPDATE_TOGGLE, remoteConfig.getBoolean(Keys.IN_APP_UPDATE_IMMEDIATE_UPDATE_TOGGLE));
        Utils.savePreference(ctx, Keys.IN_APP_UPDATE_FLEXIBLE_UPDATE_TOGGLE, remoteConfig.getBoolean(Keys.IN_APP_UPDATE_FLEXIBLE_UPDATE_TOGGLE));
        Utils.savePreference(ctx, Keys.IN_APP_UPDATE_RECOMMENDED_UPDATE_TOGGLE, remoteConfig.getBoolean(Keys.IN_APP_UPDATE_RECOMMENDED_UPDATE_TOGGLE));


        Utils.savePreference(ctx, Keys.DELIVERY_APPLY_ALL_CITIES, remoteConfig.getBoolean(Keys.DELIVERY_APPLY_ALL_CITIES));

        //ECOMMERCE
        Utils.savePreference(ctx, Keys.ECOMMERCE_TOGGLE, remoteConfig.getBoolean(Keys.ECOMMERCE_TOGGLE));

        //VAS
        Utils.savePreference(ctx, Keys.BEST_SELLER_PACK_CATEGORY, remoteConfig.getString(Keys.BEST_SELLER_PACK_CATEGORY));
        Utils.savePreference(ctx, Keys.BUMP_PACK_COLOR, remoteConfig.getString(Keys.BUMP_PACK_COLOR));
        Utils.savePreference(ctx, Keys.STAR_PACK_COLOR, remoteConfig.getString(Keys.STAR_PACK_COLOR));
        Utils.savePreference(ctx, Keys.STAR_BUMP_PACK_COLOR, remoteConfig.getString(Keys.STAR_BUMP_PACK_COLOR));
        Utils.savePreference(ctx, Keys.PREMIUM_PACK_COLOR, remoteConfig.getString(Keys.PREMIUM_PACK_COLOR));
        Utils.savePreference(ctx, Keys.INSERTION_PACK_COLOR, remoteConfig.getString(Keys.INSERTION_PACK_COLOR));
        //VAS FR
        Utils.savePreference(ctx, Keys.POPUP_BUMP_PITCH, remoteConfig.getString(Keys.POPUP_BUMP_PITCH));
        Utils.savePreference(ctx, Keys.POPUP_STAR_BUMP_PITCH, remoteConfig.getString(Keys.POPUP_STAR_BUMP_PITCH));
        Utils.savePreference(ctx, Keys.POPUP_STAR_PITCH, remoteConfig.getString(Keys.POPUP_STAR_PITCH));
        Utils.savePreference(ctx, Keys.POPUP_PREMIUM_PITCH, remoteConfig.getString(Keys.POPUP_PREMIUM_PITCH));
        Utils.savePreference(ctx, Keys.BUMP_PITCH, remoteConfig.getString(Keys.BUMP_PITCH));
        Utils.savePreference(ctx, Keys.STAR_PITCH, remoteConfig.getString(Keys.STAR_PITCH));
        Utils.savePreference(ctx, Keys.STAR_BUMP_PITCH, remoteConfig.getString(Keys.STAR_BUMP_PITCH));
        Utils.savePreference(ctx, Keys.PREMIUM_PITCH, remoteConfig.getString(Keys.PREMIUM_PITCH));
        Utils.savePreference(ctx, Keys.PREMIUM_BENEFITS, remoteConfig.getString(Keys.PREMIUM_BENEFITS));
        Utils.savePreference(ctx, Keys.STAR_BUMP_BENEFITS, remoteConfig.getString(Keys.STAR_BUMP_BENEFITS));
        Utils.savePreference(ctx, Keys.BUMP_BENEFITS, remoteConfig.getString(Keys.BUMP_BENEFITS));
        Utils.savePreference(ctx, Keys.STAR_BENEFITS, remoteConfig.getString(Keys.STAR_BENEFITS));
        Utils.savePreference(ctx, Keys.INSERTION_UI_PRICE, remoteConfig.getString(Keys.INSERTION_UI_PRICE));
        Utils.savePreference(ctx, Keys.INSERTION_TITLE, remoteConfig.getString(Keys.INSERTION_TITLE));
        Utils.savePreference(ctx, Keys.PREMIUM_TITLE, remoteConfig.getString(Keys.PREMIUM_TITLE));
        Utils.savePreference(ctx, Keys.STAR_TITLE, remoteConfig.getString(Keys.STAR_TITLE));
        Utils.savePreference(ctx, Keys.STAR_BUMP_TITLE, remoteConfig.getString(Keys.STAR_BUMP_TITLE));
        Utils.savePreference(ctx, Keys.BUMP_TITLE, remoteConfig.getString(Keys.BUMP_TITLE));
        Utils.savePreference(ctx, Keys.PAID_INSERTION_TITLE, remoteConfig.getString(Keys.PAID_INSERTION_TITLE));

        //VAS AR
        Utils.savePreference(ctx, Keys.POPUP_BUMP_PITCH_AR, remoteConfig.getString(Keys.POPUP_BUMP_PITCH_AR));
        Utils.savePreference(ctx, Keys.POPUP_STAR_BUMP_PITCH_AR, remoteConfig.getString(Keys.POPUP_STAR_BUMP_PITCH_AR));
        Utils.savePreference(ctx, Keys.POPUP_STAR_PITCH_AR, remoteConfig.getString(Keys.POPUP_STAR_PITCH_AR));
        Utils.savePreference(ctx, Keys.POPUP_PREMIUM_PITCH_AR, remoteConfig.getString(Keys.POPUP_PREMIUM_PITCH_AR));
        Utils.savePreference(ctx, Keys.BUMP_PITCH_AR, remoteConfig.getString(Keys.BUMP_PITCH_AR));
        Utils.savePreference(ctx, Keys.STAR_PITCH_AR, remoteConfig.getString(Keys.STAR_PITCH_AR));
        Utils.savePreference(ctx, Keys.STAR_BUMP_PITCH_AR, remoteConfig.getString(Keys.STAR_BUMP_PITCH_AR));
        Utils.savePreference(ctx, Keys.PREMIUM_PITCH_AR, remoteConfig.getString(Keys.PREMIUM_PITCH_AR));
        Utils.savePreference(ctx, Keys.PREMIUM_BENEFITS_AR, remoteConfig.getString(Keys.PREMIUM_BENEFITS_AR));
        Utils.savePreference(ctx, Keys.STAR_BUMP_BENEFITS_AR, remoteConfig.getString(Keys.STAR_BUMP_BENEFITS_AR));
        Utils.savePreference(ctx, Keys.BUMP_BENEFITS_AR, remoteConfig.getString(Keys.BUMP_BENEFITS_AR));
        Utils.savePreference(ctx, Keys.STAR_BENEFITS_AR, remoteConfig.getString(Keys.STAR_BENEFITS_AR));
        Utils.savePreference(ctx, Keys.INSERTION_UI_PRICE_AR, remoteConfig.getString(Keys.INSERTION_UI_PRICE_AR));
        Utils.savePreference(ctx, Keys.INSERTION_TITLE_AR, remoteConfig.getString(Keys.INSERTION_TITLE_AR));
        Utils.savePreference(ctx, Keys.PREMIUM_TITLE_AR, remoteConfig.getString(Keys.PREMIUM_TITLE_AR));
        Utils.savePreference(ctx, Keys.STAR_TITLE_AR, remoteConfig.getString(Keys.STAR_TITLE_AR));
        Utils.savePreference(ctx, Keys.STAR_BUMP_TITLE_AR, remoteConfig.getString(Keys.STAR_BUMP_TITLE_AR));
        Utils.savePreference(ctx, Keys.BUMP_TITLE_AR, remoteConfig.getString(Keys.BUMP_TITLE_AR));
        Utils.savePreference(ctx, Keys.PAID_INSERTION_TITLE_AR, remoteConfig.getString(Keys.PAID_INSERTION_TITLE_AR));

        Utils.savePreference(ctx, Keys.AI_MAX_PICTURES_COUNT, (int) remoteConfig.getLong(Keys.AI_MAX_PICTURES_COUNT));

        Utils.savePreference(ctx, Keys.INREAD_IS_ENABLED, remoteConfig.getBoolean(Keys.INREAD_IS_ENABLED));
        Utils.savePreference(ctx, Keys.INREAD_SHOW_VIDEO_DISMISS_CTA, remoteConfig.getBoolean(Keys.INREAD_SHOW_VIDEO_DISMISS_CTA));

        Utils.savePreference(ctx, DELIVERY_REMOTE_CONF_PRICE, (int) remoteConfig.getLong(DELIVERY_REMOTE_CONF_PRICE));
        Utils.savePreference(ctx, Keys.VIDEO_FIRST, remoteConfig.getBoolean(Keys.VIDEO_FIRST));
        Utils.savePreference(ctx, Keys.SUPPORT_TEAM_NUMBER, remoteConfig.getString(Keys.SUPPORT_TEAM_NUMBER));
    }
}