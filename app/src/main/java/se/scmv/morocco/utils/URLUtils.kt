package se.scmv.morocco.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.core.content.ContextCompat
import se.scmv.morocco.activities.WebViewActivity

object URLUtils {

        fun openUrlInWebView(context: Context, url: String, toolbarTitle: String = "") {
                val intent = Intent(context, WebViewActivity::class.java).apply {
                        putExtra(WebViewActivity.WEBVIEWURL, url)
                        putExtra(WebViewActivity.WEBVIEWTITLE, toolbarTitle)
                }
                ContextCompat.startActivity(
                        /* context = */ context,
                        /* intent = */ intent,
                        /* options = */ null
                )
        }

        fun modifyUrlParams(externalLink: String, newParams: Map<String, String>): String {
                val uri = Uri.parse(externalLink)
                val builder = uri.buildUpon().clearQuery()

                // Create a map of existing query parameters
                val existingParams = uri.queryParameterNames.associateWith { uri.getQueryParameter(it)!! }

                // Merge the existing parameters with the new parameters
                val mergedParams = existingParams.toMutableMap().apply {
                        putAll(newParams)
                }

                // Add merged parameters to the builder
                mergedParams.forEach { (key, value) ->
                        builder.appendQueryParameter(key, value)
                }

                return builder.build().toString()
        }

        fun openUrl(website : String, context : Context){
                var url = website
                if (!url.startsWith("http://") && !url.startsWith("https://")) {
                        url = "https://$url"
                }
                val browserIntent = Intent(
                        Intent.ACTION_VIEW,
                        Uri.parse(url)
                )
                browserIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(
                        browserIntent
                )
        }
}