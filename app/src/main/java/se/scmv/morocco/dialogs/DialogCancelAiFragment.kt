package se.scmv.morocco.dialogs

import android.app.AlertDialog
import android.app.Dialog
import android.content.Context
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.LayoutInflater
import android.widget.ImageView
import androidx.appcompat.app.AppCompatDialogFragment
import se.scmv.morocco.R

class DialogCancelAiFragment(
        private var ctx: Context,
        private var icon: Drawable?,
        private var title: Int,
        private var message: Int,
        private var onDialogActionListener: OnDialogActionListener
) : AppCompatDialogFragment() {

        override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
                val customDialog =
                        LayoutInflater.from(ctx).inflate(R.layout.dialog_cancel_draft_ai, null)
                val titleTextView =
                        customDialog.findViewById<androidx.appcompat.widget.AppCompatTextView>(R.id.dialog_title)
                val messageTextView =
                        customDialog.findViewById<androidx.appcompat.widget.AppCompatTextView>(R.id.dialog_message)
                val iconImageView = customDialog.findViewById<ImageView>(R.id.dialog_icon)
                val yesActionBtn =
                        customDialog.findViewById<androidx.appcompat.widget.AppCompatTextView>(R.id.button_yes)
                val noActionBtn =
                        customDialog.findViewById<androidx.appcompat.widget.AppCompatTextView>(R.id.button_no)
                titleTextView.setText(title)
                messageTextView.setText(message)
                iconImageView.setImageDrawable(icon)
                yesActionBtn.setOnClickListener { onDialogActionListener.onPositiveAction() }
                noActionBtn.setOnClickListener { onDialogActionListener.onNegativeAction() }

                val dialog = AlertDialog.Builder(ctx)
                dialog.setView(customDialog)
                return dialog.create()
        }

        interface OnDialogActionListener {
                fun onPositiveAction()
                fun onNegativeAction()
        }
}