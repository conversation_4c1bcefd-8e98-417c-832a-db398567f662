package se.scmv.morocco.dialogs

import android.app.Dialog
import android.os.Bundle
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatDialogFragment
import ma.avito.orion.ui.input.edittext.OrionEditText
import se.scmv.morocco.R

/**
 * Created by <PERSON><PERSON> on 6/15/16.
 */
class PasswordForgottenDialog : AppCompatDialogFragment() {

    var dialogActionListener: OnDialogActionListener? = null

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val inflater = requireActivity().layoutInflater
        val dialogContent = inflater.inflate(R.layout.dialog_password_forgotten, null)
        val emailField = dialogContent.findViewById<OrionEditText>(R.id.email_field)
        val adb = AlertDialog.Builder(requireContext())
        adb.setView(dialogContent)
        adb.setPositiveButton(getString(R.string.action_send)) { _, _ ->
            dialogActionListener?.onValidateAction(emailField.getValue().editTextValue)
        }

        return adb.create()
    }


    interface OnDialogActionListener {
        fun onValidateAction(email: String)
    }
}
