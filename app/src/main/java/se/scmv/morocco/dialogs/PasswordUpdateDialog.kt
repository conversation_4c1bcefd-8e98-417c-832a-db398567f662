package se.scmv.morocco.dialogs

import android.app.Dialog
import android.content.DialogInterface
import android.os.Bundle
import android.view.View
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatDialogFragment
import ma.avito.orion.ui.input.edittext.OrionEditText
import se.scmv.morocco.R
import se.scmv.morocco.utils.Utils

/**
 * Created by <PERSON><PERSON> on 6/14/16.
 */
class PasswordUpdateDialog : AppCompatDialogFragment() {

    var dialogActionListener: OnDialogActionListener? = null

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val inflater = requireActivity().layoutInflater
        val dialogContent = inflater.inflate(R.layout.dialog_password_update, null)
        val oldPasswd = dialogContent.findViewById<OrionEditText>(R.id.old_password_field)
        val newPasswd = dialogContent.findViewById<OrionEditText>(R.id.new_password_field)
        val tvNewPasswordError = dialogContent.findViewById<TextView>(R.id.tvNewPasswordError)
        val adb = AlertDialog.Builder(requireContext())
        adb.setView(dialogContent)
        adb.setPositiveButton(getString(R.string.action_validate), null)
        val dialog: Dialog = adb.create()
        val button = (dialog as AlertDialog).getButton(AlertDialog.BUTTON_POSITIVE)
        val standardFont = Utils.getFont(context, R.font.rubik_medium)
        if (button != null) {
            button.typeface = standardFont
        }
        dialog.setOnShowListener { d: DialogInterface ->
            (d as AlertDialog).getButton(AlertDialog.BUTTON_POSITIVE).setOnClickListener {
                hideFieldError(tvNewPasswordError)
                var isValidationPassed = oldPasswd.validate() && newPasswd.validate()
                val oldPass = oldPasswd.getValue().editTextValue
                val newPass = newPasswd.getValue().editTextValue

                if (newPass.isEmpty()) {
                    isValidationPassed = false
                    showFieldError(tvNewPasswordError, getString(R.string.err_enter_new_pass))
                }
                if (oldPass.isNotBlank() && newPass.isNotBlank() && oldPass == newPass) {
                    isValidationPassed = false
                    showFieldError(tvNewPasswordError, getString(R.string.err_old_new_same))
                }
                if (isValidationPassed) {
                    hideFieldError(tvNewPasswordError)
                    dialogActionListener?.onValidateAction(oldPasswd = oldPass, newPasswd = newPass)
                    //Dismiss once everything is OK.
                    dismiss()
                }
            }
        }

        return dialog
    }

    private fun showFieldError(errorFieldView: TextView, message: String?) {
        errorFieldView.visibility = View.VISIBLE
        errorFieldView.text = message
    }

    private fun hideFieldError(errorFieldView: TextView) {
        errorFieldView.visibility = View.GONE
    }

    interface OnDialogActionListener {
        fun onValidateAction(oldPasswd: String, newPasswd: String)
    }
}
