package se.scmv.morocco.dialogs

import android.app.Dialog
import android.os.Bundle
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatDialogFragment
import androidx.fragment.app.FragmentManager
import com.bumptech.glide.Glide
import com.google.firebase.crashlytics.FirebaseCrashlytics
import se.scmv.morocco.R
import se.scmv.morocco.utils.Utils

/**
 * Created by <PERSON><PERSON> on 4/22/16.
 */
class ConfirmDialogFragment : AppCompatDialogFragment() {

    var dialogActionListener: OnDialogActionListener? = null

    private var positiveActionLabel: String? = null
    private var negativeActionLabel: String? = null
    private var message: CharSequence? = null
    private var iconUrl: String? = null
    private var mIcon = -1

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val inflater = requireActivity().layoutInflater
        val dialogContent = inflater.inflate(R.layout.dialog_ai_confirmation, null)
        if (mIcon != -1) {
            (dialogContent.findViewById<View>(R.id.dialog_icon) as ImageView).setImageResource(mIcon)
        } else if (iconUrl != null) {
            Glide.with(dialogContent.context).load(iconUrl)
                .into((dialogContent.findViewById<View>(R.id.dialog_icon) as ImageView))
        }
        (dialogContent.findViewById<View>(R.id.dialog_message) as TextView).text = message
        val adb = AlertDialog.Builder(requireContext())
        adb.setView(dialogContent)

        if (positiveActionLabel != null) {
            adb.setPositiveButton(positiveActionLabel) { _, _ ->
                dialogActionListener?.onPositiveAction()
            }
        }

        if (negativeActionLabel != null) {
            adb.setNegativeButton(negativeActionLabel) { _, _ ->
                dialogActionListener?.onNegativeAction()
            }
        }
        adb.setCancelable(true)

        val dialog: Dialog = adb.create()
        var button = (dialog as AlertDialog).getButton(AlertDialog.BUTTON_POSITIVE)
        try {
            val standardFont = Utils.getFont(context, R.font.rubik_medium)
            if (button != null) {
                button.typeface = standardFont
            }

            button = dialog.getButton(AlertDialog.BUTTON_NEGATIVE)
            if (button != null) {
                button.typeface = standardFont
            }

            button = dialog.getButton(AlertDialog.BUTTON_NEUTRAL)
            if (button != null) {
                button.typeface = standardFont
            }
        } catch (ignored: Exception) {
            //Huawei doesnt'support this approach sometimes
        }

        return dialog
    }

    override fun show(manager: FragmentManager, tag: String?) {
        try {
            val ft = manager.beginTransaction()
            ft.add(this, tag)
            ft.commitAllowingStateLoss()
        } catch (ex: IllegalStateException) {
            FirebaseCrashlytics.getInstance().recordException(ex)
        }
    }

    fun setIcon(mIcon: Int) {
        this.mIcon = mIcon
    }

    fun setIconUrl(url: String?) {
        iconUrl = url
    }

    fun setPositiveAction(label: String?) {
        positiveActionLabel = label
    }

    fun setNegativeAction(label: String?) {
        negativeActionLabel = label
    }

    fun setMessage(message: CharSequence?) {
        this.message = message
    }

    interface OnDialogActionListener {
        fun onPositiveAction()

        fun onNegativeAction()
    }
}
