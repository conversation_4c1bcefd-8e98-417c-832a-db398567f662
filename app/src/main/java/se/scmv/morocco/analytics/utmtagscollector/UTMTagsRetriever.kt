package se.scmv.morocco.analytics.utmtagscollector

import android.net.Uri
import se.scmv.morocco.analytics.AnalyticsStrings

object UTMTagsRetriever {
        private val SUPPORTED_UTMS = arrayOf(
                "utm_id", "utm_source",
                "utm_medium", "utm_term", "utm_content", "utm_campaign"
        )
        var utmProperties = HashMap<String, String>()

        @JvmStatic
        fun retrieveUTMTags(uri: Uri?) {
                utmProperties.clear()
                uri?.let {
                        utmProperties[AnalyticsStrings.SOURCE] = "deeplink"
                        for (key in SUPPORTED_UTMS) {
                                val value = uri.getQueryParameter(key)
                                if (value != null && value != "") utmProperties[key] = value
                        }
                }
        }
}