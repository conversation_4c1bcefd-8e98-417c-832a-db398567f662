package se.scmv.morocco.analytics

import android.content.Context
import android.os.Bundle
import androidx.core.os.bundleOf
import com.braze.Braze
import com.braze.models.outgoing.BrazeProperties
import com.facebook.appevents.AppEventsLogger
import com.google.firebase.analytics.FirebaseAnalytics
import se.scmv.morocco.R
import se.scmv.morocco.analytics.AnalyticsUtils.generateMapFromAccountObject
import se.scmv.morocco.analytics.translate.TranslationAnalyticsUtils.findCategoryTranslation
import se.scmv.morocco.analytics.translate.TranslationAnalyticsUtils.findCityTranslation
import se.scmv.morocco.analytics.utmtagscollector.UTMTagsRetriever.utmProperties
import se.scmv.morocco.avitov2.filters.domain.models.GetListingQueryParams
import se.scmv.morocco.common.lang.LocaleManager.getCurrentLanguage
import se.scmv.morocco.common.lang.LocaleManager.isAr
import se.scmv.morocco.dao.CategoryRecord
import se.scmv.morocco.dao.CityRecord
import se.scmv.morocco.dao.DaoManager
import se.scmv.morocco.login.models.Account
import se.scmv.morocco.login.models.AccountToken
import se.scmv.morocco.type.AdParamListTextFilter
import se.scmv.morocco.utils.FlavorUtils.Companion.isShopFlavor
import se.scmv.morocco.utils.Utils
import java.util.Locale
import java.util.Objects
import java.util.stream.Collectors


class AnalyticsManager private constructor(private val mContext: Context) {
    private val braze: Braze
    private val facebookLogger: AppEventsLogger
    private val mFirebaseAnalytics: FirebaseAnalytics

    init {
        braze = Braze.getInstance(mContext)
        facebookLogger = AppEventsLogger.newLogger(mContext)
        mFirebaseAnalytics = FirebaseAnalytics.getInstance(mContext)
    }

    /**
     * This method fire the given event to analytics platform amplitude for both avito and tayara
     * -Appboy for avito
     *
     * @param event           event
     * @param eventProperties list of properties
     * @param logFirebase
     */
    //todo: (in firebase tagging plan tasks) this methode and logFirebase do the same thing.
    fun logEvent(
        event: String,
        eventProperties: Map<String?, String?>,
        logFirebase: Boolean
    ) {
        val updatedProperties = eventProperties.toMutableMap()
        updatedProperties[USER_LANG] = getCurrentLanguage()
        if (isShopFlavor()) {
            updatedProperties[IS_STORE_ACCOUNT] = "true"
        }
        checkLocalisedProperties(updatedProperties)
        if (logFirebase) {
            logFirebaseEvent(event, updatedProperties.toMap()) // Convert back to immutable map
        }
    }


    fun logBrazeEvent(event: String?, eventProperties: Map<String?, String?>?) {
        val appBoyProperties = getAppBoyPropertyFromMap(eventProperties)
        braze.logCustomEvent(event, appBoyProperties)
    }

    fun logFirebase(event: String, eventProperties: MutableMap<String?, String?>) {
        eventProperties[USER_LANG] = getCurrentLanguage()
        eventProperties.putAll(utmProperties)
        checkLocalisedProperties(eventProperties)
        logFirebaseEvent(event, eventProperties)
    }

    fun checkLocalisedProperties(eventProperties: MutableMap<String?, String?>) {
        if (isAr()) {
            if (eventProperties.containsKey("category_name")) {
                eventProperties["category_name"] = findCategoryTranslation(
                    eventProperties["category_name"]
                )
            }
            if (eventProperties.containsKey("MainCategory")) {
                eventProperties["MainCategory"] =
                    findCategoryTranslation(eventProperties["MainCategory"])
            }
            if (eventProperties.containsKey("subcategory_name")) {
                eventProperties["subcategory_name"] =
                    findCategoryTranslation(eventProperties["subcategory_name"])
            }
            if (eventProperties.containsKey("SubCategory")) {
                eventProperties["SubCategory"] =
                    findCategoryTranslation(eventProperties["SubCategory"])
            }
            if (eventProperties.containsKey("city")) {
                eventProperties["city"] =
                    findCityTranslation(eventProperties["city"])
            }
            if (eventProperties.containsKey("City")) {
                eventProperties["City"] =
                    findCityTranslation(eventProperties["City"])
            }
        }
    }

    fun logFirebaseEvent(event: String, eventProperties: Map<String?, String?>) {
        mFirebaseAnalytics.logEvent(event.replace(" ", "_"), getBundleFrom(eventProperties))
    }

    fun logFacebookEvent(event: String?, eventProperties: Map<String?, String?>) {
        facebookLogger.logEvent(event, getBundleFrom(eventProperties))
    }

    private fun getBundleFrom(properties: Map<String?, String?>): Bundle {
        val bundleProperties = Bundle()
        for (key in properties.keys) {
            if (properties[key] != null && properties[key]!!.isNotEmpty()) {
                bundleProperties.putString(key?.trim { it <= ' ' }
                    ?.replace(" ", "_")?.replace("/", "_")?.replace("-", "_")?.replace("'", "_")
                    ?.lowercase(
                        Locale.getDefault()
                    ), properties[key]!!
                    .lowercase(Locale.getDefault()))
            }
        }
        return bundleProperties
    }

    fun logVasEvent(context: Context, event: String, eventProperties: Bundle) {
        FirebaseAnalytics.getInstance(context).logEvent(event, eventProperties)
    }


    /**
     * This function is dedicated to track VAS events without properties
     *
     * @param event
     */
    fun logVasEvent(context: Context, event: String) {
        FirebaseAnalytics.getInstance(context).logEvent(event, bundleOf())
    }

    /**
     * This function is for pushing a screen using the AnalyticsManager instance
     */
    fun pushScreen(context: Context, screenName: String) {
        FirebaseAnalytics.getInstance(context).logEvent(
            context.getString(R.string.gtm_open_screen),
            bundleOf(context.getString(R.string.gtm_screen_label) to screenName)
        )
    }

    /**
     * Create an Appboyproperties object from a given Map properties
     *
     * @param eventProperties
     * @return Appboyproperties to be sent instance an event
     */
    private fun getAppBoyPropertyFromMap(eventProperties: Map<String?, String?>?): BrazeProperties {
        val appBoyProperties = BrazeProperties()
        eventProperties?.forEach { (key, value) ->
            value?.let { key?.let { it1 -> appBoyProperties.addProperty(it1, it) } }
        }
        return appBoyProperties
    }

    fun setUserId(userProperties: Map<String?, String?>?) {
        if (!Utils.getBooleanPreference(mContext, PREF_IS_IDENTIFIED, false)) {
            if (userProperties != null) {
                if (userProperties[EMAIL_ATTRIBUTE] != null) {
                    braze.changeUser(
                        userProperties[EMAIL_ATTRIBUTE]!!.lowercase(Locale.getDefault())
                    )
                }
                val currentUser = braze.currentUser
                if (currentUser != null) {
                    currentUser.setFirstName(userProperties[NAME_ATTRIBUTE])
                    currentUser.setPhoneNumber(userProperties[PHONE_ATTRIBUTE])
                    currentUser.setCustomUserAttribute(USER_LANG, getCurrentLanguage())
                }
                Utils.savePreference(mContext, PREF_IS_IDENTIFIED, true)
            }
        }
    }

    fun unSetUserId() {
        Utils.savePreference(mContext, PREF_IS_IDENTIFIED, false)
    }


    fun createListingPropertiesEvents(filters: GetListingQueryParams?, includeSellerTypeValue: Boolean): HashMap<String?, String?> {
        val propertiesEvents = HashMap<String?, String?>()
        if (filters == null) return propertiesEvents
        val mainCategoryLabel = "category_name"
        val subCategoryLabel = "subcategory_name"
        val categoryId = filters?.categoryId ?: 0
        injectCategorySubcategoryName(
            categoryId,
            propertiesEvents,
            mainCategoryLabel,
            subCategoryLabel
        )
        if (filters != null && filters.location != null) {
            val cityIdsOptional = filters.location.cityIds
            val cityIds = cityIdsOptional.getOrNull()
            val areaIdsOptional = filters.location.areaIds
            val areaIds = areaIdsOptional.getOrNull()
            if (cityIds != null && !cityIds.isEmpty()) {
                val firstCityId = cityIds[0]
                injectCity(propertiesEvents, firstCityId)
                propertiesEvents["city_id"] = firstCityId?.toString() ?: ""
                propertiesEvents["nbr_cities"] = cityIds.size.toString()
            }
            if (areaIds != null && !areaIds.isEmpty() && cityIds != null && !cityIds.isEmpty()) {
                val firstAreaId = areaIds[0]
                val firstCityId = cityIds[0]
                AnalyticsUtils.injectCityAreaName(
                    firstAreaId.toString(),
                    propertiesEvents,
                    firstCityId,
                    "city",
                    "area",
                    "area_id"
                )
                propertiesEvents["nbr_areas"] = areaIds.size.toString()
            }
        }
        if (AccountToken.isLoggedIn(mContext)) {
            Account.getCurrentAccount(mContext)?.let { currentAccount ->
                propertiesEvents["seller_type"] =
                    if (currentAccount.accountType == 1) "pro" else "private"
            }
        }
        if (includeSellerTypeValue) {
            propertiesEvents["value"] = when(filters.isStore) {
                true -> "pro"
                false ->"private"
                else -> "all"
            }
        }
        if (filters != null && filters.type != null) {
            propertiesEvents["ad_type"] = filters.type.rawValue
        }
        propertiesEvents["keyword"] =
            if (filters != null && filters.text != null) filters.text.toString() else ""
        propertiesEvents["element_name"] = "element_clicked"
        propertiesEvents["page_name"] = "listing"
        var textList: List<AdParamListTextFilter?>?
        if (filters != null && filters.params != null && filters.params.listMatch != null && filters.params.listMatch.getOrNull() != null && filters.params.listMatch.getOrNull()!!.textList != null) {
            textList = filters.params.listMatch.getOrNull()!!.textList.getOrNull()
            if (textList != null) {
                textList =
                    textList.stream().filter { obj: AdParamListTextFilter? -> Objects.nonNull(obj) }
                        .collect(Collectors.toList())

                // Extract brand information
                textList.stream()
                    .filter { it: AdParamListTextFilter? -> it!!.name == "brand" || it.name == "phone_brand" }
                    .findFirst()
                    .ifPresent { brandItem: AdParamListTextFilter? ->
                        val brandValue = brandItem!!.value.stream().findFirst().orElse(null)
                        if (brandValue != null) {
                            propertiesEvents["brand"] = brandValue
                        }
                        val nbrBrands = brandItem.value.size
                        if (nbrBrands != null) {
                            propertiesEvents["nbr_brands"] = nbrBrands.toString()
                        }
                    }

                // Extract model information
                textList.stream()
                    .filter { it: AdParamListTextFilter? -> it!!.name == "model" || it.name == "phone_model" }
                    .findFirst()
                    .ifPresent { modelItem: AdParamListTextFilter? ->
                        val modelValue = modelItem!!.value.stream().findFirst().orElse(null)
                        if (modelValue != null) {
                            propertiesEvents["model"] = modelValue
                        }
                        val nbrModels = modelItem.value.size
                        if (nbrModels != null) {
                            propertiesEvents["nbr_models"] = nbrModels.toString()
                        }
                    }
            }
        }
        return propertiesEvents
    }


    fun logUserProfileRelatedEvent(event: String?, properties: Map<String?, String?>?) {
        val eventProperties = HashMap<String?, String?>()
        if (AccountToken.isLoggedIn(mContext)) {
            val currentAccount = Account.getCurrentAccount(mContext)
            eventProperties.putAll(generateMapFromAccountObject(currentAccount))
        }
        eventProperties.putAll(properties!!)
        logEvent(event!!, eventProperties, true)
    }

    fun sendListedSearchAnalytics(adPropertiesForFirebase: Map<String?, String?>) {
        logFirebaseEvent("search_result", adPropertiesForFirebase)
        logBrazeEvent("search_result", adPropertiesForFirebase)
        logFacebookEvent("search_result", adPropertiesForFirebase)
    }

    companion object {
        const val USER_LANG = "Lang"
        const val IS_STORE_ACCOUNT = "IsStoreAccount"
        const val NAME_ATTRIBUTE = "Name"
        const val EMAIL_ATTRIBUTE = "Email"
        const val PHONE_ATTRIBUTE = "Phone"
        private const val PREF_IS_IDENTIFIED = "is_identified"
        var instance: AnalyticsManager? = null
            private set

        fun initialize(context: Context): AnalyticsManager? {
            if (instance == null) instance = AnalyticsManager(context)
            return instance
        }

        fun injectCity(map: HashMap<String?, String?>, regionId: Int?) {
            if (regionId!! > 0) {
                val adRegionRecord = CityRecord.getCityByRegionId(regionId)
                map["City"] = if (adRegionRecord != null) adRegionRecord.analyticsName else ""
            }
        }

        fun injectCategorySubcategoryName(
            categoryId: Int?,
            map: HashMap<String?, String?>,
            mainCategoryLabel: String,
            subCategoryLabel: String
        ) {
            if (categoryId == null) {
                return
            }
            val recordRealmResults = DaoManager.getInstance().getById(
                CategoryRecord::class.java, "categoryId", categoryId.toLong()
            )
            if (recordRealmResults != null) {
                if (recordRealmResults.size > 0) {
                    val category = recordRealmResults.first()
                    val subCategory = category!!.analyticsName
                    var mainCategory: String?
                    if (category.level > 0) {
                        mainCategory = subCategory
                        val categoryRecordRealmResults = DaoManager
                            .getInstance()
                            .getById(
                                CategoryRecord::class.java,
                                "categoryId",
                                category.parent.toLong()
                            )
                        map[mainCategoryLabel] = mainCategory
                        map["category_id"] = category.parent.toString()
                        map["subcategory_id"] = categoryId.toString()
                        if (categoryRecordRealmResults != null) {
                            if (categoryRecordRealmResults.size > 0) {
                                mainCategory = categoryRecordRealmResults
                                    .first()?.analyticsName
                            }
                        }
                    } else {
                        mainCategory = subCategory
                        map["category_id"] = categoryId.toString()
                        map["subcategory_id"] = categoryId.toString()
                    }
                    map[mainCategoryLabel] = mainCategory
                    map[subCategoryLabel] = subCategory
                }
            }
        }

    }
}