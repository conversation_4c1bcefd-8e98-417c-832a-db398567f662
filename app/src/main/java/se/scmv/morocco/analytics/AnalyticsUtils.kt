package se.scmv.morocco.analytics

import se.scmv.morocco.dao.CityRecord
import se.scmv.morocco.login.models.Account

/**
 * Created by b<PERSON><PERSON> on 5/9/17.
 */
object AnalyticsUtils {
    /**
     * track "Click on VAS CTA" event
     * event : Click on VAS CTA
     * source : listing / Favorite / Adview / Deep link
     * mode :  Grid / List
     *
     * @param requestVasType
     * @param ad
     * @param mode
     */


    fun generateMapFromAccountObject(currentAccount: Account?): HashMap<String, String?> {
        val userProperties = HashMap<String, String?>()
        if (currentAccount != null) {
            currentAccount.name?.let { userProperties[AnalyticsStrings.NAME] = it }
            userProperties[AnalyticsStrings.EMAIL] = currentAccount.email
            userProperties[AnalyticsStrings.PHONE] = currentAccount.phone
        }
        return userProperties
    }

    fun injectCityAreaName(
        areaId: String,
        map: HashMap<String?, String?>,
        regionId: Int?,
        cityPropertyLabel: String?,
        areaPropertyLabel: String?,
        areaIdPropertyLabel: String?
    ) {
        if (regionId != null && regionId > 0) {
            val adRegionRecord = CityRecord.getCityByRegionId(regionId)
            if (cityPropertyLabel != null)
                map[cityPropertyLabel] =
                    if (adRegionRecord != null) adRegionRecord.analyticsName else ""
            if (adRegionRecord != null) {
                for (area in adRegionRecord.towns) {
                    if (area.townId.toString() == areaId && areaPropertyLabel != null) map[areaPropertyLabel] =
                        area.analyticsName
                    if (areaIdPropertyLabel != null)
                        map[areaIdPropertyLabel] = area.townId.toString()
                }
            }
        }
    }
}
