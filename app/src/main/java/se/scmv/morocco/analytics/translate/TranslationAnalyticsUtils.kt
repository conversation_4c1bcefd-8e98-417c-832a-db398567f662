package se.scmv.morocco.analytics.translate

import se.scmv.morocco.Avito
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.dao.CategoryRecord
import se.scmv.morocco.dao.CityRecord
import se.scmv.morocco.dao.DaoManager
import se.scmv.morocco.utils.Utils


object TranslationAnalyticsUtils {

        //region Retrieval
        fun findCategoryTranslation(keyword: String?): String? {
                val isCategoryPersisted =
                        Utils.getBooleanPreference(Avito.context, Utils.CATEGORIES_AD_TYPES_LOADED)
                if (isCategoryPersisted && LocaleManager.isAr()) {
                        val category =
                                DaoManager.getInstance().listAll(CategoryRecord::class.java).where()
                                        .equalTo("label.arLabel", keyword)
                                        .findFirst()
                        return if (category != null) category.analyticsName else keyword
                }
                return keyword
        }

        fun findCityTranslation(keyword: String?): String? {
                val isCityPersisted =
                        Utils.getBooleanPreference(Avito.context, Utils.REGIONS_SECTORS_LOADED)
                if (isCityPersisted && LocaleManager.isAr()) {
                        val city = DaoManager.getInstance().listAll(CityRecord::class.java).where()
                                .equalTo("label.arLabel", keyword)
                                .findFirst()
                        return if (city != null) city.analyticsName else keyword
                }
                return keyword
        }
        //endregion

}
