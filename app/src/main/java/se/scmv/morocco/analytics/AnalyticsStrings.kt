package se.scmv.morocco.analytics

object AnalyticsStrings {
        const val EVENT_LOGGED_IN = "logged_in"
        const val LOGGED_TYPE = "login_type"

        //Properties
        const val SUBCATEGORY = "SubCategory"
        const val MAINCATEGORY = "MainCategory"
        const val SUBCATEGORYID = "SubCategory ID"
        const val MAINCATEGORYID = "MainCategory ID"
        const val NAME = "Name"
        const val EMAIL = "Email"
        const val PHONE = "Phone"
        const val SOURCE = "source"
        const val TRIGGER_AD_INSERT = "Trigger Ad Insert"
        const val GO_TO_MESSAGING_TAB = "Go to Messaging Tab"
        const val CURRENCY = "Dhs"
        const val TYPE_OF_AD = "Type of ad"
        const val CITY = "City"
        const val PRICE = "Price"
        const val HAS_PRICE = "HasPrice"
        const val AD_TYPE = "ad_type"
        const val ACCOUNT_TYPE = "account_type"
        const val ACCOUNT_TYPE_SHOP = "pro"
        const val ACCOUNT_TYPE_PRIVATE = "private"
        const val ADS_COUNT = "ads_count"
        const val LANG = "lang"


        /**
         * -------
         * IMAGE UPLOAD PROPERTIES
         * -------
         */
        const val IMAGE_ID = "Image ID"
        const val PICTURES_COUNT = "Pictures count"
        const val CONNECTIVITY_TYPE = "Connectivity"
        const val INIT_IMAGE_SIZE = "Init image size"
        const val IMAGE_UPLOAD_ERROR_MESSAGE = "Image upload error"
        const val COMPRESSED_IMAGE_SIZE = "Compressed image size"


        /**
         * -------
         * IMAGE UPLOAD UI EVENTS
         * -------
         */
        const val ON_ADD_PICTURE = "Trigger image upload"
        const val ON_GALLERY_TRIGGERED = "Trigger gallery chooser"
        const val ON_CAMERA_TRIGGERED = "Trigger camera"
        const val ON_SELECTED_PICTURES_SUBMITTED = "On selected pictures submitted"
        const val ON_IMAGE_DELETED = "Image deleted"
        const val ON_IMAGE_CLICKED = "Image clicked"
        const val ON_MAIN_IMAGE_CHECKED = "Main image checked/unchecked"
        const val ON_IMAGE_CLICK_RETRY = "Retry image upload"


        /**
         * -------
         * IMAGES UPLOAD SERVICE EVENTS
         * -------
         */
        const val ON_IMAGE_COMPRESSION_FAILED = "Compression failed"
        const val ON_IMAGE_COMPRESSION_FAILED_ERROR = "Compression failed with error"
        const val ON_ERROR_IMAGE_UPLOAD = "Image upload failed"

        /**
         * -------
         * IMAGES CLASSIFICATION SERVICE EVENTS
         * -------
         */
        const val ON_CATEGORY_SHOWN = "Ad categorisation shown to user"

        //Events

        //Events
        const val REBIRTH = "RebirthApp"
        const val RESURECT = "ResurectedApp"
        const val D2D_CTA = "ClickedOnD2DCta"
        const val NEW_FAB = "ClickedOnNewFAB"

        //Insertion fees
        const val SHOP_CTA = "Clicked on SHOP CTA"
        const val INSERT_CTA_FEES = "Clicked on IF CTA"
        const val LAP_LEVEL_ONE = "Viewed IF Screen LAP 1"
        const val LAP_MAX_LEVEL = "Viewed IF Screen LAP 2"
        const val CANCEL_PAYMENT = "Clicked on Cancel payment"
        const val PAY_IF = "Clicked on continue payment"


        /**
         * -------
         * DRAFT AD INSERT PROPERTIES
         * -------
         */
        const val RESPONSE = "Response"
        const val FROM_SIDE_MENU = "Side menu"
        const val FROM_STEP_1 = "Step 1"
        const val YES = "Clicked YES"
        const val NO = "Clicked NO"
        const val HOME_BTN = "Home Button"
        const val RETURN_BTN = "Return Button"
        const val IS_DRAFT = "isDraft"

        /**
         * -------
         * Firebase TAGGING PLAN EVENT PROPERTIES NAMES
         * -------
         */
        const val FRB_RESULT_COUNT = "result_count"

        const val FRB_CONTENT_TYPE = "content_type"
        const val FRB_EVENT_NAME = "search_result"

        /**
         * -------
         * Firebase TAGGING PLAN EVENT NAMES
         * -------
         */
        const val EVENT_CHOSED_PAYMENT_METHOD = "CHOSED PAYMENT METHOD"


}