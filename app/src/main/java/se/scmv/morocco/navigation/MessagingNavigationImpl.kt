//package se.scmv.morocco.navigation
//
//import android.content.Context
//import android.content.Intent
//import se.scmv.morocco.domain.navigation.MessagingNavigation
//import se.scmv.morocco.activities.MainActivity
//
//class MessagingNavigationImpl(private val context: Context) : MessagingNavigation {
//    override fun openConversation(conversationId: String) {
//        // Launch the main activity with the chat route
//        val intent = Intent(context, MainActivity::class.java).apply {
//            putExtra("navigate_to_chat", true)
//            putExtra("conversation_id", conversationId)
//            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
//        }
//        context.startActivity(intent)
//    }
//}