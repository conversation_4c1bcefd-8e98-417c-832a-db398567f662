package se.scmv.morocco.navigation

import android.content.Context
import se.scmv.morocco.domain.models.AdDetails
import se.scmv.morocco.domain.navigation.EcommerceNavigator
import se.scmv.morocco.utils.BuyEcommerceProductUtils
import javax.inject.Inject

class EcommerceNavigatorImpl @Inject constructor(
) : EcommerceNavigator {

    override fun buyEcommerceProduct(
        context: Context,
        domainAdDetail: AdDetails.Details,
        adUuid: String
    ) {
        BuyEcommerceProductUtils.buyEcommerceProduct(
            context = context,
            ad = domainAdDetail,
            adUuid = adUuid
        )

    }


}
