package se.scmv.morocco.navigation

import android.content.Context
import android.content.Intent
import se.scmv.morocco.activities.MainActivity
import se.scmv.morocco.domain.navigation.AppNavigation
import se.scmv.morocco.utils.Keys

class AppNavigatorImpl(private val context: Context) : AppNavigation {
    override fun navigateToAdView(listingId: String) {
        val intent = Intent(context, MainActivity::class.java).apply {
            putExtra(Keys.AD_ID, listingId)
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        }
        context.startActivity(intent)
    }
}
