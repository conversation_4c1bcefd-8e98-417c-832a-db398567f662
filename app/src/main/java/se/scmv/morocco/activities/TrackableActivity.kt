package se.scmv.morocco.activities

import se.scmv.morocco.analytics.AnalyticsManager

abstract class TrackableActivity : BaseActivity() {

        abstract fun getAnalyticsProperties(): Map<String, String>

        abstract fun getScreenViewAnalyticalName(): String

        open fun logFragment() {
                AnalyticsManager.instance
                        ?.logFirebase(getScreenViewAnalyticalName(), getAnalyticsProperties().toMutableMap())
        }

        override fun onResume() {
                super.onResume()
                logFragment()
        }
}