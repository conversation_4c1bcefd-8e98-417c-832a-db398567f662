package se.scmv.morocco.activities;

import static se.scmv.morocco.utils.Utils.rebirthApp;

import android.app.ProgressDialog;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.fragment.app.FragmentManager;

import com.braze.ui.inappmessage.BrazeInAppMessageManager;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

import org.greenrobot.eventbus.Subscribe;

import java.lang.ref.WeakReference;

import se.scmv.morocco.GetMyStoreInfoQuery;
import se.scmv.morocco.R;
import se.scmv.morocco.events.BusEvent;
import se.scmv.morocco.network.NetworkManager;
import se.scmv.morocco.utils.EventBusManager;
import se.scmv.morocco.utils.MemoryUtils;
import se.scmv.morocco.utils.StoreUtils;

public abstract class BaseActivity extends AppCompatActivity implements NetworkManager.NetworkStatusListener {

    protected String TAG;
    protected FragmentManager fragmentmanager;
    protected NetworkManager mNetworkManager;
    protected Toolbar toolbar;
    protected View rootView;
    private ProgressDialog dialog;

    @Override
    protected void onCreate(Bundle savedInstanceState) {

        super.onCreate(savedInstanceState);
        dialog = new ProgressDialog(this);
        mNetworkManager = new NetworkManager(this);
        mNetworkManager.setNetworkListener(this);
        fragmentmanager = getSupportFragmentManager();
        init();
        initToolBar();
        if (savedInstanceState != null) {
            restoreInstanceState(savedInstanceState);
        } else {
            onModel();
        }
    }

    public abstract String getPageName();

    public GetMyStoreInfoQuery.GetMyStoreInfo getStoreInfo() {
        return StoreUtils.INSTANCE.getStoreInfoFromPref(this);
    }

    @Override
    public void startActivityForResult(Intent intent, int requestCode) {
        intent.putExtra("requestCode", requestCode);
        super.startActivityForResult(intent, requestCode);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        this.setIntent(intent);
    }

    protected void initToolBar() {
        rootView = findViewById(android.R.id.content);
        if (toolbar == null) {
            toolbar = findViewById(R.id.toolbar);
        }
        if (toolbar != null) {
            setSupportActionBar(toolbar);
            getSupportActionBar().setDisplayShowTitleEnabled(true);
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
    }

    /**
     * restore previous Activity state from  saved bundle params
     *
     * @param savedInstanceState
     */
    protected abstract void restoreInstanceState(Bundle savedInstanceState);

    @Override
    protected void onStart() {
        try {
            super.onStart();
            rootView = findViewById(android.R.id.content);
            EventBusManager.getInstance().register(this);
        } catch (ClassCastException e) {
            FirebaseCrashlytics.getInstance().recordException(e);
            rebirthApp(this);
        }

    }

    @Override
    protected void onResume() {
        super.onResume();
        if (enableAppboyInAppMessages()) {
            BrazeInAppMessageManager.getInstance().registerInAppMessageManager(this);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (enableAppboyInAppMessages()) {
            BrazeInAppMessageManager.getInstance().unregisterInAppMessageManager(this);
        }

    }

    /**
     * This method allows for activities not to receive in-app messages from Braze
     * <p>
     * Override this method to enable Braze in-app messages
     *
     * @return true : enable
     */
    public boolean enableAppboyInAppMessages() {
        return false;
    }

    @Override
    protected void onStop() {
        super.onStop();
        EventBusManager.getInstance().unregister(this);
    }

    /**
     * set activity's content view instance an according layout
     * and  init all the sub views
     */
    public abstract void init();

    /**
     * catch any Model object sent to the activity
     * either by intent bundle or database or any other method
     */
    public abstract void onModel();

    /**
     * Override this method to catch events sent from EventBus
     *
     * @param t
     * @param <T>
     */
    @Subscribe
    public <T extends BusEvent> void onEvent(T t) {
    }


    @Override
    public void onConnected() {

    }

    @Override
    public void onUnavailableNetwork() {
        onOffline();
    }

    /**
     * handle network requests error in offline mode
     */
    public abstract void onOffline();

    public View getRootView() {
        if (rootView == null)
            rootView = findViewById(android.R.id.content);
        return rootView;
    }

    public void displayLoading(boolean cancelable) {
        dialog.setMessage(getResources().getString(R.string.common_loading));
        dialog.setCancelable(cancelable);
        dialog.show();
    }

    public void dismissLoading() {
        dialog.dismiss();
    }

    @Override
    protected void onDestroy() {
        WeakReference<Context> contextWeakReference = new WeakReference<>(this);
        MemoryUtils.trimMemoryIfRequired(contextWeakReference);
        super.onDestroy();
    }
}