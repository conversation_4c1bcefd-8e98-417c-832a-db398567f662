package se.scmv.morocco.activities

import android.app.Activity
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.text.TextUtils
import android.view.Menu
import android.view.MenuItem
import android.webkit.CookieManager
import android.webkit.JavascriptInterface
import android.webkit.WebChromeClient
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.ProgressBar
import se.scmv.morocco.R
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.databinding.ActivityWebviewBinding
import se.scmv.morocco.urlsprovider.UrlsProvider
import java.util.regex.Pattern

/**
 * Created by bachiri on 6/3/16.
 */
class WebViewActivity : BaseActivity() {

    private var _binding: ActivityWebviewBinding?= null
    private val binding: ActivityWebviewBinding
        get() = _binding!!

    private var menu: Menu? = null

    private val isImmoNeuf: Boolean
        get() = intent?.getStringExtra(WEBVIEWBODY) == IMMONEUF

    override fun init() {
        TAG = "WebViewActivity"

        _binding = ActivityWebviewBinding.inflate(layoutInflater)
        setContentView(_binding?.root)

        initToolBar()
        initWebView()
        initProgressBar()
        if (isImmoNeuf) {
            initWebViewClient()
        }
        initWebViewUrlAndTitle(
            webviewbody = intent?.getStringExtra(WEBVIEWBODY),
            webviewtitle = intent?.getStringExtra(WEBVIEWTITLE)
        )
    }

    override fun onBackPressed() {
        with(binding) {
            if (customwebview.canGoBack()) {
                customwebview.goBack()
                menu?.getItem(0)?.isVisible = true
                webviewProgressBar.visibility = ProgressBar.VISIBLE
            } else super.onBackPressed()
        }
    }

    private fun initWebView() {
        with(binding.customwebview) {
            settings.javaScriptEnabled = true
            settings.domStorageEnabled = true
            settings.javaScriptCanOpenWindowsAutomatically = true
        }
    }

    override fun initToolBar() {
        with(binding.webViewToolbar) {
            setSupportActionBar(this)
            supportActionBar?.setDisplayShowTitleEnabled(true)
            supportActionBar?.setDisplayHomeAsUpEnabled(true)
            if (isImmoNeuf) {
                supportActionBar?.setHomeAsUpIndicator(R.drawable.ic_webview_close)
                setBackgroundColor(resources.getColor(R.color.webview_toolbar_background_color))
                setTitleTextColor(Color.WHITE)
                setSubtitleTextColor(Color.WHITE)
            } else {
                setTitleTextColor(Color.BLACK)
                setSubtitleTextColor(Color.BLACK)
                setBackgroundColor(resources.getColor(R.color.white))
                supportActionBar?.setHomeAsUpIndicator(R.drawable.ic_arrow_back_black)
            }
        }
    }

    private fun initProgressBar() {
        with(binding) {
            customwebview.webChromeClient = object : WebChromeClient() {
                override fun onProgressChanged(view: WebView, progress: Int) {
                    if (progress < 100 && webviewProgressBar.visibility == ProgressBar.GONE) {
                        webviewProgressBar.visibility = ProgressBar.VISIBLE
                    }
                    webviewProgressBar.progress = progress
                    if (progress == 100) {
                        webviewProgressBar.visibility = ProgressBar.GONE
                    }
                }
            }
        }
    }

    private fun initWebViewClient() {
        binding.customwebview.webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
                val shareItem: MenuItem
                if (menu != null) {
                    menu?.let {
                        it.getItem(0)?.let {
                            shareItem = it
                            shareItem.isVisible = isImmoNeufURL(url)
                        }
                    }

                }
                return super.shouldOverrideUrlLoading(view, url)
            }

            override fun onPageFinished(view: WebView, url: String) {
                var url = url
                if (supportActionBar != null && !url.isEmpty()) {
                    url = removeHttpOrHttps(url)
                    supportActionBar?.title = ""
                    supportActionBar?.subtitle = url
                }
            }

            override fun onPageStarted(view: WebView, url: String, favicon: Bitmap?) {
                var url = url
                super.onPageStarted(view, url, favicon)
                if (supportActionBar != null) {
                    supportActionBar?.title = getString(R.string.common_loading)
                    url = removeHttpOrHttps(url)
                    supportActionBar?.subtitle = url
                }
            }
        }
    }

    private fun initWebViewUrlAndTitle(webviewbody: String?, webviewtitle: String?) {
        with(binding) {
            when (webviewbody) {
                MOBILE_REGLE -> {
                    customwebview.loadUrl(resources.getString(R.string.url_regle))
                    webViewToolbar.setTitle(R.string.regles_publication)
                }

                MOBILE_CONDITION -> {
                    customwebview.loadUrl(resources.getString(R.string.url_term_of_services))
                    webViewToolbar.setTitle(R.string.common_term_of_services)
                }

                MOBILE_DONNEE -> {
                    customwebview.loadUrl(resources.getString(R.string.url_privacy_policy))
                    webViewToolbar.setTitle(R.string.common_privacy_policy)
                }

                D2D -> initDelivery()
                IMMONEUF -> {
                    intent?.getStringExtra(WEBVIEWURL)?.let {
                        customwebview.loadUrl(it)
                    }
                    if (TextUtils.isEmpty(webviewtitle)) {
                        if (isImmoNeufURL(webviewbody)) {
                            webViewToolbar.title = "Avito Immobilier Neuf"
                        } else webViewToolbar.setTitle(R.string.app_name)
                    } else {
                        webViewToolbar.title = webviewtitle
                    }
                }
                else -> {
                    webViewToolbar.title = webviewtitle
                    intent?.getStringExtra(WEBVIEWURL)?.let {
                        customwebview.loadUrl(it)
                    }
                }
            }
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        this.menu = menu
        menuInflater.inflate(R.menu.webview_menu, menu)
        return super.onCreateOptionsMenu(menu)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == R.id.mShare) {
            val i = Intent(Intent.ACTION_SEND)
            i.type = "text/plain"
            i.putExtra(Intent.EXTRA_TEXT, binding.customwebview.url + "&utm_medium=share")
            startActivity(Intent.createChooser(i, getString(R.string.share_title)))
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onPrepareOptionsMenu(menu: Menu): Boolean {
        val shareItem = menu.findItem(R.id.mShare)
        shareItem.isVisible = binding.customwebview.url?.let { isImmoNeufURL(it) } == true
        return true
    }

    private fun isImmoNeufURL(url: String): Boolean {
        val m = reg.matcher(url)
        return m.find()
    }

    private fun initDelivery() {
        setLangCookie()
        intent?.getStringExtra(WEBVIEWURL)?.let { binding.customwebview.loadUrl(it) }
        if (supportActionBar != null) supportActionBar?.setTitle(R.string.delivery_web_view_title)
        binding.customwebview.addJavascriptInterface(DeliveryWebViewInterface(this), "delivery")
    }

    private fun setLangCookie() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            CookieManager.getInstance().setAcceptThirdPartyCookies(binding.customwebview, true)
            CookieManager.getInstance().removeAllCookies(null)
        } else {
            CookieManager.getInstance().setAcceptCookie(true)
            CookieManager.getInstance().removeAllCookie()
        }
        CookieManager.getInstance().setCookie(
            UrlsProvider.getDeliveryBaseUrl(),
            "lang=" + LocaleManager.getCurrentLanguage() + ";"
        )
    }

    private fun removeHttpOrHttps(url: String): String {
        var url = url
        url = url.replace("http://", "")
        url = url.replace("https://", "")
        return url
    }

    override fun restoreInstanceState(savedInstanceState: Bundle) {}
    override fun onModel() {}
    override fun onOffline() {}
    override fun getPageName(): String {
        return WebViewActivity::class.java.simpleName
    }

    class DeliveryWebViewInterface internal constructor(private val activity: Activity) {
        @JavascriptInterface
        fun quitDeliveryPage() {
            activity.finish()
        }
    }

    companion object {
        const val MOBILE_REGLE = "mobileregle"
        const val MOBILE_CONDITION = "mobilecondition"
        const val MOBILE_DONNEE = "mobiledonnees"
        const val WEBVIEWBODY = "webviewbody"
        const val ON_CLOSE_ANALYTICS_MESSAGE = "on_close_analytics_message"
        const val WEBVIEWTITLE = "webviewtitle" // optional
        const val D2D = "D2D" // optional
        const val IMMONEUF = "immoneuf"
        const val WEBVIEWURL = "url"
        private val reg = Pattern.compile("http?.://immoneuf.avito.ma(.*)")
    }
}