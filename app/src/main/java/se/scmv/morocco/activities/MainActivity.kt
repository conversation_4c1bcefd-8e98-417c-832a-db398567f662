package se.scmv.morocco.activities

import android.Manifest
import android.content.ActivityNotFoundException
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.window.DialogProperties
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.compose.rememberNavController
import com.braze.ui.inappmessage.BrazeInAppMessageManager
import com.google.android.play.core.appupdate.AppUpdateManager
import com.google.android.play.core.appupdate.AppUpdateManagerFactory
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.dynamiclinks.FirebaseDynamicLinks
import com.google.firebase.dynamiclinks.PendingDynamicLinkData
import com.microsoft.clarity.Clarity
import com.microsoft.clarity.ClarityConfig
import com.microsoft.clarity.models.LogLevel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import se.scmv.morocco.BuildConfig
import se.scmv.morocco.R
import se.scmv.morocco.account.presentation.edit_account.EditAccountActivity
import se.scmv.morocco.activities.main.MainViewModel
import se.scmv.morocco.activities.main.MainViewState
import se.scmv.morocco.authentication.presentation.AuthenticationActivity
import se.scmv.morocco.authentication.presentation.common.LoginType
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.designsystem.components.AvConfirmationAlertDialog
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.inappupdate.InAppUpdateUtils
import se.scmv.morocco.inappupdate.InAppUpdateUtils.Companion.resumeAppUpdate
import se.scmv.morocco.inappupdate.InAppUpdateUtils.Companion.updateApp
import se.scmv.morocco.ui.AvitoNavHost
import se.scmv.morocco.ui.SnackBarHostForSnackBarController
import se.scmv.morocco.utils.Constants
import se.scmv.morocco.utils.Keys
import se.scmv.morocco.utils.NotifyUtils.displayDefaultSnackbar
import se.scmv.morocco.utils.NotifyUtils.displaySnackbar
import se.scmv.morocco.utils.NotifyUtils.displaySuccessSnackbar
import se.scmv.morocco.utils.Utils
import javax.inject.Inject

@AndroidEntryPoint
class MainActivity : AppCompatActivity() {

    //InAPP Update
    private var appUpdateManager: AppUpdateManager? = null

    private val mainViewModel: MainViewModel by viewModels()

    @Inject
    lateinit var accountRepository: AccountRepository

    private val requestPermission: ActivityResultLauncher<String> = registerForActivityResult(
        ActivityResultContracts.RequestPermission(), object : ActivityResultCallback<Boolean> {
            override fun onActivityResult(result: Boolean) {

            }
        }
    )



    override fun onCreate(savedInstanceState: Bundle?) {
        val splashScreen = installSplashScreen()
        enableEdgeToEdge()
        super.onCreate(savedInstanceState)
        splashScreen.setKeepOnScreenCondition {
            mainViewModel.viewState.value.shouldKeepSplashScreen()
        }
        setContent {
            AvitoTheme {
                val state = mainViewModel.viewState.collectAsStateWithLifecycle().value
                if (state is MainViewState.Success) {
                    val scope = rememberCoroutineScope()
                    val navController = rememberNavController()
                    Scaffold(
                        snackbarHost = {
                            SnackBarHostForSnackBarController()
                        }
                    ) { paddingValues ->
                        AvitoNavHost(
                            account = state.account,
                            navController = navController,
                            updateAppLanguage = { scope.launch { LocaleManager.switchLanguage() } },
                            openContactSupport = { openContactSupport() },
                            onUpdateAppClicked = {
                                appUpdateManager?.let { updateApp(this, it) }
                            },
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(paddingValues)
                        )
                    }
                    var shouldShowLangDialog by remember { mutableStateOf(shouldShowLangDialog()) }
                    if (shouldShowLangDialog) {
                        LanguageDialog(onDismiss = { shouldShowLangDialog = false })
                    }
                    var shouldShowNotificationPermissionDialog by remember {
                        mutableStateOf(shouldShowNotificationPermissionDialog())
                    }
                    if (shouldShowLangDialog.not() && shouldShowNotificationPermissionDialog) {
                        AskNotificationPermissionDialog(
                            onDismiss = { shouldShowNotificationPermissionDialog = false }
                        )
                    }
                }
            }
        }
        appUpdateManager = AppUpdateManagerFactory.create(this)
        handleFirebaseDynamicLinks()
        appUpdateManager?.let { updateApp(this, it) }

        // fill category and region names in the control bar in case of deeplink
        val config = ClarityConfig(
            projectId = getString(R.string.clarity_project_id),
            logLevel = LogLevel.None
        )
        Clarity.initialize(
            applicationContext,
            config
        )
    }

    private fun shouldShowNotificationPermissionDialog() =
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                this,
                Manifest.permission.POST_NOTIFICATIONS
            ) != PackageManager.PERMISSION_GRANTED
        } else {
            false
        }

    @Composable
    private fun AskNotificationPermissionDialog(onDismiss: () -> Unit) {
        AvConfirmationAlertDialog(
            title = stringResource(R.string.notification_ask_permission_title),
            description = stringResource(R.string.notification_ask_permission_message),
            confirmText = stringResource(R.string.notification_ask_permission_positive_text_button),
            onConfirm = {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    requestPermission.launch(Manifest.permission.POST_NOTIFICATIONS)
                }
                onDismiss()
            },
            cancelText = stringResource(R.string.notification_ask_permission_negative_text_button),
            onCancel = onDismiss
        )
    }

    /**
     * -----------------------------------
     * Firebase Dynamic links handler
     * -----------------------------------
     * Must be executed only when first installed
     */
    private fun handleFirebaseDynamicLinks() {
        if (Utils.getBooleanPreference(
                this,
                Utils.PREF_IS_FIRST_LAUNCHED_AFTER_INSTALL,
                true
            )
        ) {
            FirebaseDynamicLinks.getInstance().getDynamicLink(intent)
                .addOnSuccessListener(this) { pendingDynamicLinkData: PendingDynamicLinkData? ->
                    var deepLink: Uri? = null
                    if (pendingDynamicLinkData != null) {
                        deepLink = pendingDynamicLinkData.link
                    }
                    if (deepLink != null) {
                        Utils.savePreference(
                            this,
                            Utils.PREF_IS_FIRST_LAUNCHED_AFTER_INSTALL,
                            false
                        )
                        startActivity(Intent(Intent.ACTION_VIEW, deepLink))
                    }
                }
        }
    }

    override fun onResume() {
        super.onResume()
        appUpdateManager?.let { resumeAppUpdate(this, it) }
        BrazeInAppMessageManager.getInstance().registerInAppMessageManager(this)
    }

    override fun onPause() {
        super.onPause()
        BrazeInAppMessageManager.getInstance().unregisterInAppMessageManager(this)
    }

    private fun shouldShowLangDialog(): Boolean {
        return !Utils.getBooleanPreference(this, "popupSwitch")
    }

    @Composable
    private fun LanguageDialog(
        onDismiss: () -> Unit = {}
    ) {
        val scope = rememberCoroutineScope()
        val context = LocalContext.current
        AvConfirmationAlertDialog(
            title = stringResource(R.string.lang_popup_title),
            description = stringResource(R.string.lang_popup_message),
            confirmText = stringResource(R.string.arabic_language),
            onConfirm = {
                Utils.savePreference(context, "popupSwitch", true)
                FirebaseAnalytics
                    .getInstance(this)
                    .setUserProperty("lang", LocaleManager.getCurrentLanguage())
                FirebaseAnalytics.getInstance(this).logEvent(
                    getString(R.string.tm_event_switch_to_french_popup),
                    bundleOf()
                )
                scope.launch { LocaleManager.changeToArabic() }
                onDismiss()
            },
            cancelText = stringResource(R.string.french_language),
            onCancel = {
                Utils.savePreference(context, "popupSwitch", true)
                FirebaseAnalytics
                    .getInstance(this)
                    .setUserProperty("lang", LocaleManager.getCurrentLanguage())
                FirebaseAnalytics.getInstance(this).logEvent(
                    getString(R.string.tm_event_switch_to_arabic_popup),
                    bundleOf()
                )
                scope.launch { LocaleManager.changeToFrench() }
                onDismiss()
            },
            properties = DialogProperties(dismissOnClickOutside = false, dismissOnBackPress = false)
        )
    }


    /**
     * Method that handles a click action on the "contactez nous" button in the left drawer
     */
    private fun openContactSupport() {
        try {
            val intent = Intent(Intent.ACTION_VIEW)
            intent.data = Uri.parse("mailto:" + getString(R.string.customer_services_email))
            intent.putExtra(
                Intent.EXTRA_SUBJECT,
                getString(R.string.customer_services_email_subject)
            )
            intent.putExtra(Intent.EXTRA_TEXT, menuContactMessageTemplate())
            startActivity(intent)
        } catch (e: ActivityNotFoundException) {
            val builder = AlertDialog.Builder(this)
            builder.setTitle(R.string.customer_services_email_no_apps_title)
            builder.setMessage(R.string.customer_services_email_no_apps_description)
            builder.setNeutralButton(getString(R.string.common_ok)) { dialog, _ -> dialog.dismiss() }
            builder.show()
        }
    }

    /**
     * Method that returns the email template for "contactez nous"
     *
     * @return: String representing the contact message body to be sent
     */
    private fun menuContactMessageTemplate(): String {
        return StringBuilder("\n\n\n\n")
            .append("----------------------\n")
            .append("----------------------\n")
            .append(this.getString(R.string.customer_services_email_footer_message))
            .append("\n")
            .append(this.getString(R.string.customer_services_email_device_os_version))
            .append(Build.VERSION.RELEASE)
            .append("\n")
            .append(this.getString(R.string.customer_services_email_device_model))
            .append(Build.MANUFACTURER)
            .append(" ").append(Build.DEVICE)
            .append(" ").append(Build.MODEL)
            .append("\n")
            .append(this.getString(R.string.customer_services_email_application_version))
            .append(BuildConfig.VERSION_NAME)
            .toString()
    }

    // TODO replace this by navigating in the NavHost with callbacks
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK) {
            val token = data?.getStringExtra(AuthenticationActivity.TOKEN_KEY) ?: return
            val loginTypeName = data.getStringExtra(AuthenticationActivity.LOGIN_TYPE_KEY) ?: return
            val loginType = try {
                LoginType.valueOf(loginTypeName)
            } catch (e: IllegalArgumentException) {
                return
            }
            mainViewModel.onUserConnected(loginType, token)
        }
        when (requestCode) {
            InAppUpdateUtils.UPDATE_REQUEST_CODE -> {
                if (resultCode == RESULT_OK) return

                val isImmediateUpdateEnabled =
                    Utils.getBooleanPreference(this, Keys.IN_APP_UPDATE_IMMEDIATE_UPDATE_TOGGLE)
                if (isImmediateUpdateEnabled) {
                    handleUpdateDismiss()
                }
            }

            AuthenticationActivity.REQUEST_SIGN_IN -> if (resultCode == RESULT_OK) {
                // switchViewPager(BottomNavigationBar.NAVIGATION_POSITION_HOME)
                displaySuccessSnackbar(TODO(), R.string.login_success_message)
            }

            AuthenticationActivity.REQUEST_SIGN_AD_INSERTION -> if (resultCode == RESULT_OK) {
                // switchViewPager(BottomNavigationBar.NAVIGATION_POSITION_HOME)
            }

            AuthenticationActivity.REQUEST_SIGN_IN_MY_ACCOUNT -> if (resultCode == RESULT_OK) {
                // switchViewPager(BottomNavigationBar.NAVIGATION_POSITION_PROFILE)
                displaySuccessSnackbar(TODO(), R.string.login_success_message)
            }

            AuthenticationActivity.REQUEST_SIGN_IN_FAVORITES_STATS -> if (resultCode == RESULT_OK) {
                // switchViewPager(BottomNavigationBar.NAVIGATION_POSITION_FAVORITES_STATS)
                displaySuccessSnackbar(TODO(), R.string.login_success_message)
            }

            EditAccountActivity.REQUEST_UPDATE_ACCOUNT -> {
                if (resultCode == EditAccountActivity.RESULT_LOGOUT) {
                    displayDefaultSnackbar(
                        TODO(),
                        R.string.common_logout_success_message
                    )
                    // switchViewPager(BottomNavigationBar.NAVIGATION_POSITION_HOME)
                }
            }

            AuthenticationActivity.REQUEST_SIGN_IN_MESSAGING -> if (resultCode == RESULT_OK) {
                // switchViewPager(BottomNavigationBar.NAVIGATION_POSITION_MESSAGING)
            }

            AuthenticationActivity.REQUEST_SIGN_IN_FILTERS -> if (resultCode == RESULT_OK) {
                // switchViewPager(BottomNavigationBar.NAVIGATION_POSITION_HOME)
                // displayFilterSideBar()
            }

            INAPP_UPDATE_REQUEST_CODE -> if (resultCode != RESULT_OK) {
                if (resultCode == RESULT_CANCELED) displaySnackbar(
                    TODO(),
                    if (LocaleManager.isFr()) Utils.getStringPreference(
                        this,
                        Keys.IN_APP_UPDATE_CANCEL_UPDATE_SNACKBAR_TEXT
                    ) else Utils.getStringPreference(
                        this,
                        Keys.IN_APP_UPDATE_CANCEL_UPDATE_SNACKBAR_TEXT_AR
                    ),
                    -1
                )
            }

            else -> if (resultCode == Constants.RESULT_SIGN_IN_FROM_AD_DETAIL) {
                // switchViewPager(BottomNavigationBar.NAVIGATION_POSITION_HOME)
            } else {
                super.onActivityResult(requestCode, resultCode, data)
            }
        }
    }

    private fun handleUpdateDismiss() {
        AlertDialog.Builder(this)
            .setTitle(R.string.update_dismiss_title)
            .setMessage(R.string.update_dismiss_message)
            .setPositiveButton(R.string.timeout_second_retry_message_action) { _, _ ->
                appUpdateManager?.let { updateApp(this, it) }
            }
            .setCancelable(false)
            .show()
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)
    }

    // TODO confirmLeavingApp in compose
    /*override fun onBackPressed() {
    }*/

    private fun confirmLeavingApp() {
        val builder: AlertDialog.Builder =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                AlertDialog.Builder(this, R.style.AlertDialogCustom)
            } else {
                AlertDialog.Builder(this)
            }
        builder.setTitle(R.string.app_name)
            .setMessage(R.string.onbackpressed_dialog_message)
            .setNegativeButton(R.string.no, null)
            .setPositiveButton(R.string.yes) { _, _ ->
                setResult(RESULT_CANCELED)
                finishAffinity()
            }
            .show()
    }

    companion object {
        private const val INAPP_UPDATE_REQUEST_CODE = 530
    }
}