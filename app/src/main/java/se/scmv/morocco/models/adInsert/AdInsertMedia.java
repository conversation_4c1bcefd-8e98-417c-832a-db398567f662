package se.scmv.morocco.models.adInsert;

import android.net.Uri;

import se.scmv.morocco.type.AdMediaType;

public class AdInsertMedia {

    private String imageId;
    private int order;
    private int isDefault;

    public AdInsertMedia() {

    }

    public enum Status {
        PENDING,
        ERROR,
        UPLOADED
    }

    private int containerId;        // Uploaded ImageView ID
    private String originMediaPath;
    private String tempMediaPath;
    private String serverId;        // Remote uploaded image path
    private Uri imageUri;
    private int position;
    private Status status;
    private boolean isLocal = true;
    private boolean isMainMedia;
    private String initImageSize;
    private String compressedImageSize;

    private String path;

    private AdMediaType type;

    public AdInsertMedia(int containerId, String originMediaPath, Uri uri) {
        this.containerId = containerId;
        this.originMediaPath = originMediaPath;
        this.status = Status.PENDING;
        this.imageUri = uri;
    }
    public void setIsDefault(int isDefault) {
        this.isDefault = isDefault;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public AdMediaType getType() {
        return type;
    }

    public void setType(AdMediaType type) {
        this.type = type;
    }

    public String getImageId() {
        return imageId;
    }

    public void setImageId(String imageId) {
        this.imageId = imageId;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public int getIsDefault() {
        return isDefault;
    }

    public int getContainerId() {
        return containerId;
    }

    public void setContainerId(int containerId) {
        this.containerId = containerId;
    }

    public String getOriginMediaPath() {
        return originMediaPath;
    }

    public void setOriginMediaPath(String originMediaPath) {
        this.originMediaPath = originMediaPath;
    }

    public String getTempMediaPath() {
        return tempMediaPath;
    }

    public void setTempMediaPath(String tempMediaPath) {
        this.tempMediaPath = tempMediaPath;
    }

    public String getServerId() {
        return serverId;
    }

    public void setServerId(String serverId) {
        this.serverId = serverId;
    }

    public Uri getImageUri() {
        return imageUri;
    }

    public void setImageUri(Uri imageUri) {
        this.imageUri = imageUri;
    }

    public int getPosition() {
        return position;
    }

    public void setPosition(int position) {
        this.position = position;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public boolean isLocal() {
        return isLocal;
    }

    public void setLocal(boolean local) {
        isLocal = local;
    }

    public boolean isMainMedia() {
        return isMainMedia;
    }

    public void setMainMedia(boolean mainMedia) {
        isMainMedia = mainMedia;
    }

    public String getInitImageSize() {
        return initImageSize;
    }

    public void setInitImageSize(String initImageSize) {
        this.initImageSize = initImageSize;
    }

    public String getCompressedImageSize() {
        return compressedImageSize;
    }

    public void setCompressedImageSize(String compressedImageSize) {
        this.compressedImageSize = compressedImageSize;
    }

    public void setDefault(boolean b) {
        this.isDefault = b ? 1 : 0;
    }
}
