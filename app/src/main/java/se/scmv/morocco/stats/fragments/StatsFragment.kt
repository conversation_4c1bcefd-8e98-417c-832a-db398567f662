package se.scmv.morocco.stats.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.material3.Surface
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.fragment.app.Fragment
import dagger.hilt.android.AndroidEntryPoint
import se.scmv.morocco.account.presentation.statistics.AccountStatisticsRoute
import se.scmv.morocco.analytics.AnalyticsHelper
import se.scmv.morocco.analytics.LocalAnalyticsHelper
import se.scmv.morocco.designsystem.theme.AvitoTheme
import javax.inject.Inject

@AndroidEntryPoint
class StatsFragment : Fragment() {

    @Inject
    lateinit var analyticsHelper: AnalyticsHelper

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
            setContent {
                AvitoTheme {
                    CompositionLocalProvider(LocalAnalyticsHelper provides analyticsHelper) {
                        Surface { AccountStatisticsRoute() }
                    }
                }
            }
        }
    }
}