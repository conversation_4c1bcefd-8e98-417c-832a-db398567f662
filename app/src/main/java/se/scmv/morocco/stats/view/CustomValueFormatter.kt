package se.scmv.morocco.stats.view

import com.github.mikephil.charting.components.AxisBase
import com.github.mikephil.charting.data.BarEntry
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.formatter.ValueFormatter
import java.text.DecimalFormat

class CustomValueFormatter : ValueFormatter() {
        private val format = DecimalFormat("##")

        // override this for e.g. Line<PERSON>hart or Scatter<PERSON>hart
        override fun getPointLabel(entry: Entry?): String {
                return format.format(entry?.y)
        }

        // override this for <PERSON><PERSON><PERSON>
        override fun getBarLabel(barEntry: BarEntry?): String {
                return format.format(barEntry?.y)
        }

        // override this for custom formatting of XAxis or YAxis labels
        override fun getAxisLabel(value: Float, axis: AxisBase?): String {
                return format.format(value)
        }

        override fun getFormattedValue(value: Float): String {
                return format.format(value)
        }

        // ... override other methods for the other chart types
}