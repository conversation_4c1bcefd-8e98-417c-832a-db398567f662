package se.scmv.morocco.avitov2.adinsert.domain.navigation

import android.content.Context
import android.content.Intent
import se.scmv.morocco.activities.MainActivity
import se.scmv.morocco.ad.vas.master.IntentParams
import se.scmv.morocco.avitov2.adinsert.presentation.AdInsertActivity
import se.scmv.morocco.avitov2.vas.presentation.master.NewVasActivity
import se.scmv.morocco.domain.models.VasPacksApplication
import se.scmv.morocco.domain.navigation.AccountNavigation
import se.scmv.morocco.utils.Keys

class AccountNavigationImpl(private val context: Context) : AccountNavigation {

    override fun navigateToAdInsert(
        adId: String?,
        adCategoryKey: String?,
        adType: String?,
        toImageStep: Boolean
    ) {
        val intent = Intent(context, AdInsertActivity::class.java).apply {
            // Only add adId to the intent if it is not null
            adId?.let {
                putExtra(AdInsertActivity.AD_ID_KEY, it)
                putExtra(AdInsertActivity.GO_IMAGE_STEP_KEY, toImageStep)
            }
            flags = Intent.FLAG_ACTIVITY_NEW_TASK // Add this flag
        }

        context.startActivity(intent)
    }

    override fun navigateToVasActivity(
        adId: String,
        adCategoryKey: String,
        adType: String,
        application: VasPacksApplication
    ) {
        val intentParams = IntentParams(
            from = IntentParams.From.Account,
            adId = adId,
            application = application
        )

        val intent = Intent(context, NewVasActivity::class.java).apply {
            putExtra(INTENT_PARAMS_KEY, intentParams) // Replace with actual key if required
            flags = Intent.FLAG_ACTIVITY_NEW_TASK // Add flag for non-activity context
        }

        context.startActivity(intent)
    }

    override fun navigateToAdView(
        adListId: String,
        imageUrl: String?,
        title: String?,
        date: String?,
        imageCount: Int?,
        videoCount: Int?,
        videoUrl: String?,
        isStore: Boolean?,
        price: String?,
        oldPrice: String?,
        location: String?,
        category: String?,
        categoryIcon: String?,
        isUrgent: Boolean?,
        isHotDeal: Boolean?,
        discount: Int?
    ) {
        val intent = Intent(context, MainActivity::class.java).apply {
            putExtra(Keys.AD_ID, adListId)
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        }
        context.startActivity(intent)

    }


    companion object {
        private const val INTENT_PARAMS_KEY =
            "se.scmv.morocco.avitov2.vas.NewVasActivity_INTENT_PARAMS_KEY"
    }
}
