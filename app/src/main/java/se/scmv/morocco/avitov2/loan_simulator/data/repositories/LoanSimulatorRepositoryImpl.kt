package se.scmv.morocco.avitov2.loan_simulator.data.repositories

import com.google.gson.Gson
import se.scmv.morocco.avitov2.loan_simulator.data.dtos.LoanSimulatorConfigDto
import se.scmv.morocco.avitov2.loan_simulator.domain.models.LoanSimulatorConfig
import se.scmv.morocco.avitov2.loan_simulator.domain.repositories.LoanSimulatorRepository
import se.scmv.morocco.domain.repositories.RemoteConfigRepository
import javax.inject.Inject

class LoanSimulatorRepositoryImpl @Inject constructor(
    private val remoteConfigRepository: RemoteConfigRepository,
    private val gson: Gson
): LoanSimulatorRepository {

    override fun getConfig(adCategoryId: String, type: String?): LoanSimulatorConfig? {
        val config = remoteConfigRepository.getString(RemoteConfigRepository.LOAN_SIMULATOR_CONFIG_KEY)
        return try {
            val dto = gson.fromJson(config, LoanSimulatorConfigDto::class.java)
            val isEnabled = dto.loanCategories.any {
                it.category.lowercase() == adCategoryId.lowercase() && it.type.lowercase() == type.orEmpty().lowercase()
            }
            if (isEnabled)
                LoanSimulatorConfig(
                    loanDurations = dto.loanDurations,
                    defaultDuration = dto.defaultDuration,
                    interestPercentage = dto.interestPercentage,
                    redirectionUrl = dto.redirectionUrl
                ) else null
        } catch (e: Exception) {
            null
        }
    }
}