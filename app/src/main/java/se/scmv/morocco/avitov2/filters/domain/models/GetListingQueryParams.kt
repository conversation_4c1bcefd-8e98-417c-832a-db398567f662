package se.scmv.morocco.avitov2.filters.domain.models

import se.scmv.morocco.avitov2.filters.presentation.ListingFiltersViewModel
import se.scmv.morocco.type.AdLocationFilter
import se.scmv.morocco.type.AdSortProperty
import se.scmv.morocco.type.AdTypeKey
import se.scmv.morocco.type.ListingAdParamsFilters
import se.scmv.morocco.type.RangeFilter
import se.scmv.morocco.type.SortOrder
import se.scmv.morocco.utils.isNotNull

data class GetListingQueryParams(
    val categoryId: Int,
    val hasImage: Boolean,
    val hasPrice: Boolean,
    val text: String? = null,
    val type: AdTypeKey? = null,
    val price: RangeFilter? = null,
    val location: AdLocationFilter? = null,
    val offersShipping: Boolean? = null,
    val offersShippingWithinCity: Boolean? = null,
    val isHotDeal: Boolean? = null,
    val isUrgent: Boolean? = null,
    val isEcommerce: Boolean? = null,
    val isNewConstruction: Boolean? = null,
    val isPremium: Boolean? = null,
    val isVerifiedSeller: Boolean? = null,
    val params: ListingAdParamsFilters? = null,
    val adSortProperty : AdSortProperty = AdSortProperty.LIST_TIME,
    val sortOrder : SortOrder = SortOrder.DESC,
    val extendSearch: Boolean = true,
    val isStore: Boolean? = null
) {
    companion object {
        val default = GetListingQueryParams(
            categoryId = ListingFiltersViewModel.DEFAULT_CATEGORY_ID,
            hasPrice = false,
            hasImage = false,
        )
    }

    fun getDifferenceCount(): Int {
        var count = 0
        if (categoryId != default.categoryId) count++
        if (hasPrice != default.hasPrice) count++
        if (hasImage != default.hasImage) count++
        if (!text.isNullOrEmpty()) count++
        if (price != default.price) count++
        if (offersShipping == true) count++
        if (isHotDeal == true) count++
        if (isUrgent == true) count++
        if (isVerifiedSeller == true) count++
        if (isEcommerce == true) count++
        if (isNewConstruction == true) count++
        if (isPremium == true) count++

        count += if (location?.cityIds?.getOrNull()?.isNotEmpty() == true) 1 else 0
        count += params?.singleMatch?.getOrNull()?.text?.getOrNull()
            ?.filter { it.value.isNotEmpty() }?.size ?: 0

        count += params?.singleMatch?.getOrNull()?.numeric?.getOrNull()
            ?.filter { it.value != 0.0 }?.size ?: 0

        count += params?.singleMatch?.getOrNull()?.boolean?.getOrNull()?.filter { it.value }?.size
            ?: 0

        count += params?.listMatch?.getOrNull()?.textList?.getOrNull()
            ?.filter { it?.value?.isNotEmpty() ?: false }
            ?.size ?: 0

        count += params?.listMatch?.getOrNull()?.numericList?.getOrNull()
            ?.filter { it?.value?.isNotEmpty() == true }?.size ?: 0

        count += params?.rangeMatch?.getOrNull()?.filter { it.value.isNotNull() }?.size ?: 0

        return count
    }
}