package se.scmv.morocco.avitov2.filters.data.repositories

import com.apollographql.apollo3.ApolloClient
import com.apollographql.apollo3.api.Optional
import se.scmv.morocco.GetListingAdsCountQuery
import se.scmv.morocco.GetListingAdsCountTwoQuery
import se.scmv.morocco.avitov2.core.domain.models.ERROR_CODE_DEFAULT
import se.scmv.morocco.avitov2.core.domain.models.Resource
import se.scmv.morocco.avitov2.filters.data.network.ListingFiltersApi
import se.scmv.morocco.avitov2.filters.domain.models.GetListingQueryParams
import se.scmv.morocco.avitov2.filters.domain.repositories.ListingFiltersRepository
import se.scmv.morocco.data.rest.config.dtos.ListingCategoryFiltersDto
import se.scmv.morocco.data.rest.config.dtos.ListingFilterCategoriesDto
import se.scmv.morocco.utils.toOptional
import javax.inject.Inject

class ListingFiltersRepositoryImpl @Inject constructor(
    private val listingFiltersApi: ListingFiltersApi,
    private val apolloClient: ApolloClient
) : ListingFiltersRepository {

    override suspend fun getFiltersCategories(): Resource<List<ListingFilterCategoriesDto>> {
        return try {
            val categories = listingFiltersApi.getFiltersCategories()
            Resource.Success(categories)
        } catch (e: Exception) {
            e.printStackTrace()
            Resource.Failure.Exception(e)
        }
    }

    override suspend fun getFilters(
        id: Int,
        type: String
    ): Resource<ListingCategoryFiltersDto.Filters> {
        val filters = runCatching {
            listingFiltersApi.getFilters(id, type).filters
        }.getOrElse {
            //get from remote
            return Resource.Failure.Exception(it)
        }
        return filters.let {
            Resource.Success(it)
        }
    }

    override suspend fun getAdsCount(params: GetListingQueryParams): Resource<Int> {
        return try {

            val query = if (params.offersShippingWithinCity == null) {
                GetListingAdsCountQuery(
                    isStore = Optional.presentIfNotNull(params.isStore),
                )
            } else {
                GetListingAdsCountTwoQuery(
                    text = params.text.toOptional(),
                    categoryId = params.categoryId,
                    hasPrice = params.hasPrice,
                    hasImage = params.hasImage,
                    type = params.type.toOptional(),
                    price = params.price.toOptional(),
                    location = params.location.toOptional(),
                    offersShipping = params.offersShipping.toOptional(),
                    offersShippingWithinCity = params.offersShippingWithinCity.toOptional(),
                    isHotDeal = params.isHotDeal.toOptional(),
                    isUrgent = params.isUrgent.toOptional(),
                    isEcommerce = params.isEcommerce.toOptional(),
                    isImmoneuf = params.isNewConstruction.toOptional(),
                    isPremium = params.isPremium.toOptional(),
                    isVerifiedSeller = params.isVerifiedSeller.toOptional(),
                    isStore = Optional.presentIfNotNull(params.isStore),
                    params = params.params.toOptional()
                )
            }

            val response = apolloClient.query(
                query
            ).execute()

            val count = when (val data = response.data) {
                is GetListingAdsCountTwoQuery.Data -> data.getListingAds.count.total
                is GetListingAdsCountQuery.Data -> data.getListingAds.count.total
                else -> null
            }
            count?.let {
                Resource.Success(it)
            } ?: Resource.Failure.Error(ERROR_CODE_DEFAULT, "")
        } catch (e: Exception) {
            Resource.Failure.Error(ERROR_CODE_DEFAULT, "")
        }
    }
}