package se.scmv.morocco.avitov2.core.domain.models

const val ERROR_CODE_DEFAULT = 1
const val ERROR_CODE_USER_NOT_LOGGED_IN = 2

sealed interface Resource<out T> {
    data class Success<T>(val data: T) : Resource<T>
    sealed interface Failure : Resource<Nothing> {
        data class Error(val code: Int, val message: String) : Failure
        data class Exception(val throwable: Throwable) : Failure
    }
}

val errorUserNotLogged = Resource.Failure.Error(ERROR_CODE_USER_NOT_LOGGED_IN, "")
val exceptionUserNotLogged = Throwable("User not loggedIn")