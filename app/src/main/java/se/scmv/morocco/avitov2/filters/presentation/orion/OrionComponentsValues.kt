package se.scmv.morocco.avitov2.filters.presentation.orion

import ma.avito.orion.ui.OrionField
import ma.avito.orion.ui.OrionOutValue
import ma.avito.orion.ui.input.chips.OrionChipsOutValue
import ma.avito.orion.ui.input.dropdown.OrionSmartDropdownOutValue
import ma.avito.orion.ui.input.edittext.OrionEditTextOutValue
import ma.avito.orion.ui.input.slider.NewOrionSliderOutValue
import ma.avito.orion.ui.input.slider.OrionMinMaxOutValue
import ma.avito.orion.ui.input.toggle.OrionToggleOutValue
import se.scmv.morocco.avitov2.adinsert.presentation.builders.FieldUIType
import se.scmv.morocco.utils.isNotEmpty
import se.scmv.morocco.utils.isNotNull

fun Pair<String, OrionField>.collectValue(): Map<String, Pair<Any, String>> {
    val paramsKeyValues = mutableMapOf<String, Pair<Any, String>>()
    val (type, orionField) = this
    when (type) {

        FieldUIType.TOGGLE_FIELD_FILTER.id -> {
            if (orionField.getValue()
                    .isNotNull() && orionField.getValue() is OrionToggleOutValue
            ) {
                paramsKeyValues[orionField.getKey()] =
                    Pair((orionField.getValue() as OrionToggleOutValue).toggleValue, type)
            }
        }

        FieldUIType.SINGLE_SELECT_SMART_DROPDOWN.id, FieldUIType.SINGLE_SELECT_SMART_DROPDOWN_ICON.id -> {
            if (orionField.getValue()
                    .isNotNull() && orionField.getValue() is OrionSmartDropdownOutValue
            ) {

                (orionField.getValue() as OrionSmartDropdownOutValue)?.let { value ->
                    value.selectedItems?.joinToString(separator = ",") { it.key }
                        ?.let { joinedKeys ->
                            paramsKeyValues[value.parentKey] = Pair(joinedKeys, type)
                        }


                    value.selectedItems?.firstOrNull()?.children?.joinToString(separator = ",") { it.key }
                        ?.let { joinedChildKeys ->
                            paramsKeyValues[value.childKey] = Pair(joinedChildKeys, type)
                        }
                }
            }
        }

        FieldUIType.SINGLE_SELECT_EXTENDED.id -> {
            if (orionField.getValue()
                    .isNotNull() && orionField.getValue() is OrionChipsOutValue && (orionField.getValue() as OrionChipsOutValue).chipsValue.isNotEmpty()
            )
                paramsKeyValues[orionField.getKey()] =
                    Pair((orionField.getValue() as OrionChipsOutValue).chipsValue.first(), type)
        }

        FieldUIType.MULTISELECT_EXTENDED.id -> {
            if (orionField.getValue()
                    .isNotNull() && orionField.getValue() is OrionChipsOutValue && (orionField.getValue() as OrionChipsOutValue).chipsValue.isNotEmpty()
            ) {
                paramsKeyValues[orionField.getKey()] =
                    Pair((orionField.getValue() as OrionChipsOutValue).chipsValue.toList(), type)
            }
        }

        FieldUIType.TEXT_FIELD.id -> {
            if (orionField.getValue()
                    .isNotNull() && orionField.getValue() is OrionEditTextOutValue && (orionField.getValue() as OrionEditTextOutValue).editTextValue.isNotEmpty()
            )
                paramsKeyValues[orionField.getKey()] =
                    Pair((orionField.getValue() as OrionEditTextOutValue).editTextValue, type)
        }

        FieldUIType.MEASURE_TEXT_FIELD.id -> {
            try {
                if (orionField.getValue()
                        .isNotNull() && orionField.getValue() is OrionEditTextOutValue && (orionField.getValue() as OrionEditTextOutValue).editTextValue.isNotEmpty()
                )
                    paramsKeyValues[orionField.getKey()] =
                        Pair(
                            (orionField.getValue() as OrionEditTextOutValue).editTextValue.toInt(),
                            type
                        )
            } catch (e: NumberFormatException) {
                // Handle the exception as needed
            }
        }

        FieldUIType.SLIDER.id -> {
            orionField.getValue()?.let { value ->
                if (value is NewOrionSliderOutValue) {
                    value.sliderMinMax?.let { sliderMinMax ->
                        paramsKeyValues[orionField.getKey()] = sliderMinMax to type
                    }
                }
            }
        }

        FieldUIType.MIN_MAX_FIELD.id -> {
            orionField.getValue().let { value ->
                (value as? OrionMinMaxOutValue)?.minMaxValue?.let { minMaxValue ->
                    paramsKeyValues[orionField.getKey()] = minMaxValue to type
                }
            }
        }


        FieldUIType.MULTIPLE_SELECT_SMART_DROPDOWN.id, FieldUIType.MULTIPLE_SELECT_SMART_DROPDOWN_ICON.id -> {
            if (orionField.getValue()
                    .isNotNull() && orionField.getValue() is OrionSmartDropdownOutValue
            ) {

                (orionField.getValue() as OrionSmartDropdownOutValue).let { value ->
                    value.selectedItems?.joinToString(separator = ",") { it.key }
                        ?.let { joinedKeys ->
                            paramsKeyValues[(orionField.getValue() as OrionSmartDropdownOutValue).parentKey] =
                                Pair(joinedKeys, type)


                        }


                    value.selectedItems?.firstOrNull()?.children?.takeIf { it.isNotEmpty() }
                        ?.joinToString(separator = ",") { it.key }?.let { joinedKeys ->
                        paramsKeyValues[value.childKey] = Pair(joinedKeys, type)
                    }



                    if ((orionField.getValue() as OrionSmartDropdownOutValue).parentKey == "city") {


                        value.selectedItems?.joinToString(separator = ",") { it.value }
                            ?.let { joinedValues ->
                                paramsKeyValues["cityLabel"] =
                                    Pair(joinedValues.orEmpty(), type)
                            }

                        value.selectedItems?.firstOrNull()?.children?.joinToString(separator = ",") { it.value }
                            ?.let { joinedValues ->
                                paramsKeyValues["areaLabel"] =
                                    Pair(joinedValues.orEmpty(), type)
                            }


                    }

                }
            }
        }
    }
    return paramsKeyValues
}

fun OrionOutValue.hasValue() = when (this) {

    is OrionToggleOutValue -> toggleValue

    is OrionSmartDropdownOutValue -> selectedItems.isNotEmpty()

    is OrionChipsOutValue -> chipsValue.isNotEmpty()

    is OrionEditTextOutValue -> editTextValue.isNotBlank()

    is NewOrionSliderOutValue -> sliderMinMax != null

    else -> false
}
