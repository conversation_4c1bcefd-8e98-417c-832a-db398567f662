package se.scmv.morocco.avitov2.di

import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import se.scmv.morocco.adstickybanner.repositories.AdServerRepository
import se.scmv.morocco.adstickybanner.repositories.AdServerRepositoryImpl
import se.scmv.morocco.avitov2.filters.data.repositories.ListingFiltersRepositoryImpl
import se.scmv.morocco.avitov2.filters.domain.repositories.ListingFiltersRepository
import se.scmv.morocco.avitov2.loan_simulator.data.repositories.LoanSimulatorRepositoryImpl
import se.scmv.morocco.avitov2.loan_simulator.domain.repositories.LoanSimulatorRepository
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class BindingsModule {

    @Binds
    abstract fun bindListingFiltersRepository(
        listingFiltersRepositoryImpl: ListingFiltersRepositoryImpl
    ): ListingFiltersRepository

    @Binds
    abstract fun bindAdServerRepository(
        adServerRepositoryImpl: AdServerRepositoryImpl
    ): AdServerRepository


    @Binds
    abstract fun bindLoanSimulatorRepository(
        loanSimulatorRepositoryImpl: LoanSimulatorRepositoryImpl
    ): LoanSimulatorRepository
}