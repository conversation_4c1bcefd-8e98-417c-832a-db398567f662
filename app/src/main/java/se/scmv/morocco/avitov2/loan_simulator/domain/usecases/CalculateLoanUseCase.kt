package se.scmv.morocco.avitov2.loan_simulator.domain.usecases

import se.scmv.morocco.avitov2.loan_simulator.domain.models.LoanSimulatorConfig
import se.scmv.morocco.avitov2.loan_simulator.domain.models.LoanSimulatorInput
import se.scmv.morocco.avitov2.loan_simulator.domain.models.LoanSimulatorOutput
import javax.inject.Inject
import kotlin.math.abs
import kotlin.math.pow
import kotlin.math.roundToLong

class CalculateLoanUseCase @Inject constructor(
    private val config: LoanSimulatorConfig,
) {
    operator fun invoke(input: LoanSimulatorInput): LoanSimulatorOutput {

        val months = 12 * input.loanDuration
        val interestPerYear = config.interestPercentage / 100 / 12

        val loanAmount = input.goodPrice - input.personalContributionPrice

        // monthlyPayment = [goodPrice × (interestPercentage/12)]/[1 – (1 + (interestPercentage/12) – (12 × loanDuration))]
        val result = (1 + interestPerYear).pow(months)
        val monthlyPayment = ((result * interestPerYear * loanAmount) / (result - 1)).roundToLong()

        val interestAmount = abs(loanAmount - (monthlyPayment * months )).roundToLong()
        val personalContributionPercentage = if (input.goodPrice == 0.0) {
            0.0
        } else input.personalContributionPrice * 100 / input.goodPrice

        return LoanSimulatorOutput(
            monthlyPayment = monthlyPayment,
            loanAmount = loanAmount,
            interestAmount = interestAmount,
            personalContributionPercentage = personalContributionPercentage
        )
    }
}