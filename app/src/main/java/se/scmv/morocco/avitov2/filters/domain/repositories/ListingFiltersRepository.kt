package se.scmv.morocco.avitov2.filters.domain.repositories

import se.scmv.morocco.avitov2.core.domain.models.Resource
import se.scmv.morocco.avitov2.filters.domain.models.GetListingQueryParams
import se.scmv.morocco.data.rest.config.dtos.ListingCategoryFiltersDto
import se.scmv.morocco.data.rest.config.dtos.ListingFilterCategoriesDto


interface ListingFiltersRepository {

    suspend fun getFiltersCategories(): Resource<List<ListingFilterCategoriesDto>>

    suspend fun getFilters(id: Int, type: String): Resource<ListingCategoryFiltersDto.Filters>

    suspend fun getAdsCount(listingQueryParams: GetListingQueryParams): Resource<Int>
}