package se.scmv.morocco.avitov2.core.presentation

import android.view.View
import se.scmv.morocco.core.UiText
import se.scmv.morocco.utils.NotifyUtils

enum class SnackBarType {
    STANDARD, SUCCESS, ERROR
}

fun showSnackBar(
    type: SnackBarType,
    message: Int,
    view: View
) {
    when (type) {
        SnackBarType.STANDARD -> NotifyUtils.displayDefaultSnackbar(view, message)

        SnackBarType.SUCCESS -> NotifyUtils.displaySuccessSnackbar(view, message)

        SnackBarType.ERROR -> NotifyUtils.displayErrorSnackbar(view, message)
    }
}

fun showSnackBar(
    type: SnackBarType,
    message: UiText,
    view: View
) {
    when (type) {
        SnackBarType.STANDARD -> NotifyUtils.displayDefaultSnackbar(view, message.getValue(view.context))

        SnackBarType.SUCCESS -> NotifyUtils.displaySuccessSnackbar(view, message.getValue(view.context))

        SnackBarType.ERROR -> NotifyUtils.displayErrorSnackbar(view, message.getValue(view.context))
    }
}