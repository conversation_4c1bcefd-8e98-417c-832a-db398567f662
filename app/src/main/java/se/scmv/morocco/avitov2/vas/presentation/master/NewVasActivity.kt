package se.scmv.morocco.avitov2.vas.presentation.master

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.layout.consumeWindowInsets
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.core.os.bundleOf
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.collectLatest
import se.scmv.morocco.account.presentation.myads.AccountAdsActivity
import se.scmv.morocco.ad.vas.master.IntentParams
import se.scmv.morocco.ad.vas.master.VasMasterRoute
import se.scmv.morocco.ad.vas.master.VasMasterViewModel
import se.scmv.morocco.avitov2.adinsert.presentation.AdInsertActivity
import se.scmv.morocco.avitov2.vas.presentation.activities.EmbeddedWebViewActivity
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.ui.SnackBarHostForSnackBarController
import javax.inject.Inject

@AndroidEntryPoint
class NewVasActivity : AppCompatActivity() {

    companion object {
        const val INTENT_PARAMS_KEY =
            "se.scmv.morocco.avitov2.vas.NewVasActivity_INTENT_PARAMS_KEY"


        fun open(context: Context, params: IntentParams) {
            context.startActivity(
                Intent(context, NewVasActivity::class.java).apply {
                    putExtra(INTENT_PARAMS_KEY, params)
                }
            )
        }
    }

    private val viewModel: VasMasterViewModel by viewModels()

    @Inject
    lateinit var accountRepository: AccountRepository

    // Intent params.
    private lateinit var intentParams: IntentParams

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Shouldn't be null, but we add return as security.
        <EMAIL> = intent.extras?.getParcelable(INTENT_PARAMS_KEY)
            ?: throw IllegalStateException("IntentParams must not be null!")

        enableEdgeToEdge()
        setContent {
            AvitoTheme {
                val accountState = remember { mutableStateOf<Account?>(null) }
                when (val account = accountState.value) {
                    is Account.Connected -> {
                        Scaffold(
                            snackbarHost = { SnackBarHostForSnackBarController() }
                        ) { paddingValues ->
                            VasMasterRoute(
                                modifier = Modifier
                                    .safeDrawingPadding()
                                    .consumeWindowInsets(paddingValues),
                                account = account,
                                viewModel = viewModel,
                                closeVas = {
                                    onBackPressedDispatcher.onBackPressed()
                                    finish()
                                },
                                openUrl = ::openUrl,
                                navigateToAccountAds = ::openAccountAdsScreen,
                                navigateToAdInsertImageStep = ::onAdImageToAdClicked
                            )
                        }
                    }

                    else -> {}
                }
                LaunchedEffect(Unit) {
                    accountRepository.currentAccount.collectLatest {
                        // accountState.value == null check is so important,
                        // because after shop payment we refresh the account sold/points in datastore so currentAccount will emit again.
                        if (it is Account.Connected && accountState.value == null) {
                            accountState.value = it

                        }
                    }
                }
            }
        }
    }

    private fun openUrl(link: String, isCreditCardPayment: Boolean) {
        val intent = Intent(this, EmbeddedWebViewActivity::class.java).apply {
            putExtras(
                bundleOf().apply {
                    // Add the payment type to the passed bundle
                    if (isCreditCardPayment) {
                        putString(
                            EmbeddedWebViewActivity.WEBVIEW_CONTENT_TYPE,
                            EmbeddedWebViewActivity.MONETISATION
                        )
                        putString(EmbeddedWebViewActivity.CUSTOMER_TOKEN, link)
                    } else {
                        putString(
                            EmbeddedWebViewActivity.WEBVIEW_CONTENT_TYPE,
                            EmbeddedWebViewActivity.MEDIA
                        )
                        putString(EmbeddedWebViewActivity.WEBVIEW_URL, link)
                    }
                }
            )
        }
        startActivity(intent)
        if (isCreditCardPayment) {
            finish()
        }
    }

    private fun openAccountAdsScreen() {
        if (intentParams.from == IntentParams.From.Account) {
            onBackPressedDispatcher.onBackPressed()
            finish()
        } else {
            startActivity(Intent(this, AccountAdsActivity::class.java))
            finish()
        }
    }

    fun onAdImageToAdClicked() {
        Intent(
            this,
            AdInsertActivity::class.java
        ).apply {
            putExtra(
                AdInsertActivity.AD_ID_KEY,
                intentParams.adId
            )
            putExtra(
                AdInsertActivity.GO_IMAGE_STEP_KEY,
                true
            )
        }.also {
            startActivity(it)
            finish()
        }
    }
}