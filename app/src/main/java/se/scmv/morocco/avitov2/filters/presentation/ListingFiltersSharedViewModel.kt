package se.scmv.morocco.avitov2.filters.presentation

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import se.scmv.morocco.avitov2.filters.domain.models.GetListingQueryParams
import se.scmv.morocco.avitov2.filters.domain.models.GetListingQueryParamsWithLabels
import se.scmv.morocco.domain.models.Category

class ListingFiltersSharedViewModel : ViewModel() {

    var previousFilters = GetListingQueryParamsWithLabels(
        getListingQueryParams = GetListingQueryParams.default,
        cityLabel = null,
        areaLabel = null
    )

    private val _targetCategoryAndCity = MutableStateFlow(Pair("", listOf<Int?>(0)))
    val targetCategoryAndCity get() = _targetCategoryAndCity

    sealed interface FiltersEvents {
        data class FiltersChanged(val filters: GetListingQueryParamsWithLabels) : FiltersEvents

        data class SearchSuggestionSelected(
            val id: Int,
            val type: String,
            val suggestion: String = ""
        ) : FiltersEvents

        data object SaveSearchAfterSignIn : FiltersEvents

        data object LoadFilters : FiltersEvents

        data object LoadCounter : FiltersEvents

        data object OpenCityDropDown : FiltersEvents

        data class OnListingCategorySelected(val category: Category) : FiltersEvents
    }

    private val _filtersEvents: MutableSharedFlow<FiltersEvents> = MutableSharedFlow()
    val filtersEvents = _filtersEvents.asSharedFlow()

    private val _deepLinkFiltersEvents: MutableStateFlow<MutableMap<String, Pair<Any, String>>?> =
        MutableStateFlow(null)
    val deepLinkFiltersEvents = _deepLinkFiltersEvents.asSharedFlow()

    fun onFiltersChanged(filters: GetListingQueryParamsWithLabels) {
        _targetCategoryAndCity.update {
            Pair(
                filters.getListingQueryParams.categoryId.toString(),
                filters.getListingQueryParams.location?.cityIds?.getOrNull()?: emptyList()
            )
        }
        onEvent(FiltersEvents.FiltersChanged(filters))
        previousFilters = filters
    }


    fun onSearchSuggestionSelected(
        id: Int,
        type: String,
        suggestion: String = ""
    ) = onEvent(FiltersEvents.SearchSuggestionSelected(id, type, suggestion))

    fun onSaveSearchAfterSignIn() = onEvent(FiltersEvents.SaveSearchAfterSignIn)

    fun onLoadFilters() = onEvent(FiltersEvents.LoadFilters)

    fun onLoadCounter() = onEvent(FiltersEvents.LoadCounter)

    fun onOpenCityDropDown() = onEvent(FiltersEvents.OpenCityDropDown)

    fun onApplyFiltersFromSaveSearchOrDeeplink(
        reverseMapParams: MutableMap<String, Pair<Any, String>>? = null
    ) {
        _deepLinkFiltersEvents.update { reverseMapParams }
    }

    fun onEvent(event: FiltersEvents) {
        viewModelScope.launch { _filtersEvents.emit(event) }
    }
}
