package se.scmv.morocco.avitov2.floatingsearchview.suggestions.model

import android.os.Parcel
import android.os.Parcelable

class SearchSuggestionImpl : SearchSuggestion {
    private var mSuggestion: String = ""
    private var mKeyword: String = ""
    private var mCategoryName: String = ""
    private var mId: Int = 0
    private var mCityId: Int = 0
    private var mCityName: String = ""
    private var mAdTypeKey: String = ""
    private var mAdTypeName: String = ""
    private var mModelKey: String = ""
    private var mModel: String = ""
    private var mModelName: String = ""
    private var mBrandKey: String = ""
    private var mBrand: String = ""
    private var mBrandName: String = ""
    override var isHistory : Boolean = false

    constructor(
        suggestion: String,
        keyword: String,
        category: String,
        id: Int,
        adTypeName: String,
        cityId: Int,
        cityName: String,
        adTypeKey: String,
        modelKey: String,
        model: String,
        modelName: String,
        brandKey: String,
        brand: String,
        brandName: String,
        isHistory : Boolean = false
    ) {
        mSuggestion = suggestion
        mKeyword = keyword
        mCategoryName = category
        mAdTypeName = adTypeName
        mId = id
        this.mAdTypeKey = adTypeKey
        mCityId = cityId
        mCityName = cityName
        this.mAdTypeKey = adTypeKey
        mModel = model
        mModelKey = modelKey
        mModelName = modelName
        mBrand = brand
        mBrandKey = brandKey
        mBrandName = brandName
        this.isHistory = isHistory
    }

    constructor(source: Parcel) {
        mSuggestion = source.readString()!!
        isHistory = source.readInt() != 0
    }

    override val suggestion: String
        get() = mSuggestion

    override val keyword: String
        get() = mKeyword

    override val categoryName: String
        get() = mCategoryName

    override val id: Int
        get() = mId

    override val adTypeKey: String
        get() = this.mAdTypeKey

    override val cityId: Int
        get() = mCityId
    override val cityName: String
        get() = mCityName

    override val adTypeName: String
        get() = mAdTypeName
    override val modelKey: String
        get() = mModelKey

    override val model: String
        get() = mModel
    override val modelName: String
        get() = mModelName
    override val brandKey: String
        get() = mBrandKey

    override val brand: String
        get() = mBrand
    override val brandName: String
        get() = mBrandName

    override fun describeContents(): Int {
        return 0
    }

    override fun writeToParcel(dest: Parcel, flags: Int) {
        dest.writeString(mSuggestion)
        dest.writeInt(if (isHistory) 1 else 0)
    }

    companion object {
        @JvmField
        val CREATOR: Parcelable.Creator<SearchSuggestionImpl?> =
            object : Parcelable.Creator<SearchSuggestionImpl?> {
                override fun createFromParcel(`in`: Parcel): SearchSuggestionImpl? {
                    return SearchSuggestionImpl(`in`)
                }

                override fun newArray(size: Int): Array<SearchSuggestionImpl?> {
                    return arrayOfNulls(size)
                }
            }
    }
}