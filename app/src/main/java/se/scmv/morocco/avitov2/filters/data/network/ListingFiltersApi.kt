package se.scmv.morocco.avitov2.filters.data.network

import retrofit2.http.GET
import retrofit2.http.Query
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.data.rest.config.dtos.ListingCategoryFiltersDto
import se.scmv.morocco.data.rest.config.dtos.ListingFilterCategoriesDto

interface ListingFiltersApi {

    @GET("tree")
    suspend fun getFiltersCategories(
        @Query("lang") lang: String = LocaleManager.getCurrentLanguage()
    ): List<ListingFilterCategoriesDto>

    @GET("filters")
    suspend fun getFilters(
        @Query("category_id") categoryId: Int,
        @Query("type") type: String,
        @Query("lang") lang: String = LocaleManager.getCurrentLanguage()
    ): ListingCategoryFiltersDto
}