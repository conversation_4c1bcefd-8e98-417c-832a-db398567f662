package se.scmv.morocco.avitov2.listing.presentation

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Label
import androidx.compose.material3.PlainTooltip
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import kotlinx.coroutines.delay
import se.scmv.morocco.R

@OptIn(ExperimentalMaterial3Api::class)
@Preview(showBackground = true)
@Composable
fun NotifAdComponent(
    image : Any = painterResource(R.drawable.raun)
){
        Box(
            modifier = Modifier.fillMaxHeight()
                .wrapContentWidth(),
            contentAlignment = Alignment.CenterEnd
        ){
            Box(
                modifier = Modifier.fillMaxHeight()
                    .width(10.dp)
                    .background(Color(0xFF585858))
            )
            Row(
                modifier = Modifier.align(Alignment.Center),
                verticalAlignment = Alignment.CenterVertically
            ) {

                var isTextVisible by remember { mutableStateOf(true) } // Track visibility

                // Automatically hide text after 4 seconds
                LaunchedEffect(Unit) {
                    isTextVisible = false
                    delay(4000)
                    isTextVisible = true
                    delay(4000)
                    isTextVisible = false
                }

                Label(
                    label = {
                        PlainTooltip(
                            modifier = Modifier.wrapContentWidth(),
                            shape = CircleShape,
                            containerColor = Color(0xFF585858),
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                AsyncImage(
                                    model = image,
                                    contentDescription = null,
                                    modifier = Modifier
                                        .size(40.dp)
                                        .clip(CircleShape)
                                        .border(1.dp, Color(0xFF585858), CircleShape)
                                        .clickable { }
                                        .drawBehind {
                                            drawRoundRect(
                                                color = Color(0xFF585858),
                                                cornerRadius = CornerRadius(20f)
                                            )
                                        }
                                )
                                // Animated Text Visibility
                                AnimatedVisibility(
                                    visible = isTextVisible,
                                    enter = fadeIn(),
                                    exit = fadeOut()
                                ) {
                                    Text(
                                        text = "Renault APV",
                                        modifier = Modifier.padding(end = 10.dp)
                                    )
                                }
                            }
                        }
                    },
                    isPersistent = true
                ) {}


            }

        }



}


