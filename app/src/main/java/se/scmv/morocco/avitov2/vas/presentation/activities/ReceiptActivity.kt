package se.scmv.morocco.avitov2.vas.presentation.activities

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import pub.devrel.easypermissions.EasyPermissions
import se.scmv.morocco.R
import se.scmv.morocco.activities.BaseActivity
import se.scmv.morocco.activities.MainActivity
import se.scmv.morocco.analytics.AnalyticsManager
import se.scmv.morocco.databinding.ActivityReceiptBinding
import se.scmv.morocco.events.SwitchToTab
import se.scmv.morocco.login.models.AccountToken
import se.scmv.morocco.services.SaveImageInternallyService
import se.scmv.morocco.utils.Constants
import se.scmv.morocco.utils.DateUtils
import se.scmv.morocco.utils.EventBusManager
import se.scmv.morocco.utils.ImageUtils
import se.scmv.morocco.widgets.BottomNavigationBar

class ReceiptActivity : BaseActivity(), EasyPermissions.PermissionCallbacks {

    private var _binding: ActivityReceiptBinding? = null
    private val binding: ActivityReceiptBinding
        get() = _binding!!

    var extras: Bundle? = null
    var isLoggedIn = false

    override fun init() {
        _binding = ActivityReceiptBinding.inflate(layoutInflater)
        setContentView(_binding?.root)
        // Set toolbar
        setToolbar()
        isLoggedIn = AccountToken.isLoggedIn(this)

        // Get extras
        extras = intent.extras
        if (extras != null) {
            fillInTheReceipt()
        }
        setUpTheLoggedInUI()
        //Push Receipt Screen
        AnalyticsManager.instance?.pushScreen(this, getString(R.string.an_screen_receipt))
    }

    private fun setToolbar() {
        toolbar = binding.toolbar.toolbar
        toolbar?.setTitle(R.string.vas_receipt_activity_title)
        setSupportActionBar(toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(false)
        toolbar?.setNavigationIcon(R.drawable.ic_view_close)
    }

    private fun setUpTheLoggedInUI() {
        if (isLoggedIn) {
            binding.receiptFooter.buttonGoToAds.setText(R.string.my_ads)
        }
    }

    private fun fillInTheReceipt() {
        with(binding) {
            val res = resources
            val email = res.getString(
                R.string.vas_receipt_email_sent_message,
                extras?.getString(Constants.CREDIT_CARD_EMAIL)
            )
            receiptSentEmailMessage.text = email
            receiptDate.text = DateUtils.getCurrentDateWithFormat(
                format = DateUtils.DATE_FORMAT_YEAR_MONTH_DAY
            )
            receiptReceiverName.text = String.format(
                "%s %s",
                extras?.getString(Constants.CREDIT_CARD_FIRST_NAME),
                extras?.getString(Constants.CREDIT_CARD_LAST_NAME)
            )
            receiptTransactionNumber.text = extras?.getString(Constants.VAS_PAYMENT_KEY)
            receiptPaymentMethod.text = BANK_CARD
            receiptOrderTitle.text = extras?.getString(Constants.VAS_UNIT_TITLE)
            receiptOrderDuration.text = extras?.getString(Constants.VAS_UNIT_DURATION)
            val price = extras?.getString(Constants.VAS_UNIT_NORMAL_PRICE)
            receiptPackagePrice.text = price
            val totalPrice = res.getString(R.string.vas_payment_total, price)
            receiptTotal.text = totalPrice
        }
    }

    override fun restoreInstanceState(savedInstanceState: Bundle?) {}
    override fun onModel() {}
    override fun onOffline() {}

    /**
     * Go to the listing
     *
     * @param view
     */
    fun goToListing(view: View?) {
        startListingActivity()
        if (isLoggedIn) {
            notifyMainActivity()
        }
    }

    private fun notifyMainActivity() {
        EventBusManager.instance?.postSticky(SwitchToTab(BottomNavigationBar.NAVIGATION_POSITION_FAVORITES_STATS))
    }

    /**
     * This function contain the logic behind taking a screenshot
     */
    private fun saveVasCodeAsScreenShot() {
        //Take crash Payment Code
        trackScreenshotCTAAnalytic()
        //parentView
        val rootView = findViewById<View>(R.id.receipt_container)
        //Bitmap
        val screenShotBitmap = ImageUtils.getViewContentAsBitmap(rootView)
        val saveImageAsynchTask = SaveImageInternallyService(this, rootView)
        saveImageAsynchTask.execute(screenShotBitmap)
    }

    /**
     * Take the ScreenShoot
     *
     * @param view
     */
    fun tackScreensShot(view: View?) {
        val perms: Array<String> = arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE)
        if (EasyPermissions.hasPermissions(
                this@ReceiptActivity,
                *perms
            ) || Build.VERSION.SDK_INT >= Build.VERSION_CODES.R
        ) {
            saveVasCodeAsScreenShot()
        } else {
            EasyPermissions.requestPermissions(
                this@ReceiptActivity,
                getString(R.string.common_storage_permissions_required),
                MY_PERMISSIONS_REQUEST_WRITE_EXTERNAL_STORAGE,
                *perms
            )
        }
    }

    /**
     * check if the app have the write permission
     *
     * @return
     */
    val isWritePermissionGranted: Int
        get() = ContextCompat.checkSelfPermission(
            this@ReceiptActivity,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        )

    /**
     * This function's for starting a new listing Activity
     */
    private fun startListingActivity() {
        trackListNavigationAnalytic()
        val listingIntent = Intent(this@ReceiptActivity, MainActivity::class.java)
        startActivity(listingIntent)
        finish()
    }

    /**
     * Function for requesting the write permission
     */
    private fun requestWritePermission() {

        // Here, thisActivity is the current activity
        if (ContextCompat.checkSelfPermission(
                this@ReceiptActivity,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            )
            != PackageManager.PERMISSION_GRANTED
        ) {
            ActivityCompat.requestPermissions(
                this@ReceiptActivity,
                arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE),
                MY_PERMISSIONS_REQUEST_WRITE_EXTERNAL_STORAGE
            )
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        EasyPermissions.onRequestPermissionsResult(
            requestCode,
            permissions,
            grantResults,
            this
        )
    }

    override fun onPermissionsGranted(requestCode: Int, perms: MutableList<String>) {
        when (requestCode) {
            MY_PERMISSIONS_REQUEST_WRITE_EXTERNAL_STORAGE -> saveVasCodeAsScreenShot()
        }
    }

    override fun onPermissionsDenied(requestCode: Int, perms: MutableList<String>) {
        when (requestCode) {
            MY_PERMISSIONS_REQUEST_WRITE_EXTERNAL_STORAGE -> Toast.makeText(
                this@ReceiptActivity, getString(R.string.common_storage_permissions_required),
                Toast.LENGTH_LONG
            ).show()
        }
    }

    override fun getPageName(): String {
        return ""
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        val id = item.itemId
        if (id == android.R.id.home) {
            startListingActivity()
        }
        return super.onOptionsItemSelected(item)
    }

    /**
     * track payment method selection event
     */
    private fun trackListNavigationAnalytic() {
        val manager = AnalyticsManager.instance
        if (manager != null) {
            val properties = bundleOf(
                getString(R.string.am_property_source)
                        to getString(R.string.am_val_cc_payment_receipt)
            )
            manager.logVasEvent(
                this@ReceiptActivity,
                getString(R.string.an_event_listing_page),
                properties
            )
        }
    }

    /**
     * track filled form event
     */
    private fun trackScreenshotCTAAnalytic() {
        val manager = AnalyticsManager.instance
        manager?.logVasEvent(this, getString(R.string.an_event_receipt_screenshot_cta))
    }

    companion object {
        const val BANK_CARD = "Carte Bancaire"
        const val MY_PERMISSIONS_REQUEST_WRITE_EXTERNAL_STORAGE = 19
    }
}