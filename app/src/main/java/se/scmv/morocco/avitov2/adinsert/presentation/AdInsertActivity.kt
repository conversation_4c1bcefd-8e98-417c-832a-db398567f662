package se.scmv.morocco.avitov2.adinsert.presentation

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.consumeWindowInsets
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.navigation.compose.rememberNavController
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.collectLatest
import se.scmv.morocco.account.presentation.myads.AccountAdsActivity
import se.scmv.morocco.ad.navigation.AdNavHost
import se.scmv.morocco.analytics.AnalyticsHelper
import se.scmv.morocco.analytics.LocalAnalyticsHelper
import se.scmv.morocco.avitov2.vas.presentation.master.NewVasActivity
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.ui.SnackBarHostForSnackBarController
import javax.inject.Inject

@AndroidEntryPoint
class AdInsertActivity : ComponentActivity() {

    companion object {
        const val AD_ID_KEY: String = "se.scmv.morocco.avitov2.insert.AD_ID_KEY"
        const val GO_IMAGE_STEP_KEY: String = "se.scmv.morocco.avitov2.insert.GO_IMAGE_STEP_KEY"
    }

    @Inject
    lateinit var accountRepository: AccountRepository

    @Inject
    lateinit var analyticsHelper: AnalyticsHelper

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val adId: String? = intent.getStringExtra(AD_ID_KEY)
        val goToImageStep: Boolean = intent.getBooleanExtra(GO_IMAGE_STEP_KEY, false)

        enableEdgeToEdge()
        setContent {
            CompositionLocalProvider(LocalAnalyticsHelper provides analyticsHelper) {
                AvitoTheme {
                    val accountState = remember { mutableStateOf<Account?>(null) }
                    when (val account = accountState.value) {
                        is Account.Connected -> Scaffold(
                            snackbarHost = { SnackBarHostForSnackBarController() }
                        ) { paddingValues ->
                            val navController = rememberNavController()
                            AdNavHost(
                                modifier = Modifier
                                    .safeDrawingPadding()
                                    .consumeWindowInsets(paddingValues),
                                account = account,
                                adId = adId,
                                goToImageStep = goToImageStep,
                                navController = navController,
                                navigateBack = { finish() },
                                navigateToVas = {
                                    NewVasActivity.open(context = this@AdInsertActivity, params = it)
                                    finish()
                                },
                                navigateToAccountAds = {
                                    startActivity(Intent(this, AccountAdsActivity::class.java))
                                    finish()
                                }
                            )
                        }

                        else -> {}
                    }
                    LaunchedEffect(Unit) {
                        accountRepository.currentAccount.collectLatest {
                            accountState.value = it
                        }
                    }
                }
            }
        }
    }
}