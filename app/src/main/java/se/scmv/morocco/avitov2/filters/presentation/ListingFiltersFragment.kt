package se.scmv.morocco.avitov2.filters.presentation

import android.content.Intent
import android.content.res.ColorStateList
import android.os.Bundle
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.google.android.material.chip.Chip
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import ma.avito.orion.ui.OrionField
import ma.avito.orion.ui.accordion.model.ExpandableItem
import ma.avito.orion.ui.accordion.model.OrionAccordionAnalyticalLabel
import ma.avito.orion.ui.accordion.model.OrionExpandableInValue
import ma.avito.orion.ui.accordion.model.OrionExpandableSection
import ma.avito.orion.ui.accordion.view.IAccordion
import ma.avito.orion.ui.input.dropdown.OrionSmartDropdown
import ma.avito.orion.ui.input.toggle.OrionToggle
import se.scmv.morocco.R
import se.scmv.morocco.authentication.presentation.AuthenticationActivity
import se.scmv.morocco.avitov2.adinsert.presentation.builders.FieldUIType
import se.scmv.morocco.avitov2.adinsert.presentation.utils.InsertUIUtils
import se.scmv.morocco.avitov2.core.presentation.showSnackBar
import se.scmv.morocco.avitov2.filters.presentation.ListingFiltersSharedViewModel.FiltersEvents.FiltersChanged
import se.scmv.morocco.avitov2.filters.presentation.ListingFiltersSharedViewModel.FiltersEvents.LoadFilters
import se.scmv.morocco.avitov2.filters.presentation.ListingFiltersSharedViewModel.FiltersEvents.OnListingCategorySelected
import se.scmv.morocco.avitov2.filters.presentation.ListingFiltersSharedViewModel.FiltersEvents.OpenCityDropDown
import se.scmv.morocco.avitov2.filters.presentation.ListingFiltersSharedViewModel.FiltersEvents.SaveSearchAfterSignIn
import se.scmv.morocco.avitov2.filters.presentation.ListingFiltersSharedViewModel.FiltersEvents.SearchSuggestionSelected
import se.scmv.morocco.avitov2.filters.presentation.ListingFiltersViewModel.Companion.DEFAULT_CATEGORY_ID
import se.scmv.morocco.avitov2.filters.presentation.ListingFiltersViewModel.Companion.DEFAULT_CATEGORY_TYPE
import se.scmv.morocco.avitov2.filters.presentation.orion.collectValue
import se.scmv.morocco.avitov2.filters.presentation.orion.hasValue
import se.scmv.morocco.avitov2.filters.presentation.orion.viewTypeToOrionComponent
import se.scmv.morocco.data.rest.config.dtos.ListingCategoryFiltersDto
import se.scmv.morocco.databinding.FragmentListingFiltersBinding
import se.scmv.morocco.domain.models.Category
import se.scmv.morocco.login.models.AccountToken
import se.scmv.morocco.utils.isNotEmpty
import se.scmv.morocco.utils.isNotNull

@AndroidEntryPoint
class ListingFiltersFragment : Fragment() {

    companion object {
        private const val KEYWORD_FILTER_KEY = "keyword"
        private const val CITY_FILTER_KEY = "city"
        private const val AREA_FILTER_KEY = "area"
        private const val CATEGORY_FILTER_KEY = "category"
        private const val AD_TYPE_FILTER_KEY = "adType"
        private const val OFFERS_SHIPPING_KEY = "delivery"
    }

    private var _binding: FragmentListingFiltersBinding? = null
    private val binding: FragmentListingFiltersBinding
        get() = _binding!!

    private val listingFiltersViewModel: ListingFiltersViewModel by viewModels()
    private val filtersSharedViewModel: ListingFiltersSharedViewModel by activityViewModels()

    // PRIVATE MEMBERS
    private var accordingExpandableItems = emptyList<ExpandableItem>()
    private var orionViewListFilters: ArrayList<Pair<String, OrionField>> = ArrayList()
    private var suggestionSelected: String = ""
    private var savedSelectedFilters: Map<String, Pair<Any, String>> = mapOf()
    private var savedSelectedFiltersHash = -1
    private var selectedCitiesAndAreasIds: Pair<String, String>? = null
    private var filtersWithDependencies: List<ListingCategoryFiltersDto.BaseFilters>? = null


    // PUBLIC MEMBERS
    var retryDisplayed = false
    private val currentChips = mutableMapOf<String, Chip>()
    private val currentFiltersLabels = mutableMapOf<String, String>()

    override fun onCreateView(
        inflater: LayoutInflater,
        parent: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentListingFiltersBinding.inflate(inflater, parent, false)
        return _binding?.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initCategoriesSpinner()
        initButtonsListeners()
        initSaveSearchVisibility()
        renderCategories()
        renderFilters()
        renderUiState()
        renderSavedSearch()
        renderSharedEvents()
        renderOneTimeEvents()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    private fun initCategoriesSpinner() {
        with(binding.oaCategories) {
            setTitle(getString(R.string.categoryLabel))
            setSelectionListener(
                object : IAccordion.ItemListener {
                    override fun onItemSelected(
                        item: OrionExpandableInValue,
                        adTypeIndex: Int?
                    ) {
                        val id = item.item.key.toInt()
                        val type = adTypeIndex?.let { item.item.adTypes?.get(it)?.key }
                            ?: item.item.adTypes?.firstOrNull()?.key
                        onCategoryChanged(id, type)
                    }

                    override fun onNothingSelected() {}
                }
            )
        }
    }

    private fun onCategoryChanged(id: Int, type: String?) {
        savedSelectedFilters = emptyMap()
        currentChips.clear()
        listingFiltersViewModel.onCategorySelected(id, type ?: "")
        applyCategoryFilterChanges()
    }

    private fun initButtonsListeners() {
        with(binding) {
            btnApply.setOnClickListener {
                listingFiltersViewModel.onFilterSelected(collectSelectedFilters())
                filtersSharedViewModel.onFiltersChanged(listingFiltersViewModel.buildGetListingQueryParams())
                requireActivity().onBackPressed()
            }
            btnRetry.setOnClickListener { listingFiltersViewModel.onRetry() }
            btnBack.setOnClickListener { requireActivity().onBackPressed() }
            btnSaveSearch.setOnClickListener {
                if (btnSaveSearch.isChecked) {
                    if (AccountToken.isLoggedIn(context) && !AccountToken.isSessionExpired(context)) {
                        listingFiltersViewModel.onSaveSearchChecked(
                            selectedFilters = generateSaveSearchFilters()
                        )
                    } else {
                        btnSaveSearch.isChecked = false
                        startLogin()
                    }
                } else {
                    listingFiltersViewModel.onSaveSearchUnchecked()
                }
            }
            btnDeleteSearch.setOnClickListener {
                currentFiltersLabels[KEYWORD_FILTER_KEY] = ""
                currentChips.clear()
                selectedCitiesAndAreasIds = null
                onCategorySelected(DEFAULT_CATEGORY_ID, DEFAULT_CATEGORY_TYPE)
                loadFilters()
            }
        }
    }

    private fun initSaveSearchVisibility() {
        if (AccountToken.isLoggedIn(context) && AccountToken.getCurrentToken(context).isStore) {
            binding.savedText.visibility = View.INVISIBLE
            binding.btnSaveSearch.visibility = View.INVISIBLE
        }
    }

    private fun renderCategories() {
        repeatWithStartedLifeCycle {
            listingFiltersViewModel.categories
                .collectLatest {
                    accordingExpandableItems = it
                    orionViewListFilters.clear()
                    orionViewListFilters.add(
                        Pair(
                            FieldUIType.SINGLE_SELECT_CATEGORY_DROPDOWN_SEARCH.id,
                            binding.oaCategories as OrionField
                        )
                    )
                    with(binding.oaCategories) {
                        setData(
                            OrionExpandableSection(
                                key = CATEGORY_FILTER_KEY,
                                isRequired = false,
                                title = getString(R.string.categoryLabel),
                                message = getString(R.string.categoryLabel),
                                analyticalLabel = OrionAccordionAnalyticalLabel(CATEGORY_FILTER_KEY),
                                values = it
                            )
                        )
                        it.firstOrNull()?.let { setValue(OrionExpandableInValue(it, 0)) }
                    }
                }
        }
    }

    private fun renderFilters() {
        repeatWithStartedLifeCycle {
            listingFiltersViewModel.uiState
                .map { it.filters }
                .distinctUntilChanged()
                .collectLatest { filters: List<ListingCategoryFiltersDto.BaseFilters> ->
                    with(binding) {
                        binding.llKeywordContainer.removeAllViews()
                        currentFiltersLabels[KEYWORD_FILTER_KEY]?.let { query ->
                            if (query.isNotEmpty()) {
                                val newChip = createOrReplaceChip(query, KEYWORD_FILTER_KEY)
                                binding.llKeywordContainer.addView(newChip)
                                currentChips[KEYWORD_FILTER_KEY] = newChip
                            }
                        }

                        rvFilters.removeAllViews()
                        orionViewListFilters =
                            orionViewListFilters.filter { it.first == FieldUIType.SINGLE_SELECT_CATEGORY_DROPDOWN_SEARCH.id }
                                .toCollection(ArrayList())

                        var pairCounter = 0
                        val totalViews = filters.filter { it.type.isNotNull() }.size

                        filtersWithDependencies =
                            filters.filter { it.dependencies != null && it.dependencies.isNotEmpty() }

                        filters.filter { it.type.isNotNull() }
                            .map {
                                currentFiltersLabels[it.id] = it.name.toString()
                                val view = it.type!!.viewTypeToOrionComponent(
                                    context = requireContext(),
                                    baseFilters = it,
                                    setValue = if (savedSelectedFilters.isNotEmpty() && savedSelectedFilters[it.childParam?.id].isNotNull()) {
                                        Pair(
                                            savedSelectedFilters[it.id]?.first,
                                            savedSelectedFilters[it.childParam?.id]?.first
                                        )
                                    } else if (savedSelectedFilters.isNotEmpty()) {
                                        savedSelectedFilters[it.id]?.first
                                    } else if (it.id == CITY_FILTER_KEY && selectedCitiesAndAreasIds != null) {
                                        Pair(
                                            first = selectedCitiesAndAreasIds!!.first,
                                            second = selectedCitiesAndAreasIds!!.second
                                        )
                                    } else null,
                                    onChanged = { onFilterSelected() }
                                )
                                val priceSortingView = createPriceSortingView(it)
                                rvFilters.addView(view)
                                if (view != null) {
                                    if (it.dependencies != null && it.dependencies.isNotEmpty()) {
                                        if (selectedCitiesAndAreasIds != null)
                                            view.visibility = View.VISIBLE
                                        else
                                            view.visibility = View.GONE
                                    }
                                }
                                if (priceSortingView.isNotNull())
                                    rvFilters.addView(priceSortingView)
                                val pair = Pair(it.type.orEmpty(), view as OrionField)
                                orionViewListFilters.add(pair)
                                if (priceSortingView.isNotNull()) {
                                    val pair2 = Pair(
                                        FieldUIType.SINGLE_SELECT_EXTENDED.id,
                                        priceSortingView as OrionField
                                    )
                                    orionViewListFilters.add(pair2)
                                }
                            }

                        // Post X() to the message queue to run after all views are added
                        rvFilters.post {
                            val selectedFilters = collectSelectedFilters()
                            savedSelectedFiltersHash = selectedFilters.hashCode()
                            listingFiltersViewModel.onFilterSelected(
                                selectedFilters
                            )
                        }
                    }
                }
        }
    }

    private fun onFilterSelected() {
        val selectedFilters = collectSelectedFilters().also {
            val citiesIds = it[CITY_FILTER_KEY]?.first as? String? ?: ""
            val areasIds = it[AREA_FILTER_KEY]?.first as? String? ?: ""
            selectedCitiesAndAreasIds = Pair(citiesIds, areasIds)
        }
        listingFiltersViewModel.onFilterSelected(selectedFilters)

        // Perform UI update on the main thread
        lifecycleScope.launch(Dispatchers.Main) {
            val existingKeys = currentChips.keys.toMutableSet()
            val excludedKeys = setOf(
                CATEGORY_FILTER_KEY,
                "adType",
                "area",
                "cityLabel",
                "areaLabel",
                "model",
                "phone_model",
                "AdSortProperty"
            )

            selectedFilters.forEach { (key, value) ->
                val isValidValue = value.first.toString()
                    .isNotEmpty() && !(value.first is Boolean && !(value.first as Boolean))

                if (key !in excludedKeys && isValidValue) {
                    if (currentChips.containsKey(key)) {
                        // Update existing chip
                        currentChips[key]?.apply {
                            text = currentFiltersLabels[key]
                            existingKeys.remove(key)
                        }
                    } else {
                        // Create and add new chip
                        with(binding.llKeywordContainer) {
                            val newChip = createOrReplaceChip(
                                text = currentFiltersLabels[key].toString(),
                                tag = key
                            )
                            addView(newChip)
                            currentChips[key] = newChip
                        }

                    }
                }
            }

            filtersWithDependencies?.forEach { filtersWithDep ->
                val dependencies = filtersWithDep.dependencies ?: return@forEach
                val visibility = dependencies.any { dependency ->
                    // TODO check condition.
                    orionViewListFilters.firstOrNull {
                        dependency.dependsOn == it.second.getKey()
                    }?.second?.getValue()?.hasValue() ?: false
                }

                val filteredView = orionViewListFilters
                    .firstOrNull { it.second.getKey() == filtersWithDep.id }
                    ?.second
                if (filteredView != null && filteredView is View) {
                    filteredView.isVisible = visibility
                }
            }

            // Remove chips that are no longer needed
            existingKeys.forEach { keyToRemove ->
                currentChips[keyToRemove]?.let { chipToRemove ->
                    binding.llKeywordContainer.removeView(chipToRemove)
                    currentChips.remove(keyToRemove)
                }
            }
        }
    }

    private fun renderUiState() {
        repeatWithStartedLifeCycle {
            listingFiltersViewModel.uiState.collectLatest { state ->
                with(state) {
                    with(binding) {
                        val isFiltersEmpty = filters.isEmpty()
                        filtersContainer.visibility = if (isLoading.not() && isFiltersEmpty.not()) {
                            View.VISIBLE
                        } else View.GONE
                        layoutProgress.visibility = if (isLoading) View.VISIBLE else View.GONE
                        layoutEmpty.visibility =
                            if (isLoading.not() && showRetry.not() && isFiltersEmpty) {
                                View.VISIBLE
                            } else View.GONE
                        layoutRetry.visibility = if (showRetry) View.VISIBLE else View.GONE
                        retryDisplayed = showRetry
                        btnApply.text = adsCount?.let {
                            getString(R.string.listing_filters_count, "$adsCount")
                        } ?: getString(R.string.common_show)
                    }
                }
            }
        }
    }

    fun repeatWithStartedLifeCycle(block: suspend () -> Unit) {
        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                block()
            }
        }
    }

    private fun renderSavedSearch() {
        lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                listingFiltersViewModel.savedSearch.collect {
                    binding.btnSaveSearch.isChecked = it != null
                }
            }
        }
    }

    private fun renderSharedEvents() {
        repeatWithStartedLifeCycle {
            filtersSharedViewModel.filtersEvents.collectLatest {
                when (it) {
                    is FiltersChanged -> onFiltersChanged(it)

                    is SearchSuggestionSelected -> onCategorySelected(it.id, it.type, it.suggestion)

                    SaveSearchAfterSignIn -> saveSearchAfterSignIn()

                    LoadFilters -> loadFilters()

                    ListingFiltersSharedViewModel.FiltersEvents.LoadCounter -> loadCounter()

                    OpenCityDropDown -> openCityDropDown()

                    is OnListingCategorySelected -> onListingCategorySelected(it.category)
                }
            }
        }
        repeatWithStartedLifeCycle {
            with(filtersSharedViewModel) {
                filtersSharedViewModel.deepLinkFiltersEvents.collectLatest { filters ->
                    filters?.let {
                        applyFiltersFromSaveSearchOrDeeplink(it)
                        onApplyFiltersFromSaveSearchOrDeeplink(null)
                    }
                }
            }
        }
    }

    private fun onFiltersChanged(newFilters: FiltersChanged) {
        if (newFilters.filters.getListingQueryParams.offersShipping == false) {
            orionViewListFilters.find {
                it.second is OrionToggle && it.second.getKey() == OFFERS_SHIPPING_KEY
            }?.let {
                (it.second as OrionToggle).setChecked(false)
            }
        }
        if (!newFilters.filters.getListingQueryParams.text.isNullOrEmpty()) {
            val newChip = createOrReplaceChip(
                newFilters.filters.getListingQueryParams.text,
                KEYWORD_FILTER_KEY
            )
            binding.llKeywordContainer.addView(newChip)
            currentChips[KEYWORD_FILTER_KEY] = newChip
            currentFiltersLabels[KEYWORD_FILTER_KEY] = newFilters.filters.getListingQueryParams.text
        } else {
            val chipToRemove = currentChips[KEYWORD_FILTER_KEY]
            if (chipToRemove != null) {
                binding.llKeywordContainer.removeView(chipToRemove)
                currentChips.remove(KEYWORD_FILTER_KEY)
                currentFiltersLabels[KEYWORD_FILTER_KEY] = ""
            }
        }
    }

    private fun onCategorySelected(id: Int, type: String? = "", suggestion: String = "") {
        InsertUIUtils.findExpandableItemWithId(
            categoryListExp = accordingExpandableItems,
            category = "$id",
            adType = type ?: ""
        )?.let { expandableItem: ExpandableItem ->
            val selectedType = if (type in expandableItem.adTypes.orEmpty().map { it.key }) {
                type ?: expandableItem.adTypes?.firstOrNull()?.key ?: ""
            } else {
                expandableItem.adTypes?.firstOrNull()?.key ?: ""
            }
            val selectedTypeIndex =
                expandableItem.adTypes?.indexOfFirst { it.key == selectedType } ?: 0
            binding.oaCategories.setValue(OrionExpandableInValue(expandableItem, selectedTypeIndex))
            if (expandableItem.key == DEFAULT_CATEGORY_ID.toString()) {
                binding.oaCategories.resetDialog()
            }
            savedSelectedFilters = mapOf()
            savedSelectedFiltersHash = -1
            listingFiltersViewModel.setCategory(
                id = expandableItem.key.toInt(),
                type = selectedType
            )
            suggestionSelected = suggestion
            if (suggestion.isNotEmpty()) {
                val newChip = createOrReplaceChip(suggestion, KEYWORD_FILTER_KEY)
                binding.llKeywordContainer.addView(newChip)
                currentChips[KEYWORD_FILTER_KEY] = newChip
            }
        }
    }

    private fun saveSearchAfterSignIn() {
        listingFiltersViewModel.onSaveSearchChecked(generateSaveSearchFilters())
    }

    private fun loadFilters() {
        applyCategoryFilterChanges()
        listingFiltersViewModel.loadFilters()
    }

    private fun loadCounter() {
        listingFiltersViewModel.onFilterSelected(collectSelectedFilters())
    }

    private fun applyCategoryFilterChanges() {
        binding.btnSaveSearch.isChecked = false
        orionViewListFilters.clear()
        orionViewListFilters.add(
            Pair(
                FieldUIType.SINGLE_SELECT_CATEGORY_DROPDOWN_SEARCH.id,
                binding.oaCategories
            )
        )
        listingFiltersViewModel.onFilterSelected(collectSelectedFilters())
    }

    private fun openCityDropDown() {
        orionViewListFilters.firstOrNull { it.second.getKey() == CITY_FILTER_KEY }?.let {
            (it.second as OrionSmartDropdown).openDialog()
        }
    }

    private fun onListingCategorySelected(category: Category) {
        // To review with @Zouhir : I deleted this to have smooth transition from category selected to filters
//        InsertUIUtils.findExpandableItem(
//            categoryListExp = accordingExpandableItems,
//            category = category.id,
//            adTypeKey = category.type
//        )?.let { itemAndTypeIndex ->
//            binding.oaCategories.setValue(
//                OrionExpandableInValue(
//                    item = itemAndTypeIndex.first,
//                    typeIndex = itemAndTypeIndex.second
//                )
//            )
//        }
//        onCategoryChanged(id = category.id.toInt(), type = category.type)
    }

    private fun renderOneTimeEvents() {
        repeatWithStartedLifeCycle {
            listingFiltersViewModel.oneTimeEvents.collectLatest {
                when (it) {
                    is ListingFiltersOneTimeEvents.ShowSnackBar -> showSnackBar(
                        type = it.type,
                        message = it.message,
                        view = binding.root
                    )

                    is ListingFiltersOneTimeEvents.ShowHideSearchProgress -> binding.saveProgressBar.isVisible =
                        it.visible
                }
            }
        }
    }

    private fun createPriceSortingView(baseFilter: ListingCategoryFiltersDto.BaseFilters): View? {
        return if (baseFilter.id == "price") {
            FieldUIType.SINGLE_SELECT_EXTENDED.id.viewTypeToOrionComponent(
                context = requireContext(),
                baseFilters = createPriceSortingFilter(),
                setValue = if (savedSelectedFilters.isNotEmpty()) {
                    savedSelectedFilters["AdSortProperty"]?.first
                } else {
                    null
                }
            ) {
                val selectedFilters = collectSelectedFilters()
                listingFiltersViewModel.onFilterSelected(selectedFilters)
            }
        } else {
            null
        }
    }

    private fun createPriceSortingFilter(): ListingCategoryFiltersDto.BaseFilters {
        return ListingCategoryFiltersDto.BaseFilters(
            icon = null,
            id = "AdSortProperty",
            isParam = true,
            filterItems = listOf(
                ListingCategoryFiltersDto.FilterItems(
                    key = "Non",
                    name = null,
                    label = getString(R.string.non),
                    short = null,
                    trackingName = null,
                    children = null
                ),
                ListingCategoryFiltersDto.FilterItems(
                    key = "DESC",
                    name = null,
                    label = getString(R.string.more_expensive),
                    short = null,
                    trackingName = null,
                    children = null
                ),
                ListingCategoryFiltersDto.FilterItems(
                    key = "ASC",
                    name = null,
                    label = getString(R.string.less_expensive),
                    short = null,
                    trackingName = null,
                    children = null
                )
            ),
            null,
            null,
            null,
            getString(R.string.sort_by_price),
            FieldUIType.SINGLE_SELECT_EXTENDED.id,
            null,
            null,
            null
        )
    }

    private fun createOrReplaceChip(text: String, tag: String): Chip {
        // Find existing chip with the same tag and remove it
        for (i in 0 until binding.llKeywordContainer.childCount) {
            val child = binding.llKeywordContainer.getChildAt(i)
            if (child is Chip && child.tag == tag) {
                binding.llKeywordContainer.removeView(child)
                break
            }
        }

        // Create the new chip
        val chip = Chip(context).apply {
            this.text = text
            this.tag = tag
            setTextSize(TypedValue.COMPLEX_UNIT_SP, 14f)
            isCloseIconVisible = true
            setEnsureMinTouchTargetSize(false)

            setChipBackgroundColorResource(R.color.lavender)
            setChipStrokeColorResource(R.color.avitoBlue2)
            chipStrokeWidth = 1f
            setTextColor(ContextCompat.getColor(context, R.color.avitoBlue2))
            closeIcon = ContextCompat.getDrawable(context, R.drawable.ic_clear)
            closeIconTint =
                ColorStateList.valueOf(ContextCompat.getColor(context, R.color.avitoBlue2))

            if (tag == KEYWORD_FILTER_KEY) {
                currentFiltersLabels[KEYWORD_FILTER_KEY] = text
            }

            setOnCloseIconClickListener {

                if (tag != KEYWORD_FILTER_KEY) {
                    val filteredList: List<Pair<String, OrionField>> =
                        orionViewListFilters.filter { it.second.getKey() == tag }
                    filteredList.firstOrNull()?.second?.reset()
                } else {
                    currentFiltersLabels[KEYWORD_FILTER_KEY] = ""
                    val selectedFilters: Map<String, Pair<Any, String>> = collectSelectedFilters()
                    listingFiltersViewModel.onFilterSelected(selectedFilters)

                    // Perform UI update on the main thread
                    lifecycleScope.launch(Dispatchers.Main) {
                        val chipToRemove = currentChips[tag]

                        if (chipToRemove != null) {
                            binding.llKeywordContainer.removeView(chipToRemove)
                            currentChips.remove(tag)
                        }
                    }
                }
            }

            setPadding(26, 8, 0, 8)
        }

        // Set layout parameters with margins
        val layoutParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.WRAP_CONTENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        ).apply {
            setMargins(8, 8, 8, 8)
        }
        chip.layoutParams = layoutParams

        return chip
    }

    private fun applyFiltersFromSaveSearchOrDeeplink(sFilters: Map<String, Pair<Any, String>>) {
        savedSelectedFilters = sFilters
        suggestionSelected = (savedSelectedFilters[KEYWORD_FILTER_KEY]?.first ?: "").toString()
        val id = savedSelectedFilters[CATEGORY_FILTER_KEY]?.first as? String? ?: "0"
        val type = savedSelectedFilters[AD_TYPE_FILTER_KEY]?.first as? String? ?: ""

        InsertUIUtils.findExpandableItem(
            categoryListExp = accordingExpandableItems,
            category = id,
            adTypeKey = type
        )?.let { itemAndTypeIndex ->
            binding.oaCategories.setValue(
                OrionExpandableInValue(
                    itemAndTypeIndex.first,
                    itemAndTypeIndex.second
                )
            )
            listingFiltersViewModel.setCategory(id.toInt(), type)
        }
//        filtersSharedViewModel.onFiltersChanged(
//            listingFiltersViewModel.buildGetListingQueryParams(savedSelectedFilters)
//        )
    }

    private fun collectSelectedFilters(): Map<String, Pair<Any, String>> {
        val result = mutableMapOf<String, Pair<Any, String>>()
        result[CATEGORY_FILTER_KEY] = Pair(
            listingFiltersViewModel.selectedCategory.first.toString(),
            FieldUIType.SINGLE_SELECT_CATEGORY_DROPDOWN_SEARCH.id
        )
        result[AD_TYPE_FILTER_KEY] = Pair(
            listingFiltersViewModel.selectedCategory.second,
            FieldUIType.SINGLE_SELECT_CATEGORY_DROPDOWN_SEARCH.id
        )
        currentFiltersLabels[KEYWORD_FILTER_KEY]?.let {
            result[KEYWORD_FILTER_KEY] = Pair(it, FieldUIType.TEXT_FIELD.id)
        }
        orionViewListFilters.forEach { pair: Pair<String, OrionField> ->
            result += pair.collectValue()
        }
        return result
    }

    private fun generateSaveSearchFilters(): Map<String, Pair<Any, String>> {
        val result = mutableMapOf<String, Pair<Any, String>>()
        currentFiltersLabels[KEYWORD_FILTER_KEY]?.let {
            result[KEYWORD_FILTER_KEY] = Pair(it, FieldUIType.TEXT_FIELD.id)
        }
        orionViewListFilters.forEach {
            result += it.collectValue()
        }
        return result
    }

    private fun startLogin() {
        val intent = Intent(context, AuthenticationActivity::class.java)
        val requestCode = AuthenticationActivity.REQUEST_SIGN_IN_FILTERS
        requireActivity().startActivityForResult(intent, requestCode)
    }
}