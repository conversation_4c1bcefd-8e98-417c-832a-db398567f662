package se.scmv.morocco.avitov2.core.presentation

import android.graphics.drawable.Drawable
import android.view.LayoutInflater
import android.view.View
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.google.android.flexbox.FlexboxLayout
import se.scmv.morocco.databinding.ItemPublishedAdParamBinding
import se.scmv.morocco.domain.models.AdParam

fun FlexboxLayout.setAdParams(params: List<AdParam>) {
    if (params.isEmpty()) {
        visibility = View.GONE
        return
    } else {
        visibility = View.VISIBLE
        removeAllViews()
    }
    params.forEach { param ->
        ItemPublishedAdParamBinding.inflate(
            LayoutInflater.from(context),
            this,
            false
        ).apply {
            tvAdName.text = param.value
            Glide.with(root.context)
                .load(param.iconUrl)
                .listener(object : RequestListener<Drawable?> {
                    override fun onLoadFailed(
                        e: GlideException?,
                        model: Any?,
                        target: Target<Drawable?>?,
                        isFirstResource: Boolean
                    ): Boolean {

                        return false
                    }

                    override fun onResourceReady(
                        resource: Drawable?,
                        model: Any?,
                        target: Target<Drawable?>?,
                        dataSource: DataSource?,
                        isFirstResource: Boolean
                    ): Boolean {
                        addView(root)
                        return false
                    }
                })
                .into(ivAdImage)
        }
    }
}