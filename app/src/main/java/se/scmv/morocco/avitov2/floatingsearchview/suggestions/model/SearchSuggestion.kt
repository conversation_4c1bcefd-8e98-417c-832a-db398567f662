package se.scmv.morocco.avitov2.floatingsearchview.suggestions.model

import android.content.Context
import android.os.Parcelable
import se.scmv.morocco.R

interface SearchSuggestion : Parcelable {
    val suggestion: String
    val keyword: String
    val categoryName: String
    val id: Int
    val adTypeKey: String
    val cityId: Int
    val cityName: String
    val adTypeName: String
    val modelKey: String
    val model: String
    val modelName: String
    val brandKey: String
    val brand: String
    val brandName: String
    val isHistory: Boolean

    fun getBody(context: Context): String {

        val builder = StringBuilder()
        if (keyword.isNotEmpty()) {
            builder.append("${this.keyword} ")
        }

        if (brandName.isNotEmpty()) {
            builder.append("${this.brandName?.capitalize()} ")
        }
        if (modelName.isNotEmpty()) {
            builder.append(" (${this.modelName?.capitalize()}) ")
        }
        if (categoryName.isNotEmpty() && keyword.isEmpty()) {
            builder.append("${this.categoryName} ")
        } else if (categoryName.isNotEmpty() && (keyword.isNotEmpty() || brandName.isNotEmpty() || modelName.isNotEmpty())) {
            builder.append("${context.getString(R.string.dans)} ${this.categoryName} ")
        } else if (categoryName.isNotEmpty()){
            builder.append("${context.getString(R.string.dans)} ${this.categoryName} ")
        }
        if (adTypeName.isNotEmpty()) {
            builder.append(" ${this.adTypeName} ")
        }
        if (cityName.isNotEmpty()) {
            builder.append("${context.getString(R.string.a)} ${cityName?.capitalize()} ")
        }


        return builder.toString()
    }

    fun getSavedSearchQuery(): String {
        val queryParams = mutableListOf<String>()

        queryParams.add("category=$id")
        queryParams.add("adType=$adTypeKey")
        queryParams.add("city=$cityId")
        queryParams.add("keyword=$suggestion")
        queryParams.add("$modelKey=$model")
        queryParams.add("$brandKey=$brand")

        return queryParams.joinToString("&") { it.replace(" ", "%20") }
    }

}