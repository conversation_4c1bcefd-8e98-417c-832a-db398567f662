package se.scmv.morocco.avitov2.adinsert.presentation.builders

enum class FieldUIType(val id: String) {
    SINGLE_SELECT_CATEGORY_DROPDOWN_SEARCH("single_select_category_dropdown_search"),
    SINGLE_SELECT_SMART_DROPDOWN("single_select_smart_dropdown"),
    MU<PERSON>IPLE_SELECT_SMART_DROPDOWN("multiple_select_smart_dropdown"),
    MULTIPLE_SELECT_SMART_DROPDOWN_ICON("multiple_select_smart_dropdown_icon"),
    MULTISELECT_EXTENDED("multiselect_extended"),
    SINGLE_SELECT_SMART_DROPDOWN_ICON("single_select_smart_dropdown_icon"),
    SINGLE_SELECT_EXTENDED("single_select_extended"),
    MEASURE_TEXT_FIELD("measure_text_field"),
    TEXT_FIELD("text_field"),
    TOGGLE_FIELD_FILTER("toggle_field_filter"),
    MIN_MAX_FIELD("min_max_field"),
    <PERSON><PERSON><PERSON>("slider")
}