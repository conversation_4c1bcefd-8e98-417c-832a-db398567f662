package se.scmv.morocco.avitov2.floatingsearchview.util.adapter

import android.view.GestureDetector
import android.view.MotionEvent

abstract class GestureDetectorListenerAdapter : GestureDetector.OnGestureListener {
        override fun onDown(e: MotionEvent): <PERSON><PERSON><PERSON> {
                return false
        }

        override fun onShowPress(e: MotionEvent) {}
        override fun onSingleTapUp(e: MotionEvent): <PERSON><PERSON>an {
                return false
        }

        override fun onScroll(
                e1: MotionEvent?,
                e2: MotionEvent,
                distanceX: Float,
                distanceY: Float
        ): <PERSON><PERSON><PERSON> {
                return false
        }

        override fun onLongPress(e: MotionEvent) {}
        override fun onFling(
                e1: MotionEvent?,
                e2: MotionEvent,
                velocityX: Float,
                velocityY: Float
        ): <PERSON><PERSON><PERSON> {
                return false
        }
}