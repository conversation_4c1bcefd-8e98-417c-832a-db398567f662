package se.scmv.morocco.avitov2.filters.presentation.orion

import android.content.Context
import android.view.View
import androidx.appcompat.widget.LinearLayoutCompat
import ma.avito.orion.ui.OrionInValue
import ma.avito.orion.ui.OrionValueChildren
import ma.avito.orion.ui.dialogs.range.OrionRangeAnalyticalLabel
import ma.avito.orion.ui.dialogs.range.models.OrionRangeSection
import ma.avito.orion.ui.dialogs.range.models.OrionRangeValue
import ma.avito.orion.ui.input.chips.OrionChipsInValue
import ma.avito.orion.ui.input.dropdown.OrionSmartDropdown
import ma.avito.orion.ui.input.dropdown.OrionSmartDropdownInValues
import ma.avito.orion.ui.input.edittext.OrionEditText
import ma.avito.orion.ui.input.edittext.OrionEditTextInValue
import ma.avito.orion.ui.input.extended.OrionExtended
import ma.avito.orion.ui.input.extended.OrionExtendedChipValue
import ma.avito.orion.ui.input.slider.OrionMinMaxInValue
import ma.avito.orion.ui.input.slider.OrionNewRangeSlider
import ma.avito.orion.ui.input.slider.OrionRangeMinMax
import ma.avito.orion.ui.input.slider.OrionSliderInValue
import ma.avito.orion.ui.input.slider.OrionSliderValue
import ma.avito.orion.ui.input.toggle.OrionToggle
import ma.avito.orion.ui.input.toggle.OrionToggleInValue
import se.scmv.morocco.R
import se.scmv.morocco.avitov2.adinsert.presentation.builders.FieldUIType
import se.scmv.morocco.avitov2.adinsert.presentation.utils.InsertUIUtils.Companion.buildIconUrl
import se.scmv.morocco.data.rest.config.dtos.ListingCategoryFiltersDto
import se.scmv.morocco.utils.enums.SortType
import se.scmv.morocco.utils.isNotNull

// Extension function to convert the FieldUIType to the appropriate OrionField type.
fun String.viewTypeToOrionComponent(
    context: Context,
    baseFilters: ListingCategoryFiltersDto.BaseFilters,
    setValue: Any?,
    onChanged: () -> Unit
): View {
    return when (this) {
        FieldUIType.TEXT_FIELD.id -> newOrionTextFieldInstance(
            context = context,
            data = baseFilters,
            setValue = setValue,
            onChanged = onChanged
        )

        FieldUIType.SINGLE_SELECT_EXTENDED.id -> newOrionExtendedInstance(
            context = context,
            data = baseFilters,
            setValue = setValue,
            onChanged = onChanged
        )

        FieldUIType.MIN_MAX_FIELD.id -> newOrionMinMaxInstance(
            context = context,
            data = baseFilters,
            setValue = setValue,
            onChanged = onChanged
        )

        FieldUIType.SLIDER.id -> newOrionSliderInstance(
            context = context,
            data = baseFilters,
            value = setValue,
            onChanged = onChanged
        )

        FieldUIType.MEASURE_TEXT_FIELD.id -> newMeasureTextFieldInstance(
            context = context,
            data = baseFilters,
            setValue = setValue,
            onChanged = onChanged
        )

        FieldUIType.TOGGLE_FIELD_FILTER.id -> newToggleFieldInstance(
            context = context,
            data = baseFilters,
            value = setValue,
            onChanged = onChanged
        )

        FieldUIType.MULTIPLE_SELECT_SMART_DROPDOWN.id -> newMultipleSelectFieldInstance(
            context = context,
            data = baseFilters,
            setValue = setValue,
            onChanged = onChanged
        )

        FieldUIType.MULTIPLE_SELECT_SMART_DROPDOWN_ICON.id -> newMultipleSelectFieldInstance(
            context = context,
            data = baseFilters,
            setValue = setValue,
            onChanged = onChanged
        )

        FieldUIType.MULTISELECT_EXTENDED.id -> newMultipleExtendedFieldInstance(
            context = context,
            data = baseFilters,
            setValue = setValue,
            onChanged = onChanged
        )

        else -> throw IllegalArgumentException("Invalid field type: $this")
    }
}


fun newMultipleSelectFieldInstance(
    context: Context,
    data: ListingCategoryFiltersDto.BaseFilters,
    setValue: Any?,
    onChanged: () -> Unit
): OrionSmartDropdown {

    return OrionSmartDropdown.Builder(context)
        .setTitle(data.name!!)
        .setSheettitle(data.name!!)
        .setSearchQueryHint(context.getString(R.string.search_in, data.name))
        .setData(mapToOrionValueChildrenList(data))
        .setKey(data.id)
        .setKeys(data.id, data.childParam?.id ?: "")
        .setMultiSelect(true)
        .setRequired(false)
        .setIcon(buildIconUrl(data.icon))
        .onValueSelectedListener { onChanged() }
        .build()
        .apply {
            layoutParams = LinearLayoutCompat.LayoutParams(
                LinearLayoutCompat.LayoutParams.MATCH_PARENT,
                LinearLayoutCompat.LayoutParams.WRAP_CONTENT
            ).apply {
                val margin = resources.getDimensionPixelSize(R.dimen.orion_space_20)
                setMargins(margin, 0, margin, 0)
            }
            setValue?.let { value ->
                when (value) {
                    is Pair<*, *> -> {
                        val key = value.first as? String?
                        val v = value.second as? String?
                        if (key != null && v != null) {
                            setValue(OrionSmartDropdownInValues(key,v))
                        }
                    }

                    is String -> {
                        setValue(OrionSmartDropdownInValues(value, ""))
                    }

                    else -> {
                    }
                }
            }

        }
}

fun newMultipleExtendedFieldInstance(
    context: Context,
    data: ListingCategoryFiltersDto.BaseFilters,
    setValue: Any?,
    onChanged: () -> Unit
): OrionExtended {
    val values = java.util.ArrayList<OrionExtendedChipValue>()
    data.fields?.forEach {
        values.add(
            OrionExtendedChipValue(
                it.id,
                it.name
            )
        )
    }
    val selectedValues = java.util.ArrayList<OrionExtendedChipValue>()
    for (value in values) {
        if (setValue.toString().split(",").any { it == value.key }) {
            selectedValues.add(value)
        }
    }
    if (setValue is MutableList<*>) {

    }

    return OrionExtended.Builder(context)
        .setMultiselect(true)
        .setKey(data.id)
        .setTitle(data.name!!)
        .setValues(values)
        .setRequired(false)
        .build()
        .apply {
            setOnValueChangeListener {
                onChanged()
            }
            layoutParams = LinearLayoutCompat.LayoutParams(
                LinearLayoutCompat.LayoutParams.MATCH_PARENT,
                LinearLayoutCompat.LayoutParams.WRAP_CONTENT
            ).apply {
                val margin = resources.getDimensionPixelSize(R.dimen.orion_space_20)
                setMargins(margin, 0, margin, 0)
            }
            setValues(OrionChipsInValue(selectedValues))
        }
}

fun newOrionTextFieldInstance(
    context: Context,
    data: ListingCategoryFiltersDto.BaseFilters,
    setValue: Any?,
    onChanged: () -> Unit
): OrionEditText {
    return OrionEditText.Builder(context)
        .setKey(data.id)
        .setTitle(data.name.orEmpty())
        .addTextChangedCallback { onChanged() }
        .setRequired(true)
        .build()
        .apply {
            data.icon?.let {
                setActionView(
                    OrionEditText.ACTION.ICON
                )
                setUrlStartIcon(
                    buildIconUrl(
                        it
                    )
                )
            }
            setValue?.let { setValue(OrionEditTextInValue(it.toString())) }
        }
}

fun newOrionExtendedInstance(
    context: Context,
    data: ListingCategoryFiltersDto.BaseFilters,
    setValue: Any?,
    onChanged: () -> Unit
): OrionExtended {
    return OrionExtended.Builder(context)
        .setKey(data.id)
        .setIcon(if (data.id != "AdSortProperty") buildIconUrl(data.icon) else R.drawable.sort_icon )
        .setMultiselect(false)
        .setSupportingIcon(data.id == "AdSortProperty")
        .setRequired(false)
        .build()
        .apply {
            setOnValueChangeListener { onChanged() }
            val arrayList = ArrayList<OrionExtendedChipValue>()
            data.filterItems?.forEach {
                arrayList.add(
                    OrionExtendedChipValue(
                        it.key!!,
                        it.label!!,
                        if (it.key == SortType.ASC.value) R.drawable.sort_asc else if (it.key == SortType.DESC.value) R.drawable.sort_des else null
                    )
                )
            }
            setTitle(data.name.toString())
            setData(arrayList)
            setValues(OrionChipsInValue(arrayList.filter { it.key == setValue.toString() } as ArrayList<OrionExtendedChipValue>))
            layoutParams = LinearLayoutCompat.LayoutParams(
                LinearLayoutCompat.LayoutParams.MATCH_PARENT,
                LinearLayoutCompat.LayoutParams.WRAP_CONTENT
            ).apply {
                val margin = resources.getDimensionPixelSize(R.dimen.orion_space_20)
                setMargins(margin, 0, margin, 0)
            }
            drawValues()
            setValues(OrionInValue(arrayList.filter { it.key == setValue.toString() }))
        }
}

fun newOrionMinMaxInstance(
    context: Context,
    data: ListingCategoryFiltersDto.BaseFilters,
    setValue: Any?,
    onChanged: () -> Unit
): OrionRangeMinMax {
    return OrionRangeMinMax.Builder(context)
        .setKey(data.id)
        .setIcon(buildIconUrl(data.icon))
        .setMinHint(data.range?.first().toString())
        .setMaxHint(data.range?.last().toString())
        .build()
        .apply {
            setOnValueChangeListener { onChanged() }
            setDataOnly(
                title = data.name.orEmpty() + " - " + data.suffix.orEmpty(),
                min = data.range?.get(0) ?: 0,
                max = data.range?.get(1) ?: Int.MAX_VALUE,
                key = data.id
            )

            setValue?.let { value ->
                when (value) {
                    is Pair<*, *> -> {
                        val first = value.first as? Int
                        val second = value.second as? Int
                        setValue(OrionMinMaxInValue(first, second))

                    }
                }
            }
        }
}

fun newOrionSliderInstance(
    context: Context,
    data: ListingCategoryFiltersDto.BaseFilters,
    value: Any?,
    onChanged: () -> Unit
): OrionNewRangeSlider {
    return OrionNewRangeSlider.Builder(context)
        .setKey(data.id)
        .setIcon(buildIconUrl(data.icon))
        .setTitle(data.name.orEmpty())
        .setData(
            values = data.filterItems?.mapNotNull { filterItem ->
                filterItem.key?.toIntOrNull()?.let { Pair(it, filterItem.label.toString()) }
            }!!)
        .build()
        .apply {
            setOnValueChangeListener { onChanged() }
            value?.let { value ->
                when (value) {
                    is Pair<*, *> -> {
                        val first = value.first as? Int
                        val second = value.second as? Int
                        if (first.isNotNull() && second.isNotNull())
                            setValues(
                                OrionSliderInValue(
                                    Pair(
                                        OrionSliderValue.InRangeValue(first!!),
                                        OrionSliderValue.InRangeValue(second!!)
                                    )
                                )
                            )
                    }
                }
            }

        }
}

fun newMeasureTextFieldInstance(
    context: Context,
    data: ListingCategoryFiltersDto.BaseFilters,
    setValue: Any?,
    onChanged: () -> Unit
): OrionEditText {
    return OrionEditText.Builder(context)
        .setKey(data.id)
        .setTitle(data.name.orEmpty())
        .build()
        .apply {
            data.icon?.let {
                setActionView(OrionEditText.ACTION.ICON)
                setUrlStartIcon(buildIconUrl(it))
            }
            addOnTextChangedCallbacks { onChanged() }
        }
}

fun newToggleFieldInstance(
    context: Context,
    data: ListingCategoryFiltersDto.BaseFilters,
    value: Any?,
    onChanged: () -> Unit
): OrionToggle {
    return OrionToggle.Builder(context)
        .setKey(data.id)
        .setIcon(buildIconUrl(data.icon))
        .setTitle(data.name.orEmpty())
        .setValue(OrionToggleInValue(data.defaultValue))
        .setOnValueChangeListener { onChanged() }
        .build()
        .apply {
            if (value is Boolean)
                setValue(OrionToggleInValue(value))
        }
}

fun ListingCategoryFiltersDto.BaseFilters.toOrionRangeSection(): OrionRangeSection =
    OrionRangeSection(
        key = id,
        required = false,
        title = name.orEmpty(),
        analyticalLabel = OrionRangeAnalyticalLabel(analyticalLabel = id),
        message = name.orEmpty(),
        autoComplete = true,
        searchHint = name.orEmpty(),
        supportMultipleSelection = true,
        values = filterItems?.map { it.toOrionRangeValue(childParam) }
            ?.toMutableList()
            ?.apply { sortByDescending { it.children?.values?.size ?: 0 } }
    )

fun mapToOrionValueChildrenList(inputData: ListingCategoryFiltersDto.BaseFilters): ArrayList<OrionValueChildren> {
    val values = arrayListOf<OrionValueChildren>()

    inputData.filterItems?.forEach { filterItem ->
        val children = arrayListOf<OrionValueChildren>()

        filterItem.children?.forEach { child ->
            children.add(
                OrionValueChildren(
                    key = child.key.orEmpty(),
                    value = child.label.orEmpty(),
                    arrayListOf()
                )
            )
        }

        values.add(
            OrionValueChildren(
                key = filterItem.key.orEmpty(),
                value = filterItem.label.orEmpty(),
                analyticalValue = filterItem.trackingName,
                children = children
            )
        )
    }

    return ArrayList(values.sortedByDescending { it.children.size })
}


fun ListingCategoryFiltersDto.FilterItems.toOrionRangeValue(childParam: ListingCategoryFiltersDto.ChildParam?): OrionRangeValue =
    OrionRangeValue(
        key = this.key.orEmpty(),
        value = this.label.orEmpty(),
        children = this.children?.toOrionRangeSection(
            childParam?.id.orEmpty(),
            childParam?.name.orEmpty(),
            childParam?.name.orEmpty()
        )
    )

fun List<ListingCategoryFiltersDto.Child>.toOrionRangeSection(
    key: String,
    title: String,
    message: String
): OrionRangeSection = OrionRangeSection(
        key = key,
        required = false,
        title = title,
        analyticalLabel = OrionRangeAnalyticalLabel(title),
        message = message,
        autoComplete = true,
        searchHint = "Tapez $title",
        supportMultipleSelection = true,
        values = null
    )

