package se.scmv.morocco.avitov2.adinsert.presentation.utils

import ma.avito.orion.ui.accordion.model.ExpandableItem
import se.scmv.morocco.data.database.entities.CategoryData
import se.scmv.morocco.data.rest.config.dtos.ListingFilterCategoriesDto

class InsertUIUtils {


    companion object {

        fun mapCategoryDataToExpandableItem(
            categoryList: List<CategoryData>,
            topParentTId: String? = null,
            topParentTrackingName: String? = null
        ): List<ExpandableItem> {
            var expandableItemList = mutableListOf<ExpandableItem>()

            for (categoryData in categoryList) {
                val adTypes: List<ExpandableItem.AdType>? =
                    categoryData.adTypes?.let { types ->
                        (types.indices).map { j ->
                            val typeObject = types[j]
                            ExpandableItem.AdType(
                                typeObject.key,
                                typeObject.name,
                                typeObject.trackingName
                            )
                        }
                    }


                val expandableItem = ExpandableItem(
                    key = categoryData.category.id,
                    title = categoryData.name ?: "",
                    icon = buildIconUrl(categoryData.icon),
                    selectable = categoryData.children.isNullOrEmpty(),
                    adTypes = adTypes,
                    trackingName = categoryData.trackingName,
                    topParentTrackingName = topParentTrackingName ?: categoryData.trackingName,
                    topParentId = topParentTId ?: categoryData.category.id
                )

                if (!categoryData.children.isNullOrEmpty()) {
                    expandableItem.children =
                        mapCategoryDataToExpandableItem(
                            categoryData.children,
                            categoryData.trackingName,
                            categoryData.category.id
                        )
                }
                expandableItemList.add(expandableItem)
            }

            return expandableItemList
        }

        fun mapListingCategoryDataToExpandableItem(
            categoryList: List<ListingFilterCategoriesDto>,
            topParentTId: String? = null,
            topParentTrackingName: String? = null
        ): List<ExpandableItem> {
            var expandableItemList = mutableListOf<ExpandableItem>()
            for (categoryData in categoryList) {
                val adTypes: List<ExpandableItem.AdType> = categoryData.category.let { c ->
                    listOf(ExpandableItem.AdType(c.adType, c.adType, c.adType))
                }


                val expandableItem = ExpandableItem(
                    key = categoryData.category.id,
                    title = categoryData.name.orEmpty(),
                    icon = buildIconUrl(categoryData.icon),
                    selectable = categoryData.children.isNullOrEmpty(),
                    adTypes = adTypes,
                    trackingName = categoryData.trackingName,
                    topParentTrackingName = topParentTrackingName ?: categoryData.trackingName,
                    topParentId = topParentTId ?: categoryData.category.id,
                    isAllLevelsSelectable = true
                )
                categoryData.children?.let {
                    if (it.isNotEmpty()) {
                        expandableItem.children = mapListingCategoryDataToExpandableItem(
                            it,
                            categoryData.trackingName,
                            categoryData.category.id
                        )
                    }
                }
                expandableItemList.add(expandableItem)
            }

            return expandableItemList
        }

        fun findExpandableItem(
            categoryListExp: List<ExpandableItem>,
            category: String,
            adTypeKey: String
        ): Pair<ExpandableItem, Int>? {
            for (item in categoryListExp) {
                if (item.key == category) {
                    item.adTypes?.forEachIndexed { index, adType ->
                        if (adType.key == adTypeKey) {
                            return Pair(item, index)
                        }
                    }
                }
                if (item.children?.isNotEmpty() == true) {
                    val foundItem =
                        findExpandableItem(item.children!!, category, adTypeKey)
                    if (foundItem != null) {
                        return foundItem
                    }
                }
            }
            return null
        }

        fun findExpandableItemWithId(
            categoryListExp: List<ExpandableItem>,
            category: String,
            adType: String
        ): ExpandableItem? {
            if (adType.isEmpty()) {
                for (item in categoryListExp) {
                    if (item.key == category) {
                        return item
                    }
                    if (!item.children.isNullOrEmpty()) {
                        val foundItem = findExpandableItemWithId(item.children!!, category, "")
                        if (foundItem != null) {
                            return foundItem
                        }
                    }
                }
            } else {
                for (item in categoryListExp) {
                    if (item.key == category && item.adTypes?.any {
                            it.key.equals(
                                adType,
                                ignoreCase = true
                            )
                        } == true) {
                        return item
                    }
                    if (!item.children.isNullOrEmpty()) {
                        val foundItem = findExpandableItemWithId(item.children!!, category, adType)
                        if (foundItem != null) {
                            return foundItem
                        }
                    }
                }
            }
            return null
        }

        fun buildIconUrl(url: String?): String {
            url?.let {
                return "https://assets.avito.ma/icons/svg/" + url + ".svg"
            }
            return "null"
        }
    }
}


