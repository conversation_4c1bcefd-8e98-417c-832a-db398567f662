package se.scmv.morocco.avitov2.filters.presentation

import se.scmv.morocco.common.lang.LocaleManager

object FiltersHelpers {

    @JvmStatic
    fun createSaveSearchQuery(
        selectedFilters: Map<String, Pair<Any, String>>,
        selectedCategory: Pair<Int, String>
    ) = buildString {
        // Add selected category parameters
        append("lang=${LocaleManager.getCurrentLanguage()}")
        append("&category=${selectedCategory.first}")
        append("&adType=${selectedCategory.second}")

        // Add other selected filters
        selectedFilters.forEach { (key, pairValue) ->
            val value = pairValue.first
            when {
                // Don't send these info to backend.
                key == "areaLabel" || key == "cityLabel" -> null

                value is Pair<*, *> -> "$key=${value.first}-${value.second}"

                value is Boolean && value -> "$key=$value"

                // We add this condition manually because delivery and offers_shipping_within_city
                // have default value to true so if the user saves a search with these two filter
                // set to false => they wont be added to savedSearch 'value is Boolean && value => false'
                value is Boolean && (key == "delivery" || key == "offers_shipping_within_city") -> "$key=$value"

                value is List<*> && !value.joinToString(",").contains(" ") ->
                    "$key=${value.joinToString(",")}"

                value is String && value.isNotBlank() && !value.contains(" ") -> "$key=$value"

                else -> null
            }?.let { filter ->
                append("&$filter")
            }
        }
    }
}