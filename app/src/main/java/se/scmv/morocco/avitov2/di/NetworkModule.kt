package se.scmv.morocco.avitov2.di

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory
import retrofit2.converter.gson.GsonConverterFactory
import se.scmv.morocco.BuildConfig
import se.scmv.morocco.adstickybanner.network.AdServerAdvertisingApi
import se.scmv.morocco.adstickybanner.network.ApiConstants
import se.scmv.morocco.adstickybanner.network.LeadsRequestApi
import se.scmv.morocco.avitov2.filters.data.network.ListingFiltersApi
import se.scmv.morocco.urlsprovider.UrlsProvider
import se.scmv.morocco.userprofile.service.UserProfileApi
import javax.inject.Named
import javax.inject.Singleton
import javax.net.ssl.SSLContext

@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {
    @Singleton
    @Provides
    fun provideHttpLoggingInterceptor() = HttpLoggingInterceptor().apply {
        if (BuildConfig.DEBUG) {
            level = HttpLoggingInterceptor.Level.BODY
        }
    }

    @Singleton
    @Provides
    fun provideOkHttpClient(loggingInterceptor: HttpLoggingInterceptor) = OkHttpClient.Builder()
        .addInterceptor(loggingInterceptor)
        .build()

    @Singleton
    @Provides
    @Named("FiltersRetrofit")
    fun provideFiltersRetrofit(okHttpClient: OkHttpClient): Retrofit = provideRetrofit(
        okHttpClient = okHttpClient,
        baseUrl = UrlsProvider.getNewListingFilterUrl()
    )

    @Singleton
    @Provides
    @Named("AdServerRetrofit")
    fun provideAdServerRetrofit(okHttpClient: OkHttpClient): Retrofit = provideRetrofit(
        okHttpClient = okHttpClient,
        baseUrl = UrlsProvider.getAdServerUrl()
    )

    @Singleton
    @Provides
    @Named("UserProfileRetrofit")
    fun provideUserProfileRetrofit(okHttpClient: OkHttpClient): Retrofit = provideRetrofit(
        okHttpClient = okHttpClient,
        baseUrl = UrlsProvider.getMAUHostUrl()
    )

    @Singleton
    @Provides
    @Named("BaseRetrofit")
    fun provideListingRetrofit(okHttpClient: OkHttpClient): Retrofit = provideRetrofit(
        okHttpClient = okHttpClient,
        baseUrl = "${UrlsProvider.getApiBaseUrl()}/"
    )

    @Singleton
    @Provides
    @Named("LeadsForceOkHttpClient")
    fun provideLeadsForceOkHttpClient(loggingInterceptor: HttpLoggingInterceptor): OkHttpClient {
        return OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .addInterceptor { chain ->
                val request = chain.request().newBuilder()
                    .addHeader(ApiConstants.HEADER_API_TOKEN, ApiConstants.API_TOKEN)
                    .build()
                chain.proceed(request)
            }
            .build()
    }

    @Singleton
    @Provides
    @Named("LeadsForceRetrofit")
    fun provideLeadsForceRetrofit(@Named("LeadsForceOkHttpClient") okHttpClient: OkHttpClient): Retrofit = provideRetrofit(
        okHttpClient = okHttpClient,
        baseUrl = UrlsProvider.getLeadsForceUrl()
    )

    @Singleton
    @Provides
    fun provideLeadsForceApi(
        @Named("LeadsForceRetrofit") retrofit: Retrofit
    ): LeadsRequestApi = retrofit.create(LeadsRequestApi::class.java)

    @Singleton
    @Provides
    fun provideListingFiltersApi(@Named("FiltersRetrofit") retrofit: Retrofit): ListingFiltersApi =
        retrofit.create(ListingFiltersApi::class.java)


    @Singleton
    @Provides
    fun provideAdServerAdvertisingApi(
        @Named("AdServerRetrofit") retrofit: Retrofit
    ): AdServerAdvertisingApi = retrofit.create(AdServerAdvertisingApi::class.java)


    @Singleton
    @Provides
    fun provideUserProfileApi(
        @Named("UserProfileRetrofit") retrofit: Retrofit
    ): UserProfileApi {
        return retrofit.create(UserProfileApi::class.java)
    }

    private fun provideRetrofit(okHttpClient: OkHttpClient, baseUrl: String) = Retrofit.Builder()
        .baseUrl(baseUrl)
        .client(okHttpClient)
        .addConverterFactory(GsonConverterFactory.create())
        .addCallAdapterFactory(RxJava2CallAdapterFactory.create())
        .build()


    @Singleton
    @Provides
    fun provideSSLContext(): SSLContext = SSLContext.getInstance("TLSv1.2")
}