package se.scmv.morocco.avitov2.filters.presentation

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.apollographql.apollo3.api.Optional
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import ma.avito.orion.ui.accordion.model.ExpandableItem
import se.scmv.morocco.R
import se.scmv.morocco.avitov2.adinsert.presentation.builders.FieldUIType
import se.scmv.morocco.avitov2.adinsert.presentation.utils.InsertUIUtils
import se.scmv.morocco.avitov2.core.domain.models.Resource
import se.scmv.morocco.avitov2.core.presentation.SnackBarType
import se.scmv.morocco.avitov2.filters.domain.models.GetListingQueryParams
import se.scmv.morocco.avitov2.filters.domain.models.GetListingQueryParamsWithLabels
import se.scmv.morocco.avitov2.filters.domain.repositories.ListingFiltersRepository
import se.scmv.morocco.data.rest.config.dtos.ListingCategoryFiltersDto
import se.scmv.morocco.domain.models.BookmarkedSearchQuery
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.type.AdLocationFilter
import se.scmv.morocco.type.AdParamListNumericFilter
import se.scmv.morocco.type.AdParamListTextFilter
import se.scmv.morocco.type.AdParamSingleBooleanFilter
import se.scmv.morocco.type.AdParamSingleNumericFilter
import se.scmv.morocco.type.AdParamSingleTextFilter
import se.scmv.morocco.type.AdParamsListMatchFilters
import se.scmv.morocco.type.AdParamsRangeFilter
import se.scmv.morocco.type.AdParamsSingleMatchFilters
import se.scmv.morocco.type.AdSortProperty
import se.scmv.morocco.type.AdTypeKey
import se.scmv.morocco.type.ListingAdParamsFilters
import se.scmv.morocco.type.RangeFilter
import se.scmv.morocco.type.SortOrder
import se.scmv.morocco.utils.enums.SortType
import se.scmv.morocco.utils.toOptional
import javax.inject.Inject
import se.scmv.morocco.domain.models.Resource as NewResource

@HiltViewModel
class ListingFiltersViewModel @Inject constructor(
    private val listingFiltersRepository: ListingFiltersRepository,
    private val accountRepository: AccountRepository
) : ViewModel() {

    companion object {
        const val DEFAULT_CATEGORY_ID = 0
        const val DEFAULT_CATEGORY_TYPE = "all"
        const val FILTER_KEYWORD_KEY = "keyword"
        const val FILTER_CATEGORY_KEY = "category"
    }

    private val _uiState = MutableStateFlow(ListingFiltersViewState())
    val uiState = _uiState.asStateFlow()

    private val _savedSearch = MutableStateFlow<BookmarkedSearchQuery?>(null)
    val savedSearch = _savedSearch.asStateFlow()

    private val _oneTimeEvents = MutableSharedFlow<ListingFiltersOneTimeEvents>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    private val _categories = MutableStateFlow(emptyList<ExpandableItem>())
    val categories = _categories.asStateFlow()

    var selectedCategory: Pair<Int, String> = DEFAULT_CATEGORY_ID to DEFAULT_CATEGORY_TYPE
        private set
    private var selectedFilters: Map<String, Pair<Any, String>> = mapOf()

    private var loadAdsCountJob: Job? = null


    init {
        loadData()
    }

    fun onCategorySelected(id: Int, type: String) {
        selectedCategory = id to type
        loadFilters()
    }

    fun setCategory(id: Int, type: String) {
        selectedCategory = id to type
    }

    fun onRetry() {
        loadData()
        if (selectedCategory != DEFAULT_CATEGORY_ID to DEFAULT_CATEGORY_TYPE || selectedFilters.isNotEmpty()) {
            loadAdsCount()
        }
    }

    fun onSaveSearchChecked(selectedFilters: Map<String, Pair<Any, String>>) {
        viewModelScope.launch {
            _oneTimeEvents.emit(ListingFiltersOneTimeEvents.ShowHideSearchProgress(true))
            val query = FiltersHelpers.createSaveSearchQuery(
                selectedFilters = selectedFilters,
                selectedCategory = selectedCategory
            )
            when (val result = accountRepository.bookmarkSearch(query)) {
                is NewResource.Success -> {
                    _savedSearch.update { result.data }
                    _oneTimeEvents.emit(
                        ListingFiltersOneTimeEvents.ShowSnackBar(
                            message = R.string.saved_search_message,
                            type = SnackBarType.SUCCESS
                        )
                    )
                }

                is NewResource.Failure -> {
                    _savedSearch.update { null }
                    _oneTimeEvents.emit(
                        ListingFiltersOneTimeEvents.ShowSnackBar(
                            message = R.string.failed_to_save_search,
                            type = SnackBarType.ERROR
                        )
                    )
                }
            }
            _oneTimeEvents.emit(ListingFiltersOneTimeEvents.ShowHideSearchProgress(false))
        }
    }

    fun onSaveSearchUnchecked() {
        _savedSearch.value?.let { savedSearchQuery ->
            viewModelScope.launch {
                _oneTimeEvents.emit(ListingFiltersOneTimeEvents.ShowHideSearchProgress(true))
                when (accountRepository.unBookmarkSearch(savedSearchQuery)) {
                    is NewResource.Success -> {
                        _savedSearch.update { null }
                        ListingFiltersOneTimeEvents.ShowSnackBar(
                            message = R.string.unsaved_search_message,
                            type = SnackBarType.STANDARD
                        )
                    }

                    is NewResource.Failure -> ListingFiltersOneTimeEvents.ShowSnackBar(
                        message = R.string.failed_to_delete_search,
                        type = SnackBarType.ERROR
                    )
                }.also {
                    _oneTimeEvents.emit(it)
                    _oneTimeEvents.emit(ListingFiltersOneTimeEvents.ShowHideSearchProgress(false))
                }
            }
        }
    }

    fun onFilterSelected(filters: Map<String, Pair<Any, String>>) {
        selectedFilters = filters
        loadAdsCount()
    }

    private fun loadData() {
        if (_categories.value.isNotEmpty()) {
            loadFilters()
        } else viewModelScope.launch {
            _uiState.update { ListingFiltersViewState(isLoading = true) }
            when (val categories = listingFiltersRepository.getFiltersCategories()) {
                is Resource.Success -> {
                    val data = InsertUIUtils.mapListingCategoryDataToExpandableItem(categories.data)
                    _categories.update { data }
                    loadFilters()
                }

                is Resource.Failure -> _uiState.update {
                    it.copy(
                        showRetry = true,
                        isLoading = false
                    )
                }
            }
        }
    }

    fun loadFilters() {
        viewModelScope.launch {
            _uiState.update {
                it.copy(
                    isLoading = true,
                    filters = emptyList(),
                    showRetry = false
                )
            }
            val (id, type) = selectedCategory
            val filters = listingFiltersRepository.getFilters(id, type)
            _uiState.update { it.copy(isLoading = false) }
            when (filters) {
                is Resource.Success -> {
                    val lists = mutableListOf<ListingCategoryFiltersDto.BaseFilters>()
                    lists.addAll(filters.data.navBarFilters)
                    lists.addAll(filters.data.primaryFilters)
                    lists.addAll(filters.data.secondaryFilters)
                    _uiState.update { it.copy(filters = lists.removeUnsupportedFilters()) }
                }

                is Resource.Failure -> {
                    _uiState.update { it.copy(showRetry = true) }
                }
            }
        }
    }

    private fun loadAdsCount() {
        // Cancel the previous job if it exists
        loadAdsCountJob?.cancel()

        // Start a new coroutine job
        loadAdsCountJob = viewModelScope.launch {
            val adsCount = listingFiltersRepository.getAdsCount(
                buildGetListingQueryParams().getListingQueryParams
            )
            _uiState.update {
                it.copy(
                    adsCount = when (adsCount) {
                        is Resource.Success -> adsCount.data
                        is Resource.Failure -> null
                    }
                )
            }
        }
    }


    private fun List<ListingCategoryFiltersDto.BaseFilters>.removeUnsupportedFilters() = filterNot {
        (it.type == FieldUIType.TEXT_FIELD.id && it.id == FILTER_KEYWORD_KEY) ||
                (it.type == FieldUIType.SINGLE_SELECT_CATEGORY_DROPDOWN_SEARCH.id && it.id == FILTER_CATEGORY_KEY)
    }

    private inline fun <reified T> getFilterValue(
        key: String,
        defaultValue: T? = null,
        filters: Map<String, Pair<Any, String>> = selectedFilters
    ): T? {
        val value = filters[key]?.first
        return if (value is T) {
            value
        } else {
            defaultValue
        }
    }

    fun buildGetListingQueryParams(setFilters: Map<String, Pair<Any, String>> = selectedFilters): GetListingQueryParamsWithLabels {
        selectedFilters = setFilters
        val adParamsSingleMatchFiltersText = mutableListOf<AdParamSingleTextFilter>()
        val adParamsSingleMatchFiltersNumeric = mutableListOf<AdParamSingleNumericFilter>()
        val adParamsSingleMatchFiltersBoolean = mutableListOf<AdParamSingleBooleanFilter>()
        val adParamsListMatchFiltersTextList = mutableListOf<AdParamListTextFilter?>()
        val adParamsListMatchFiltersNumericList = mutableListOf<AdParamListNumericFilter?>()
        val adParamsRangeFilter = mutableListOf<AdParamsRangeFilter>()

        extractSelectedFilters(
            setFilters,
            adParamsSingleMatchFiltersText,
            adParamsSingleMatchFiltersNumeric,
            adParamsSingleMatchFiltersBoolean,
            adParamsListMatchFiltersTextList,
            adParamsListMatchFiltersNumericList,
            adParamsRangeFilter
        )

        val type = parseAdType(setFilters["adType"]?.first?.toString() ?: "all")
        val categoryId = parseCategoryId(setFilters["category"]?.first?.toString() ?: "0")
        val hasPrice = getFilterValue("has_price", false, setFilters) ?: false
        val hasImage = getFilterValue("has_image", false, setFilters) ?: false
        val textQuery = getFilterValue("keyword", "", setFilters) ?: ""
        val adProperty: AdSortProperty
        val sortOrder: SortOrder
        val sort: String? = setFilters["AdSortProperty"]?.first?.toString()
        val sortType: SortType = SortType.fromString(sort)
        when (sortType) {
            SortType.NON -> {
                adProperty = AdSortProperty.LIST_TIME
                sortOrder = SortOrder.DESC
            }

            SortType.ASC -> {
                adProperty = AdSortProperty.PRICE
                sortOrder = SortOrder.ASC
            }

            SortType.DESC -> {
                adProperty = AdSortProperty.PRICE
                sortOrder = SortOrder.DESC
            }
        }

        val priceRangeFilter =
            buildPriceRangeFilter(setFilters["price"]?.first as? Pair<Int?, Int?>)
        val locationFilter = buildAdLocationFilter(setFilters)
        val sellerTypeFilter = getFilterValue<String>("seller_type", filters = setFilters)

        return GetListingQueryParamsWithLabels(
            buildGetListingQueryParams(
                text = textQuery,
                type = type,
                categoryId = categoryId,
                hasPrice = hasPrice,
                hasImage = hasImage,
                priceRangeFilter = priceRangeFilter,
                locationFilter = locationFilter,
                adParamsSingleMatchFiltersText = adParamsSingleMatchFiltersText,
                adParamsSingleMatchFiltersNumeric = adParamsSingleMatchFiltersNumeric,
                adParamsSingleMatchFiltersBoolean = adParamsSingleMatchFiltersBoolean,
                adParamsListMatchFiltersTextList = adParamsListMatchFiltersTextList,
                adParamsListMatchFiltersNumericList = adParamsListMatchFiltersNumericList,
                adParamsRangeFilter = adParamsRangeFilter,
                filters = setFilters,
                adProperty = adProperty,
                sortOrder = sortOrder,
                sellerTypeFilter = sellerTypeFilter?.toIntOrNull()
            ),
            setFilters["cityLabel"]?.first as String?,
            setFilters["areaLabel"]?.first as String?
        )
    }

    private fun extractSelectedFilters(
        selectedFilters: Map<String, Pair<Any, String>>,
        adParamsSingleMatchFiltersText: MutableList<AdParamSingleTextFilter>,
        adParamsSingleMatchFiltersNumeric: MutableList<AdParamSingleNumericFilter>,
        adParamsSingleMatchFiltersBoolean: MutableList<AdParamSingleBooleanFilter>,
        adParamsListMatchFiltersTextList: MutableList<AdParamListTextFilter?>,
        adParamsListMatchFiltersNumericList: MutableList<AdParamListNumericFilter?>,
        adParamsRangeFilter: MutableList<AdParamsRangeFilter>
    ) {
        val keysToSkip = setOf(
            "city",
            "area",
            "cityLabel",
            "areaLabel",
            "price",
            "category",
            "hasImage",
            "hasPrice",
            "verified_seller",
            "is_hotDeal",
            "is_urgent",
            "type",
            "adType",
            "has_price",
            "has_image",
            "keyword",
            "delivery",
            "AdSortProperty",
            "delivery",
            "offers_shipping_within_city",
            "seller_type"
            )
        for ((key, pair) in selectedFilters) {
            if (key in keysToSkip) {
                continue // Skip processing for this key
            }

            val value = pair.first
            val type = pair.second

            when (type) {
                // Confirmed with the front-end and back-end teams that 'single_select_extended' should be a text field, not numeric
                FieldUIType.SINGLE_SELECT_EXTENDED.id -> {
                        adParamsSingleMatchFiltersText += AdParamSingleTextFilter(
                            name = key,
                            value = value as String
                        )
                }

                FieldUIType.TOGGLE_FIELD_FILTER.id -> {
                    // Check if it is AdParamSingleNumericFilter
                    value.toString().toBooleanStrictOrNull()?.let { booleanValue ->
                        adParamsSingleMatchFiltersBoolean += AdParamSingleBooleanFilter(
                            name = key,
                            value = booleanValue
                        )
                    }
                }

                FieldUIType.MULTISELECT_EXTENDED.id -> {
                    val numericValues = when (value) {
                        is List<*> -> value as List<String>
                        is String -> value.split(",")
                        else -> emptyList()
                    }
                    adParamsSingleMatchFiltersNumeric += numericValues.map {
                        AdParamSingleNumericFilter(
                            name = it,
                            value = 1.0
                        )
                    }
                }

                FieldUIType.MULTIPLE_SELECT_SMART_DROPDOWN.id, FieldUIType.MULTIPLE_SELECT_SMART_DROPDOWN_ICON.id -> adParamsListMatchFiltersTextList += AdParamListTextFilter(
                    name = key,
                    value = value.toString().split(",").filter { it.isNotBlank() }
                        .map { it } as List<String>)

                FieldUIType.MIN_MAX_FIELD.id -> adParamsRangeFilter += AdParamsRangeFilter(
                    name = key,
                    value = createRangeFilter(value as Pair<Int, Int>)
                )

                FieldUIType.SLIDER.id -> adParamsRangeFilter += AdParamsRangeFilter(
                    name = key,
                    value = createRangeFilter(value as Pair<Int, Int>)
                )
            }
        }
    }


    private fun createRangeFilter(value: Pair<Int, Int>): RangeFilter {
        return RangeFilter(
            greaterThanOrEqual = Optional.present(value.first.toDouble()),
            lessThanOrEqual = Optional.present(value.second.toDouble())
        )
    }


    private fun parseAdType(adType: String): AdTypeKey? {
        return try {
            AdTypeKey.valueOf(adType.uppercase())
        } catch (e: Exception) {
            null
        }
    }

    private fun parseCategoryId(categoryId: String): Int {
        return categoryId.toIntOrNull() ?: DEFAULT_CATEGORY_ID
    }

    private fun buildPriceRangeFilter(pricePair: Pair<Int?, Int?>?): RangeFilter {
        return RangeFilter(
            greaterThanOrEqual = try {
                pricePair?.first?.toDouble()?.let { Optional.present(it) }
            } catch (e: Exception) {
                null
            } ?: Optional.absent(),
            lessThanOrEqual = try {
                pricePair?.second?.toDouble()?.let { Optional.present(it) }
            } catch (e: Exception) {
                null
            } ?: Optional.absent()
        )
    }

    private fun buildAdLocationFilter(filters: Map<String, Pair<Any, String>>): AdLocationFilter {
        val cityIds = getFilterValue<String>("city", filters = filters)?.let { ids ->
            if (ids.isBlank()) return@let emptyList()
            ids.split(",").filter { it.isNotBlank() }.map { it.toInt() }
        }
        val areaIds = getFilterValue<String>("area", filters = filters)?.let { ids ->
            if (ids.isBlank()) return@let emptyList()
            ids.split(",").filter { it.isNotBlank() }.map { it.toInt() }
        }
        return AdLocationFilter(
            cityIds = cityIds.toOptional(),
            areaIds = areaIds.toOptional()
        )
    }

    private fun buildListingAdParamsFilters(
        adParamsSingleMatchFiltersText: List<AdParamSingleTextFilter>,
        adParamsSingleMatchFiltersNumeric: List<AdParamSingleNumericFilter>,
        adParamsSingleMatchFiltersBoolean: List<AdParamSingleBooleanFilter>,
        adParamsListMatchFiltersTextList: List<AdParamListTextFilter?>,
        adParamsListMatchFiltersNumericList: List<AdParamListNumericFilter?>,
        adParamsRangeFilter: List<AdParamsRangeFilter>
    ): ListingAdParamsFilters? {
        val singleMatchText = if (adParamsSingleMatchFiltersText.isNotEmpty()) Optional.present(
            adParamsSingleMatchFiltersText
        ) else Optional.absent()
        val singleMatchNumeric =
            if (adParamsSingleMatchFiltersNumeric.isNotEmpty()) Optional.present(
                adParamsSingleMatchFiltersNumeric
            ) else Optional.absent()
        val singleMatchBoolean =
            if (adParamsSingleMatchFiltersBoolean.isNotEmpty()) Optional.present(
                adParamsSingleMatchFiltersBoolean
            ) else Optional.absent()
        val singleMatch =
            if (singleMatchText.isPresent() || singleMatchNumeric.isPresent() || singleMatchBoolean.isPresent())
                Optional.present(
                    AdParamsSingleMatchFilters(
                        text = singleMatchText,
                        numeric = singleMatchNumeric,
                        boolean = singleMatchBoolean
                    )
                ) else Optional.absent()

        val listMatchTextList = if (adParamsListMatchFiltersTextList.isNotEmpty()) Optional.present(
            adParamsListMatchFiltersTextList
        ) else Optional.absent()
        val listMatchNumericList =
            if (adParamsListMatchFiltersNumericList.isNotEmpty()) Optional.present(
                adParamsListMatchFiltersNumericList
            ) else Optional.absent()
        val listMatch =
            if (listMatchTextList.isPresent() || listMatchNumericList.isPresent()) Optional.present(
                AdParamsListMatchFilters(
                    textList = listMatchTextList,
                    numericList = listMatchNumericList
                )
            ) else Optional.absent()

        val rangeMatch =
            if (adParamsRangeFilter.isNotEmpty()) Optional.present(adParamsRangeFilter) else Optional.absent()

        return if (singleMatch.isPresent() || listMatch.isPresent() || rangeMatch.isPresent()) ListingAdParamsFilters(
            singleMatch = singleMatch,
            listMatch = listMatch,
            rangeMatch = rangeMatch
        ) else null
    }

    fun <T : Any> Optional<T>.isPresent(): Boolean = this is Optional.Present

    private fun buildGetListingQueryParams(
        text: String,
        type: AdTypeKey?,
        categoryId: Int,
        hasPrice: Boolean,
        hasImage: Boolean,
        priceRangeFilter: RangeFilter,
        locationFilter: AdLocationFilter,
        adParamsSingleMatchFiltersText: List<AdParamSingleTextFilter>,
        adParamsSingleMatchFiltersNumeric: List<AdParamSingleNumericFilter>,
        adParamsSingleMatchFiltersBoolean: List<AdParamSingleBooleanFilter>,
        adParamsListMatchFiltersTextList: List<AdParamListTextFilter?>,
        adParamsListMatchFiltersNumericList: List<AdParamListNumericFilter?>,
        adParamsRangeFilter: List<AdParamsRangeFilter>,
        filters: Map<String, Pair<Any, String>>,
        adProperty: AdSortProperty = AdSortProperty.LIST_TIME,
        sortOrder: SortOrder = SortOrder.DESC,
        sellerTypeFilter: Int? = null
    ): GetListingQueryParams {
        return GetListingQueryParams(
            categoryId = categoryId,
            hasPrice = hasPrice,
            hasImage = hasImage,
            text = text.takeIf { it.isNotEmpty() },
            type = type,
            price = if (priceRangeFilter.greaterThanOrEqual is Optional.Present || priceRangeFilter.lessThanOrEqual is Optional.Present) priceRangeFilter else null,
            location = if (locationFilter.cityIds is Optional.Present || locationFilter.areaIds is Optional.Present) locationFilter else null,
            offersShipping = getFilterValue("delivery", filters = filters),
            offersShippingWithinCity = getFilterValue("offers_shipping_within_city", filters = filters),
            isHotDeal = getFilterValue("is_hotDeal", filters = filters),
            isUrgent = getFilterValue("is_urgent", filters = filters),
            isEcommerce = getFilterValue("is_ecommerce", filters = filters),
            isNewConstruction = getFilterValue("is_immoneuf", filters = filters),
            isPremium = getFilterValue("is_premium", filters = filters),
            isVerifiedSeller = getFilterValue("verified_seller", filters = filters),
            params = buildListingAdParamsFilters(
                adParamsSingleMatchFiltersText,
                adParamsSingleMatchFiltersNumeric,
                adParamsSingleMatchFiltersBoolean,
                adParamsListMatchFiltersTextList,
                adParamsListMatchFiltersNumericList,
                adParamsRangeFilter
            ),
            adSortProperty = adProperty,
            sortOrder = sortOrder,
            isStore = sellerTypeFilter?.let { it == 1 }
        )
    }
}