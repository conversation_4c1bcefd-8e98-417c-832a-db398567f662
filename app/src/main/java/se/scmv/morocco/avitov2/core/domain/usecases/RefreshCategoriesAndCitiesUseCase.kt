package se.scmv.morocco.avitov2.core.domain.usecases

import io.realm.Realm
import io.realm.RealmList
import io.realm.kotlin.executeTransactionAwait
import se.scmv.morocco.avitov2.core.domain.models.Resource
import se.scmv.morocco.avitov2.filters.domain.repositories.ListingFiltersRepository
import se.scmv.morocco.dao.AdTypeRecord
import se.scmv.morocco.dao.CategoryLabel
import se.scmv.morocco.dao.CategoryRecord
import se.scmv.morocco.data.rest.config.dtos.ListingFilterCategoriesDto
import se.scmv.morocco.domain.repositories.ConfigRepository
import javax.inject.Inject

class RefreshCategoriesAndCitiesUseCase @Inject constructor(
    private val filtersRepository: ListingFiltersRepository,
    private val configRepository: ConfigRepository
) {

    suspend operator fun invoke() {
        fetchAndSaveCategories()
        configRepository.refresh()
    }

    private suspend fun fetchAndSaveCategories() {
        val categories = mutableListOf<CategoryRecord>()
        val result = filtersRepository.getFiltersCategories()
        when (result) {
            is Resource.Success -> {
                result.data.forEach { parent ->
                    processCategory(parent, null, 1, categories)
                }
            }
            else -> return
        }
        Realm.getDefaultInstance().executeTransactionAwait {
            it.delete(CategoryRecord::class.java)
            it.insertOrUpdate(categories)
        }
    }

    private fun processCategory(
        categoryDto: ListingFilterCategoriesDto,
        parentId: Int?,
        level: Int,
        categories: MutableList<CategoryRecord>
    ) {
        val categoryRecord = categoryDto.toCategoryRecord(level, parentId)
        categories.add(categoryRecord)

        categoryDto.children?.forEach { child ->
            processCategory(child, categoryRecord.categoryId, level + 1, categories)
        }
    }

    private fun ListingFilterCategoriesDto.toCategoryRecord(
        level: Int,
        parent: Int? = null
    ): CategoryRecord {
        return CategoryRecord().apply {
            categoryId = category.id.toInt()
            order = index
            adTypes = RealmList<AdTypeRecord?>().apply {
                add(
                    AdTypeRecord().apply {
                        type = category.adType
                    }
                )
            }
            this.level = level
            parent?.let {
                this.parent = parent
            }
            label = CategoryLabel().apply {
                arLabel = <EMAIL>
                frLabel = <EMAIL>
                uniqueKey = category.id
            }
        }
    }
}