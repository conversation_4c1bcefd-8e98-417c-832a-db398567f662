package se.scmv.morocco.avitov2.loan_simulator.presentation

import android.text.Editable
import android.widget.ArrayAdapter
import android.widget.AutoCompleteTextView
import androidx.core.widget.doOnTextChanged
import se.scmv.morocco.R
import se.scmv.morocco.avitov2.loan_simulator.domain.models.LoanSimulatorConfig
import se.scmv.morocco.avitov2.loan_simulator.domain.models.LoanSimulatorInput
import se.scmv.morocco.avitov2.loan_simulator.domain.models.LoanSimulatorOutput
import se.scmv.morocco.avitov2.loan_simulator.domain.usecases.CalculateLoanUseCase
import se.scmv.morocco.databinding.LayoutLoanSimulatorBinding
import se.scmv.morocco.utils.URLUtils
import se.scmv.morocco.widgets.clearFocusAfterClickOnDone
import kotlin.math.roundToLong


fun LayoutLoanSimulatorBinding.setup(
    useCase: CalculateLoanUseCase,
    config: LoanSimulatorConfig,
    adPrice: Double
) {
    var selectedDuration = config.defaultDuration
    fun update() {
        val input = generateInput(selectedDuration)
        if (input.personalContributionPrice > input.goodPrice) {
            etPersonalContributionAmount.error =
                root.context.getString(R.string.loan_simulator_personal_contribution_error)
            return
        } else {
            etPersonalContributionAmount.error = null
            etPersonalContributionAmount.isErrorEnabled = false
        }
        updateUi(loan = useCase(input = input))
    }

    init(adPrice, config.interestPercentage)
    actLoanDuration.setupDuration(
        loanDurations = config.loanDurations,
        defaultDuration = config.defaultDuration
    ) {
        selectedDuration = it
        update()
    }
    etGoodPrice.addOnTextChangedCallbacks {
        update()
    }
    etPersonalContributionAmount.editText?.doOnTextChanged { _, _, _, _ ->
        update()
    }
    btnConfirm.setOnClickListener {
        URLUtils.openUrlInWebView(it.context, config.redirectionUrl)
    }
}

private fun LayoutLoanSimulatorBinding.init(
    adPrice: Double,
    interestPercentage: Double
) {
    etGoodPrice.setText("$adPrice")
    etPersonalContributionAmount.editText?.setText("0")
    etPersonalContributionAmount.editText?.clearFocusAfterClickOnDone()
    etPersonalContributionPercentage.setText(percentage(0.0))
    etInterestPercentage.setText(percentage(interestPercentage))
}

private inline fun AutoCompleteTextView.setupDuration(
    loanDurations: List<Int>,
    defaultDuration: Int,
    crossinline onSelected: (Int) -> Unit
) {
    val durations = loanDurations.map {
        context.resources.getQuantityString(
            R.plurals.loan_simulator_loan_duration_with_unit,
            it,
            it
        )
    }
    val adapter = ArrayAdapter(
        context,
        R.layout.item_choose_vas_package_execution_slots_time,
        durations
    )
    text = Editable.Factory.getInstance().newEditable(
        context.resources.getQuantityString(
            R.plurals.loan_simulator_loan_duration_with_unit,
            defaultDuration,
            defaultDuration
        )
    )
    onSelected(defaultDuration)
    setAdapter(adapter)
    setOnItemClickListener { _, _, position, _ ->
        loanDurations.getOrNull(position)?.let { onSelected(it) }
        clearFocus()
    }
}

private fun LayoutLoanSimulatorBinding.generateInput(selectedDuration: Int): LoanSimulatorInput {
    val goodPrice = etGoodPrice.getValue().editTextValue.toDoubleOrNull() ?: 0.0
    val contribution =
        etPersonalContributionAmount.editText?.text.toString().toDoubleOrNull() ?: 0.0
    return LoanSimulatorInput(
        goodPrice = goodPrice,
        personalContributionPrice = contribution,
        loanDuration = selectedDuration
    )
}

private fun LayoutLoanSimulatorBinding.updateUi(loan: LoanSimulatorOutput) {
    tvAmountPerMonth.text = priceInDh(loan.monthlyPayment)
    tvLoanAmount.text = priceInDh(loan.loanAmount.roundToLong())
    tvInterestAmount.text = priceInDh(loan.interestAmount)
    etPersonalContributionPercentage.setText(
        percentage(loan.personalContributionPercentage.roundToLong())
    )
}

private fun LayoutLoanSimulatorBinding.priceInDh(price: Number): String {
    return "$price ${root.context.getString(R.string.currency)}"
}

private fun LayoutLoanSimulatorBinding.percentage(percent: Number): String {
    return String.format(
        root.context.getString(R.string.percentage_string),
        percent
    )
}

