package se.scmv.morocco.avitov2.floatingsearchview.util

import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import android.content.res.Resources
import android.graphics.drawable.Drawable
import android.os.Build
import android.os.Handler
import android.util.DisplayMetrics
import android.util.TypedValue
import android.view.View
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.ImageView
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.core.graphics.drawable.DrawableCompat

object Util {
        private const val TAG = "Util"
        @JvmStatic
        fun showSoftKeyboard(context: Context, editText: EditText?) {
                Handler().postDelayed({
                        val inputMethodManager =
                                context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                        inputMethodManager.showSoftInput(editText, InputMethodManager.SHOW_FORCED)
                }, 100)
        }

        @JvmStatic
        fun closeSoftKeyboard(activity: Activity) {
                val currentFocusView = activity.currentFocus
                if (currentFocusView != null) {
                        val imm =
                                activity.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                        imm.hideSoftInputFromWindow(currentFocusView.windowToken, 0)
                }
        }

        @JvmStatic
        fun dpToPx(dp: Int): Int {
                val metrics = Resources.getSystem().displayMetrics
                return (dp * metrics.density).toInt()
        }

        fun pxToDp(px: Int): Int {
                val metrics = Resources.getSystem().displayMetrics
                return (px / metrics.density).toInt()
        }

        @JvmStatic
        fun spToPx(sp: Int): Int {
                val metrics = Resources.getSystem().displayMetrics
                return TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_SP, sp.toFloat(), metrics)
                        .toInt()
        }

        fun pxToSp(px: Int): Int {
                val metrics = Resources.getSystem().displayMetrics
                return px / metrics.scaledDensity.toInt()
        }

        fun getScreenWidth(activity: Activity): Int {
                val display = activity.windowManager.defaultDisplay
                val outMetrics = DisplayMetrics()
                display.getMetrics(outMetrics)
                return outMetrics.widthPixels
        }

        fun getScreenHeight(activity: Activity): Int {
                val display = activity.windowManager.defaultDisplay
                val outMetrics = DisplayMetrics()
                display.getMetrics(outMetrics)
                return outMetrics.heightPixels
        }

        @JvmStatic
        fun setIconColor(iconHolder: ImageView, color: Int) {
                val wrappedDrawable = DrawableCompat.wrap(iconHolder.drawable)
                DrawableCompat.setTint(wrappedDrawable, color)
                iconHolder.setImageDrawable(wrappedDrawable)
                iconHolder.invalidate()
        }

        /**
         * Gets a reference to a given drawable and prepares it for use with tinting through.
         *
         * @param resId the resource id for the given drawable
         * @return a wrapped drawable ready fo use
         * with [DrawableCompat]'s tinting methods
         * @throws Resources.NotFoundException
         */
        @JvmStatic
        @Throws(Resources.NotFoundException::class)
        fun getWrappedDrawable(context: Context, @DrawableRes resId: Int): Drawable {
                return DrawableCompat.wrap(
                        ResourcesCompat.getDrawable(
                                context.resources,
                                resId, null
                        )!!
                )
        }

        @JvmStatic
        @Throws(Resources.NotFoundException::class)
        fun getColor(context: Context?, @ColorRes resId: Int): Int {
                return ContextCompat.getColor(context!!, resId)
        }

        @JvmStatic
        fun removeGlobalLayoutObserver(view: View, layoutListener: OnGlobalLayoutListener?) {
                if (Build.VERSION.SDK_INT < 16) {
                        view.viewTreeObserver.removeGlobalOnLayoutListener(layoutListener)
                } else {
                        view.viewTreeObserver.removeOnGlobalLayoutListener(layoutListener)
                }
        }

        @JvmStatic
        fun getHostActivity(context: Context?): Activity? {
                var context = context
                while (context is ContextWrapper) {
                        if (context is Activity) {
                                return context
                        }
                        context = context.baseContext
                }
                return null
        }
}