package se.scmv.morocco.avitov2.floatingsearchview.suggestions;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.core.graphics.drawable.DrawableCompat;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import se.scmv.morocco.R;
import se.scmv.morocco.avitov2.floatingsearchview.suggestions.model.SearchSuggestion;
import se.scmv.morocco.avitov2.floatingsearchview.util.Util;

public class SearchSuggestionsAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private static final String TAG = "SearchSuggestionsAdapter";

    private List<? extends SearchSuggestion> mSearchSuggestions = new ArrayList<>();

    private Listener mListener;

    private Context mContext;

    private Drawable mRightIconDrawable;
    private boolean mShowRightMoveUpBtn = false;
    private int mBodyTextSizePx;
    private int mTextColor = -1;
    private int mRightIconColor = -1;

    private static final int VIEW_TYPE_HEADER = 0;
    private static final int VIEW_TYPE_ITEM = 1;
    private String headerTitle = "";


    public interface OnBindSuggestionCallback {

        void onBindSuggestion(View suggestionView, ImageView leftIcon, TextView textView,
                              SearchSuggestion item, int itemPosition);
    }

    private OnBindSuggestionCallback mOnBindSuggestionCallback;

    public interface Listener {

        void onItemSelected(SearchSuggestion item);

        void onMoveItemToSearchClicked(SearchSuggestion item);

    }

    public static class SearchSuggestionViewHolder extends RecyclerView.ViewHolder {

        public TextView body;
        public ImageView leftIcon;
        public ImageView rightIcon;

        public ImageView icon;

        private Listener mListener;

        public interface Listener {

            void onItemClicked(int adapterPosition);

            void onMoveItemToSearchClicked(int adapterPosition);
        }

        public SearchSuggestionViewHolder(View v, Listener listener) {
            super(v);

            mListener = listener;
            body = (TextView) v.findViewById(R.id.body);
            leftIcon = (ImageView) v.findViewById(R.id.left_icon);
            rightIcon = (ImageView) v.findViewById(R.id.right_icon);
            icon = (ImageView) v.findViewById(R.id.icon);

            rightIcon.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    int adapterPosition = getAdapterPosition();
                    if (mListener != null && adapterPosition != RecyclerView.NO_POSITION) {
                        mListener.onMoveItemToSearchClicked(getAdapterPosition());
                    }
                }
            });

            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    int adapterPosition = getAdapterPosition();
                    if (mListener != null && adapterPosition != RecyclerView.NO_POSITION) {
                        mListener.onItemClicked(adapterPosition);
                    }
                }
            });
        }
    }

    static class TitleViewHolder extends RecyclerView.ViewHolder {
        TextView title;

        public TitleViewHolder(View itemView) {
            super(itemView);
            title = itemView.findViewById(R.id.titleTextView);
        }
    }

    public SearchSuggestionsAdapter(Context context, int suggestionTextSize, Listener listener) {
        this.mContext = context;
        this.mListener = listener;
        this.mBodyTextSizePx = suggestionTextSize;

        mRightIconDrawable = Util.getWrappedDrawable(mContext, R.drawable.ic_arrow_back_black_24dp);
        DrawableCompat.setTint(mRightIconDrawable, Util.getColor(mContext, R.color.gray_active_icon));
    }

    public void swapData(List<? extends SearchSuggestion> searchSuggestions, String headerTitle) {
        this.headerTitle = headerTitle;
        mSearchSuggestions = searchSuggestions;
        notifyDataSetChanged();
    }

    public List<? extends SearchSuggestion> getDataSet() {
        return mSearchSuggestions;
    }

    public void setOnBindSuggestionCallback(OnBindSuggestionCallback callback) {
        this.mOnBindSuggestionCallback = callback;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup viewGroup, int viewType) {
        if (viewType == VIEW_TYPE_HEADER) {
            View view = LayoutInflater.from(viewGroup.getContext())
                    .inflate(R.layout.search_suggestion_title_item, viewGroup, false);
            return new TitleViewHolder(view);
        } else {
            View view = LayoutInflater.from(viewGroup.getContext())
                    .inflate(R.layout.search_suggestion_item, viewGroup, false);
            return new SearchSuggestionViewHolder(view, new SearchSuggestionViewHolder.Listener() {
                @Override
                public void onItemClicked(int adapterPosition) {
                    if (mListener != null && adapterPosition > -1) {
                        mListener.onItemSelected(mSearchSuggestions.get(adapterPosition));
                    }
                }

                @Override
                public void onMoveItemToSearchClicked(int adapterPosition) {
                    if (mListener != null && adapterPosition > -1) {
                        mListener.onMoveItemToSearchClicked(mSearchSuggestions.get(adapterPosition));
                    }
                }
            });
        }
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder vh, int position) {
        if (vh instanceof TitleViewHolder) {
            ((TitleViewHolder) vh).title.setText(headerTitle);
        } else if (vh instanceof SearchSuggestionViewHolder) {
            SearchSuggestionViewHolder viewHolder = (SearchSuggestionViewHolder) vh;

            if (!mShowRightMoveUpBtn) {
                viewHolder.rightIcon.setEnabled(false);
                viewHolder.rightIcon.setVisibility(View.INVISIBLE);
            } else {
                viewHolder.rightIcon.setEnabled(true);
                viewHolder.rightIcon.setVisibility(View.VISIBLE);
            }

            SearchSuggestion suggestionItem = mSearchSuggestions.get(position);
            String body = suggestionItem.getBody(mContext);
            viewHolder.body.setText(body);
            if (suggestionItem.isHistory()) {
                viewHolder.icon.setImageResource(R.drawable.ic_recent_search);
            } else {
                viewHolder.icon.setImageResource(R.drawable.ic_search_suggestion);
            }

            if (mTextColor != -1) {
                viewHolder.body.setTextColor(mTextColor);
            }

            if (mRightIconColor != -1) {
                Util.setIconColor(viewHolder.rightIcon, mRightIconColor);
            }

            if (mOnBindSuggestionCallback != null) {
                mOnBindSuggestionCallback.onBindSuggestion(viewHolder.itemView, viewHolder.leftIcon,
                        viewHolder.body, suggestionItem, position);
            }

            // Add bottom margin for the first item
            View itemView = viewHolder.itemView;
            ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) itemView.getLayoutParams();

            if (position == 0) {
                layoutParams.bottomMargin = (int) TypedValue.applyDimension(
                        TypedValue.COMPLEX_UNIT_DIP,
                        16, // Set desired bottom margin in dp
                        itemView.getResources().getDisplayMetrics()
                );
            } else {
                layoutParams.bottomMargin = 0;
            }

            itemView.setLayoutParams(layoutParams);
        }
    }



    @Override
    public int getItemCount() {
        return mSearchSuggestions != null ? mSearchSuggestions.size() + 1 : 0;
    }

    public void setTextColor(int color) {

        boolean notify = false;
        if (this.mTextColor != color) {
            notify = true;
        }
        this.mTextColor = color;
        if (notify) {
            notifyDataSetChanged();
        }
    }

    @Override
    public int getItemViewType(int position) {
        return position == getItemCount() - 1 ? VIEW_TYPE_HEADER : VIEW_TYPE_ITEM;
    }


    public void setRightIconColor(int color) {

        boolean notify = false;
        if (this.mRightIconColor != color) {
            notify = true;
        }
        this.mRightIconColor = color;
        if (notify) {
            notifyDataSetChanged();
        }
    }

    public void setShowMoveUpIcon(boolean show) {

        boolean notify = false;
        if (this.mShowRightMoveUpBtn != show) {
            notify = true;
        }
        this.mShowRightMoveUpBtn = show;
        if (notify) {
            notifyDataSetChanged();
        }
    }

    public void reverseList() {
        Collections.reverse(mSearchSuggestions);
        notifyDataSetChanged();
    }
}
