package se.scmv.morocco.avitov2.filters.presentation

import se.scmv.morocco.avitov2.core.presentation.SnackBarType
import se.scmv.morocco.data.rest.config.dtos.ListingCategoryFiltersDto

data class ListingFiltersViewState(
    val isLoading: Boolean = false,
    val showRetry: Boolean = false,
    val filters: List<ListingCategoryFiltersDto.BaseFilters> = emptyList(),
    val adsCount: Int? = null,
)

sealed interface ListingFiltersOneTimeEvents {
    data class ShowSnackBar(val message: Int, val type: SnackBarType) : ListingFiltersOneTimeEvents
    data class ShowHideSearchProgress(val visible: Boolean) : ListingFiltersOneTimeEvents
}