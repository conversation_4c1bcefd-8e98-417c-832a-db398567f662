package se.scmv.morocco.fragments

import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.ProgressBar
import androidx.annotation.Nullable
import androidx.fragment.app.Fragment
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.target.Target
import com.bumptech.glide.request.transition.Transition
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.ui.PlayerView
import se.scmv.morocco.Avito
import se.scmv.morocco.R
import se.scmv.morocco.analytics.AnalyticsManager
import se.scmv.morocco.login.models.Account
import se.scmv.morocco.login.models.AccountToken
import se.scmv.morocco.messaging.common.Constants
import se.scmv.morocco.utils.Constants.ADVIEW_PAGE
import se.scmv.morocco.utils.Constants.COMPLETE_VIDEO
import se.scmv.morocco.utils.Constants.PAUSE_VIDEO
import se.scmv.morocco.utils.Constants.PLAY_VIDEO
import se.scmv.morocco.utils.Constants.STATE
import se.scmv.morocco.utils.Constants.VIDEO

/**
 * Created by amine on 28/01/15.
 */
class GalleryFragment : Fragment() {
        private var adPictureView: ImageView? = null
        var exoPlayer: ExoPlayer? = null
        var playerView: PlayerView? = null
        var imageThumbnail: ImageView? = null
        var playThumbnail: ImageView? = null
        var progressBar : ProgressBar? = null
        var thumbnailUri: String? = null
        var adMedia: Uri? = null
        var isItVideo: Boolean? = null

        override fun onActivityCreated(savedInstanceState: Bundle?) {
                super.onActivityCreated(savedInstanceState)
                if (savedInstanceState != null && savedInstanceState.containsKey(AD_IMAGE)) {
                        adMedia = Uri.parse(savedInstanceState.getString(AD_IMAGE))
                        isItVideo = savedInstanceState.getBoolean(IS_VIDEO)
                }
                displayImage()
        }

        private fun displayImage() {
                if (isItVideo == true) {
                        try {
                                imageThumbnail?.let {
                                        val uri = Uri.parse(thumbnailUri)
                                        Glide.with(requireContext())
                                                .load(uri)
                                                .fitCenter()
                                                .centerCrop()
                                                .into(it)

                                }
                        }catch (e : Exception){

                        }
                        imageThumbnail?.setOnClickListener {
                                play()
                        }
                        playThumbnail?.setOnClickListener {
                                play()
                        }
                } else {
                        Glide.with(requireContext())
                                .load(adMedia)
                                .listener(object : RequestListener<Drawable> {
                                        override fun onLoadFailed(
                                                e: GlideException?,
                                                model: Any?,
                                                target: Target<Drawable>?,
                                                isFirstResource: Boolean
                                        ): Boolean {
                                                progressBar?.visibility = View.GONE
                                                return false
                                        }

                                        override fun onResourceReady(
                                                resource: Drawable?,
                                                model: Any?,
                                                target: Target<Drawable>?,
                                                dataSource: DataSource?,
                                                isFirstResource: Boolean
                                        ): Boolean {
                                                progressBar?.visibility = View.GONE
                                                return false
                                        }

                                })
                                .into(object : CustomTarget<Drawable?>() {
                                        override fun onResourceReady(
                                                resource: Drawable,
                                                transition: Transition<in Drawable?>?
                                        ) {
                                                adPictureView?.setImageDrawable(resource)
                                        }

                                        override fun onLoadCleared(@Nullable placeholder: Drawable?) =
                                                Unit

                                })
                }
        }

        private fun play() {
                imageThumbnail?.visibility = View.GONE
                playThumbnail?.visibility = View.GONE
                exoPlayer = ExoPlayer.Builder(requireActivity()).build()
                playerView?.player = exoPlayer
                val mediaItem = adMedia?.let { MediaItem.fromUri(it) }
                playerView?.player = exoPlayer
                mediaItem?.let { exoPlayer?.addMediaItem(it) }
                exoPlayer?.playWhenReady = true
                exoPlayer?.playbackState
                exoPlayer?.prepare()
                exoPlayer?.addListener(object : Player.Listener {
                        override fun onPlaybackStateChanged(@Player.State state: Int) {
                                if (state == Player.STATE_ENDED) {
                                        trackVideo(COMPLETE_VIDEO)
                                }
                        }

                        override fun onIsPlayingChanged(isPlaying: Boolean) {
                                if (isPlaying) trackVideo(PLAY_VIDEO) else trackVideo(
                                        PAUSE_VIDEO
                                )
                                super.onIsPlayingChanged(isPlaying)
                        }
                })
        }

        private fun trackVideo(state: String) {
                val eventProperties = HashMap<String?, String?>()
                eventProperties[Constants.ELEMENT_NAME] = VIDEO
                eventProperties[STATE] = state
                eventProperties[Constants.PAGE_NAME] = ADVIEW_PAGE
                if (AccountToken.isLoggedIn(Avito.context)) {
                        val currentAccount = Account.getCurrentAccount(Avito.context)
                        if (currentAccount != null) {
                                eventProperties[Constants.SELLER_TYPE] =
                                        if (currentAccount.accountType == 1) Constants.ACCOUNT_TYPE_SHOP else Constants.ACCOUNT_TYPE_PRIVATE
                        }
                }
                val manager = AnalyticsManager.instance
                manager?.logFirebaseEvent(
                        Constants.ELEMENT_CLICKED,
                        eventProperties
                )
        }

        override fun onSaveInstanceState(outState: Bundle) {
                outState.putString(AD_IMAGE, adMedia.toString())
                isItVideo?.let { outState.putBoolean(IS_VIDEO, it) }
                super.onSaveInstanceState(outState)
        }

        override fun onCreateView(
                inflater: LayoutInflater,
                container: ViewGroup?,
                savedInstanceState: Bundle?
        ): View? {
                val view = inflater.inflate(
                        if (isItVideo == true) R.layout.exo_player_video else R.layout.fragment_preview_image,
                        container,
                        false
                )
                if (isItVideo == true) {
                        playerView = view.findViewById(R.id.video_view)
                        imageThumbnail = view.findViewById(R.id.image_thumbnail)
                        playThumbnail = view.findViewById(R.id.play_ex_thum)
                } else {
                        adPictureView = view.findViewById(R.id.pictureView)
                        progressBar = view.findViewById(R.id.image_progressBar)

                }
                return view
        }

        override fun setUserVisibleHint(isVisibleToUser: Boolean) {
                super.setUserVisibleHint(isVisibleToUser)
                if (!isVisibleToUser && exoPlayer?.isPlaying == true) {
                        exoPlayer?.pause()
                }
        }

        override fun onDestroy() {
                super.onDestroy()
                exoPlayer?.release()
        }

        companion object {
                private const val AD_IMAGE = "ad_image"
                private const val IS_VIDEO = "is_video"

                @JvmStatic
                fun getInstance(
                        media: Uri?,
                        isItVideo: Boolean = false,
                        thumbnailUri: String? = null
                ): GalleryFragment {
                        val fragment = GalleryFragment()
                        fragment.adMedia = media
                        fragment.isItVideo = isItVideo
                        fragment.thumbnailUri = thumbnailUri
                        return fragment
                }
        }
}