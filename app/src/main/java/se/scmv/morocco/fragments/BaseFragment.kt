package se.scmv.morocco.fragments

import android.content.Context
import androidx.fragment.app.Fragment
import com.google.android.material.snackbar.Snackbar
import org.greenrobot.eventbus.Subscribe
import se.scmv.morocco.R
import se.scmv.morocco.analytics.AnalyticsManager
import se.scmv.morocco.events.BusEvent
import se.scmv.morocco.login.models.AccountToken
import se.scmv.morocco.network.NetworkManager
import se.scmv.morocco.network.NetworkManager.NetworkStatusListener
import se.scmv.morocco.utils.EventBusManager.Companion.instance
import se.scmv.morocco.utils.Log.i

/**
 * Created by amine on 19/11/14.
 */

abstract class TrackableFragment : BaseFragment() {

        abstract fun getAnalyticsProperties(): Map<String?, String?>

        abstract fun getScreenViewAnalyticalName(): String

        open fun logFragment() {
                AnalyticsManager.instance
                        ?.logFirebase(getScreenViewAnalyticalName(), getAnalyticsProperties().toMutableMap())
        }

        override fun onResume() {
                super.onResume()
                logFragment()
        }

        override fun setUserVisibleHint(isVisibleToUser: Boolean) {
                super.setUserVisibleHint(isVisibleToUser)
                if (isVisibleToUser) {
                        if (skipTrackingFirstImpression() && isFirstImpression) {
                                isFirstImpression = false
                                return
                        }
                }
        }
}

abstract class BaseFragment : Fragment(), NetworkStatusListener {
        var title = ""

        @JvmField
        protected var mNetworkManager: NetworkManager? = null

        @JvmField
        protected var mContext: Context? = null

        //attribute to check if fragment is registred to an Otto bus
        private val isBusRegistered = false

        protected var isFirstImpression = true
        fun setTitle(resId: Int) {
                if (this.isAdded) title = resources.getString(resId)
        }


        /**
         * Override this method to block tracking this Fragment
         *
         * @return false to disable tracking, true enable traking
         */
        @Deprecated("To be removed logic is moved to TrackableFragment", ReplaceWith("true"))
        protected open fun canTrackFragment(): Boolean {
                return true
        }

        /**
         * Override this method to skip The first Impression Usually when the fragment is the first
         * element and the activity is tracked
         *
         * @return false to skip the first Impression true the other option
         */
        protected open fun skipTrackingFirstImpression(): Boolean {
                return false
        }


        override fun onStart() {
                super.onStart()
                instance?.register(this)
        }

        override fun onStop() {
                instance?.unregister(this)
                super.onStop()
        }

        override fun onAttach(context: Context) {
                super.onAttach(context)
                mContext = context
                mNetworkManager = NetworkManager(mContext)
                mNetworkManager?.networkListener = this
        }

        protected fun displayValidationNotif(text: String?) {
                if (view != null) {
                        view?.let { viewSafe ->
                                text?.let {
                                        val snackbar = Snackbar.make(
                                                viewSafe, text,
                                                Snackbar.LENGTH_LONG
                                        )
                                        val snackBarView = snackbar.view
                                        snackBarView.setBackgroundColor(resources.getColor(R.color.error_background))
                                        snackbar.show()
                                }
                        }

                }
        }

        /**
         * Override this method to catch events sent from EventBus
         *
         * @param t
         * @param <T>
        </T> */
        @Subscribe
        open fun <T : BusEvent?> onEvent(t: T) {
                i(TAG, "bus event received")
        }

        override fun onConnected() {}
        override fun onUnavailableNetwork() {
                onOffline()
        }

        abstract fun onOffline()

        protected fun isNotLoggedIn(): Boolean {
                return !AccountToken.isLoggedIn(requireContext()) || (AccountToken.isLoggedIn(
                        requireContext()
                ) && AccountToken.isSessionExpired())
        }

        companion object {
                @JvmField
                var TAG: String? = null
        }
}