package se.scmv.morocco.similarads.ads.network

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import se.scmv.morocco.similarads.SuggestedAd

@Keep
data class SuggestedAdModel(
        @SerializedName("adId") val adId: Int,
        @SerializedName("listId") val listId: Int,
        @SerializedName("name") val name: String,
        @SerializedName("subject") val subject: String,
        @SerializedName("category") val category: Int,
        @SerializedName("region") val region: Int,
        @SerializedName("description") val description: String,
        @SerializedName("price") val price: String,
        @SerializedName("date") val date: String,
        @SerializedName("imageUrl") val image: String,
        @SerializedName("imageCount") val imageCount: Int
) : SuggestedAd {
        override fun category(): Int {
                return this.category
        }

        override fun region(): Int {
                return this.region
        }

        override fun id(): String {
                return this.listId.toString()
        }

        override fun timestamp(): String {
                return this.date
        }

        override fun title(): String {
                return this.subject
        }

        override fun price(): String {
                return this.price
        }

        override fun nbImages(): Int {
                return this.imageCount
        }

        override fun thumbUrl(): String {
                return this.image
        }

}
