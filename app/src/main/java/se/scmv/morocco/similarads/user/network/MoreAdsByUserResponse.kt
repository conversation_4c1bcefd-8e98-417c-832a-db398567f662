package se.scmv.morocco.similarads.user.network

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import se.scmv.morocco.similarads.ads.network.SuggestedAdModel

@Keep
data class MoreAdsByUserResponse(
        @SerializedName("ads") val ads: List<SuggestedAdModel>,
        @SerializedName("adsCount") val adsCount: Int,
        @SerializedName("pageCount") val pageCount: Int
)
