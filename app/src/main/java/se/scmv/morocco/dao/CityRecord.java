package se.scmv.morocco.dao;


import android.content.Context;

import io.realm.Realm;
import io.realm.RealmList;
import io.realm.RealmObject;
import io.realm.RealmQuery;
import io.realm.RealmResults;
import se.scmv.morocco.Avito;
import se.scmv.morocco.common.lang.LocaleManager;
import se.scmv.morocco.utils.Utils;

/**
 * Created by amine on 07/01/15.
 */
public class CityRecord extends RealmObject {

    public static final String CITY_ID = "cityId";
    public static final String NAME = "name";
    public static final int ALL_CITIES_ID = 0;
    private int cityId;
    private Label label;
    private RealmList<TownRecord> towns;//persisted list of towns


    public static RealmResults<CityRecord> getAllCities(Context ctx) {
        Realm realm = Realm.getDefaultInstance();
        return realm.where(CityRecord.class)
                .findAll();
    }


    public static CityRecord getCityByRegionId(int cityId) {
        Realm realm = Realm.getDefaultInstance();
        RealmQuery<CityRecord> query = realm.where(CityRecord.class);

        // Add query conditions:
        query.equalTo(CITY_ID, cityId);

        // Execute the query:
        RealmResults<CityRecord> result = query.findAll();
        if (result != null && result.size() > 0) {
            CityRecord cityRecord = result.get(0);
            return cityRecord;
        }
        return null;
    }


    public int getCityId() {
        if (isValid()) {
            return this.cityId;
        } else {
            Utils.rebirthApp(Avito.Companion.getContext());
            return 0;
        }
    }


    public void setCityId(int cityId) {
        this.cityId = cityId;
    }

    public Label getLabel() {
        if (isValid() && label.isValid()) {
            return this.label;
        } else {
            Utils.rebirthApp(Avito.Companion.getContext());
            return null;
        }
    }

    public void setLabel(Label label) {
        this.label = label;
    }

    public String getName() {
        if (isValid() && label.isValid()) {
            if (LocaleManager.INSTANCE.isFr()) {
                return this.label.getFrLabel();
            } else {
                return this.label.getArLabel();
            }
        } else {
            Utils.rebirthApp(Avito.Companion.getContext());
        }
        return null;
    }

    public String getAnalyticsName() {
        if (isValid() && label.isValid()) {
            return this.label.getFrLabel();
        } else {
            Utils.rebirthApp(Avito.Companion.getContext());
            return null;
        }
    }

    public RealmList<TownRecord> getTowns() {
        if (isValid() && towns.isValid()) {
            return towns;
        } else {
            Utils.rebirthApp(Avito.Companion.getContext());
            return null;
        }
    }

    public void setTowns(RealmList<TownRecord> towns) {
        this.towns = towns;
    }


}