package se.scmv.morocco.dao

import io.realm.RealmObject
import io.realm.RealmResults
import io.realm.annotations.Index
import se.scmv.morocco.utils.Log.d
import se.scmv.morocco.utils.Log.e

open class ValuesRecord : RealmObject() {
        var family: String? = null
        var key: String? = null

        @Index
        var value: String? = null

        companion object {
                const val FAMILY = "family"
                const val KEY = "key"
                const val VALUE = "value"

                /**
                 *
                 *
                 * @param family
                 * @param key
                 * @return
                 */
                fun getValuesWithinFamilyAndKey(
                        family: String,
                        key: String?
                ): ArrayList<ValuesRecord> {
                        var family = family
                        d(FAMILY, family)
                        if (family.equals("brand", ignoreCase = true)) {
                                family = "carbrands"
                        }
                        val data: RealmResults<ValuesRecord> = DaoManager.getInstance()
                                .get<ValuesRecord>(
                                        ValuesRecord::class.java,
                                        arrayOf(FAMILY, KEY),
                                        arrayOf(family, key)
                                )
                                .sort("value")
                        return ArrayList(data)
                }

                @JvmStatic
                fun getValuesWithinFamily(family: String): RealmResults<ValuesRecord> {
                        e("ValuesRecord", "looking for: $family")
                        return DaoManager.getInstance()
                                .get(
                                        ValuesRecord::class.java,
                                        arrayOf(FAMILY),
                                        arrayOf(family)
                                ) //.sort("value")
                                .where().distinct("value").findAll()
                }
        }
}