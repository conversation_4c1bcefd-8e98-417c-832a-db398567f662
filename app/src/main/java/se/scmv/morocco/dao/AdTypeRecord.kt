package se.scmv.morocco.dao

import io.realm.RealmObject
import se.scmv.morocco.common.lang.LocaleManager

/**
 * Created by amine on 30/03/15.
 */
open class AdTypeRecord : RealmObject() {
        var type: String? = null
        var order = 0
        var label: Label? = null
        val name: String?
                get() = if (LocaleManager.isFr()) {
                        label?.frLabel
                } else {
                        label?.arLabel
                }
        val analyticsName: String?
                get() = label?.frLabel

        override fun toString(): String {
                return "type:" + type + ", labelFr:" + label?.frLabel + ", labelAr:" + label?.arLabel
        }

        companion object {
                const val AD_TYPE = "type"
                const val AD_LABEL = "label"
        }
}