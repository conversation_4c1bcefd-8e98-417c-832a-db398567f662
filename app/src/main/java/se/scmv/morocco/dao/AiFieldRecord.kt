package se.scmv.morocco.dao

import io.realm.RealmObject
import io.realm.annotations.Index

/**
 * Created by <PERSON><PERSON> on 07/12/15.
 */
open class AiFieldRecord : RealmObject() {
        var category = 0

        @Index
        var qs: String? = null
        var label: String? = null
        var title: String? = null
        var type: String? = null
        var adType: String? = null
        var format: String? = null
        var requires: String? = null
        var valuesList: String? = null
        var isOptional = false
        var error: String? = null

        companion object {
                const val CATEGORY = "category"
                const val QS = "qs"
                const val LABEL = "label"
                const val TITLE = "title"
                const val TYPE = "type"
                const val AD_TYPE = "adType"
                const val FORMAT = "format"
                const val REQUIRES = "requires"
                const val VALUES_LIST = "valuesList"
                const val OPTIONAL = "optional"
        }
}