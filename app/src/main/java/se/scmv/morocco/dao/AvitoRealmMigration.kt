package se.scmv.morocco.dao

import io.realm.DynamicRealm
import io.realm.DynamicRealmObject
import io.realm.RealmMigration

/**
 * Main Avito Migration:
 * When any change to realm Scheme refer to this class
 * Doc : https://realm.io/docs/java/latest/#migrations
 */
class AvitoRealmMigration : RealmMigration {
        override fun migrate(realm: DynamicRealm, oldVersion: Long, newVersion: Long) {
                var oldVersion = oldVersion
                val schema = realm.schema

                // Example: adding a new class
                schema.create("CategoryLabel")
                        .addField("id", Int::class.javaPrimitiveType)
                        .addField("name", String::class.java)

                // Example: changing the type of a property
                schema.get("CategoryRecord")
                        ?.addField("label_temp", CategoryLabel::class.java)
                        ?.transform { obj: DynamicRealmObject ->
                                // Assuming you can transform the old field to the new field type
                                obj["label_temp"] = obj.getObject("label")
                        }
                        ?.removeField("label")
                        ?.renameField("label_temp", "label")

                oldVersion++
        }
}