package se.scmv.morocco.dao

import io.realm.RealmObject
import se.scmv.morocco.common.lang.LocaleManager

/**
 * Created by amine on 07/01/15.
 */
open class TownRecord : RealmObject() {
        var townId = 0
        var label: Label? = null
        val name: String?
                get() = if (LocaleManager.isFr()) {
                        label?.frLabel
                } else {
                        label?.arLabel
                }
        val analyticsName: String?
                get() = label?.frLabel

        companion object {
                const val TOWN_ID = "townId"
                const val NAME = "name"
        }
}