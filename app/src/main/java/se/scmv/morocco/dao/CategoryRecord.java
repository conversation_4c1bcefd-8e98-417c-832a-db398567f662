package se.scmv.morocco.dao;


import io.realm.RealmList;
import io.realm.RealmObject;
import io.realm.RealmResults;
import io.realm.annotations.Ignore;
import io.realm.annotations.PrimaryKey;
import se.scmv.morocco.Avito;
import se.scmv.morocco.common.lang.LocaleManager;
import se.scmv.morocco.utils.Utils;

public class CategoryRecord extends RealmObject {


    public static final int REAL_ESTATE_ID = 1000;
    public static final int COLOCATION_ID = 1100;
    public static final int LOCATION_VACANCE_ID = 1030;
    public static final int APPARTEMENTS_ID = 1010;
    public static final int MAISONS_VILLAS_ID = 1020;

    public static final String FIELD_CATEGORY_ID = "categoryId";
    public static final String FIELD_LEVEL = "level";
    public static final String FIELD_PARENT = "parent";
    public static final String FIELD_LABEL = "label";
    public static final String ORDER = "order";

    private int order;

    @PrimaryKey
    private int categoryId;
    private int level;
    private int parent;
    private CategoryLabel label;
    private RealmList<AdTypeRecord> adTypes;

    //Type and media offer were introduced to differentiate between a category and media offer
    @Ignore
    private TYPE type = TYPE.CATEGORY;
    public static CategoryRecord getCategory(int id) {
        DaoManager manager = DaoManager.getInstance();
        RealmResults<CategoryRecord> results = manager.getById(CategoryRecord.class, "categoryId", id);
        if (results.size() > 0)
            return results.get(0);
        else
            return null;
    }

    public static int getCategoryParent(Integer categoryId) {
        if (categoryId != null) {
            CategoryRecord categoryRecord = getCategory(categoryId);
            if (categoryRecord != null)
                return categoryRecord.getParent();
        }
        return 0;
    }

    public static String getCategoryParentName(int categoryId) {

        String parentCategory = "";
        RealmResults<CategoryRecord> results = DaoManager.getInstance().getById(CategoryRecord.class, CategoryRecord.FIELD_CATEGORY_ID, categoryId);
        if (results != null && !results.isEmpty()) {
            CategoryRecord category = results.first();
            int parent = category.getParent();
            RealmResults<CategoryRecord> parents = DaoManager.getInstance().getById(CategoryRecord.class, CategoryRecord.FIELD_CATEGORY_ID, parent);
            if (parents != null && !parents.isEmpty()) {
                parentCategory = parents.first().getName();
            }
        }
        return parentCategory;
    }

    public TYPE getType() {
        checkValidity();
        return type;
    }

    public void setType(TYPE type) {
        this.type = type;
    }

    public int getCategoryId() {
        checkValidity();
        return categoryId;
    }

    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public int getParent() {
        return parent;
    }

    public void setParent(int parent) {
        this.parent = parent;
    }

    public String getName() {
        checkValidity();
        if (LocaleManager.INSTANCE.isFr()) {
            return this.label.getFrLabel();
        } else {
            return this.label.getArLabel();
        }
    }

    public String getAnalyticsName() {
        checkValidity();
        return label.getFrLabel();
    }

    public CategoryLabel getLabel() {
        return label;
    }

    public void setLabel(CategoryLabel label) {
        this.label = label;
    }

    public RealmList<AdTypeRecord> getAdTypes() {
        checkValidity();
        return adTypes;
    }

    public void setAdTypes(RealmList<AdTypeRecord> adTypes) {
        this.adTypes = adTypes;
    }

    public int getOrder() {
        checkValidity();
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public void checkValidity() {
        try {
            if (!isValid()) {
                Utils.rebirthApp(Avito.Companion.getContext());
            }
        } catch (IllegalStateException e) {
            Utils.rebirthApp(Avito.Companion.getContext());
        }
    }

    public enum TYPE {
        MEDIA,
        CATEGORY
    }
}
