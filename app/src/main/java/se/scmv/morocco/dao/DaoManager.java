package se.scmv.morocco.dao;

import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import io.realm.Realm;
import io.realm.RealmObject;
import io.realm.RealmQuery;
import io.realm.RealmResults;
import se.scmv.morocco.utils.Log;

/**
 * Created by amine on 09/01/15.
 * Updated by Soufiane on 10/06/16
 */
public class DaoManager {

    /**
     * Method that returns all the AI categories_fr
     * AI categories_fr are all the categories_fr except "Véhicules professionels" which has the id "2070"
     *
     * @param clazz
     * @param <T>
     * @return
     */
    private final int CATEGORY_VEHICULES_PROFESSIONELS = 2070;
    private Realm realm;

    private DaoManager() {
        // The Realm Config is done on the Avito.java class
        try {
            realm = Realm.getDefaultInstance();


        } catch (Exception e) {
            // This should not happen
            e.printStackTrace();
            Log.e("DAO-MANAGER", e.getMessage());
        }
    }

    public static DaoManager getInstance() {
        return new DaoManager();
    }

    public boolean isValid() {
        return !realm.isEmpty();
    }

    /**
     * Returns all data of the corresponding table/class.
     *
     * @param clazz class/table
     * @return data
     */
    public <T extends RealmObject> RealmResults<T> listAll(Class<T> clazz) {
        if (realm == null) {
            realm = Realm.getDefaultInstance();
        }

        return realm.where(clazz).findAll();
    }

    public <T extends RealmObject> void insertAll(Collection<T> objects) {
        realm.beginTransaction();
        realm.insert(objects);
        realm.commitTransaction();
    }

    public <T extends RealmObject> List<T> copyFromRealm(RealmResults<T> realmResults) {
        if (realm == null) {
            realm = Realm.getDefaultInstance();
        }

        return realm.copyFromRealm(realmResults);
    }

    public RealmResults<CategoryRecord> listAllSearchCategories() {
        if (realm == null) {
            realm = Realm.getDefaultInstance();
        }

        RealmQuery<CategoryRecord> query = realm.where(CategoryRecord.class);
        RealmResults<CategoryRecord> categoryRecords = query.findAll();
        HashMap<Integer, Integer> childAndParentMap = new HashMap<>();

        for (CategoryRecord categoryRecord : categoryRecords) {
            if (categoryRecord.getCategoryId() != 0 && categoryRecord.getParent() != 0)
                childAndParentMap.put(categoryRecord.getCategoryId(), categoryRecord.getParent());
        }
        for (int categoryId : childAndParentMap.values()) {
            if (childAndParentMap.containsKey(categoryId)) {
                query.notEqualTo(CategoryRecord.FIELD_CATEGORY_ID, categoryId);
            }
        }

        return query.findAll();
    }

    public RealmResults<CategoryRecord> listAllAiCategories() {
        if (realm == null) {
            realm = Realm.getDefaultInstance();
        }

        RealmQuery<CategoryRecord> query = realm.where(CategoryRecord.class);
        RealmResults<CategoryRecord> categoryRecords = query.findAll();
        HashMap<Integer, Integer> childAndParentMap = new HashMap<>();

        for (CategoryRecord categoryRecord : categoryRecords) {
            if (categoryRecord.getCategoryId() != 0 && categoryRecord.getParent() != 0)
                childAndParentMap.put(categoryRecord.getCategoryId(), categoryRecord.getParent());
        }
        for (int categoryId : childAndParentMap.values()) {
            if (childAndParentMap.containsKey(categoryId)) {
                query.notEqualTo(CategoryRecord.FIELD_CATEGORY_ID, categoryId);
            }
        }
        //TODO CONF Temp
        query.notEqualTo(CategoryRecord.FIELD_CATEGORY_ID, 1030);
        query.notEqualTo(CategoryRecord.FIELD_CATEGORY_ID, 1100);

        return query.findAll();
    }

    public RealmResults<CategoryRecord> listShopAiCategory(int storeCat) {
        if (realm == null) {
            realm = Realm.getDefaultInstance();
        }

        RealmQuery<CategoryRecord> query = realm.where(CategoryRecord.class);
        int boundaryX = ((storeCat / 1000)) * 1000;
        int boundaryY = (((storeCat / 1000) + 1) * 1000) - 1;
        query.between(CategoryRecord.FIELD_CATEGORY_ID, boundaryX, boundaryY);

        RealmResults<CategoryRecord> categoryRecords = query.findAll();
        HashMap<Integer, Integer> childAndParentMap = new HashMap<>();

        for (CategoryRecord categoryRecord : categoryRecords) {
            if (categoryRecord.getCategoryId() != 0 && categoryRecord.getParent() != 0)
                childAndParentMap.put(categoryRecord.getCategoryId(), categoryRecord.getParent());
        }
        for (int categoryId : childAndParentMap.values()) {
            if (childAndParentMap.containsKey(categoryId)) {
                query.notEqualTo(CategoryRecord.FIELD_CATEGORY_ID, categoryId);
            }
        }
        query.notEqualTo(CategoryRecord.FIELD_CATEGORY_ID, 1030);
        query.notEqualTo(CategoryRecord.FIELD_CATEGORY_ID, 1100);
        return query.findAll();
    }
    /**
     * Returns all data, sorted, of the corresponding table/class.
     *
     * @param clazz               class/table
     * @param fieldNameToSortWith fieldName to use for sorting
     * @return data
     */
    public <T extends RealmObject> RealmResults<T> listAllSorted(Class<T> clazz, String fieldNameToSortWith) {
        if (realm == null) {
            realm = Realm.getDefaultInstance();
        }

        return realm.where(clazz).findAll().sort(fieldNameToSortWith);
    }

    /**
     * This is a method which represents a Realm instance WHERE clause
     *
     * @param clazz  table/class/model
     * @param args   column to apply where clause
     * @param values values to check instance where clause
     * @return matching items
     */
    public <T extends RealmObject> RealmResults<T> get(Class<T> clazz, String[] args, Object[] values) {
        if (realm == null) {
            realm = Realm.getDefaultInstance();
        }

        RealmQuery<T> query = realm.where(clazz);
        for (int i = 0; i < args.length; i++) {
            if (values[i] instanceof Boolean)
                query.equalTo(args[i], (Boolean) values[i]);
            else if (values[i] instanceof Integer)
                query.equalTo(args[i], (Integer) values[i]);
            else if (values[i] instanceof Long)
                query.equalTo(args[i], (Long) values[i]);
            else if (values[i] instanceof Short)
                query.equalTo(args[i], (Short) values[i]);
            else if (values[i] instanceof Float)
                query.equalTo(args[i], (Float) values[i]);
            else if (values[i] instanceof Double)
                query.equalTo(args[i], (Double) values[i]);
            else if (values[i] instanceof String)
                query.equalTo(args[i], (String) values[i]);
            else if (values[i] instanceof Date)
                query.equalTo(args[i], (Date) values[i]);

        }
        return query.findAll();
    }

    /**
     * Same as the previous method, only this time the id is a non numeric string
     *
     * @param clazz
     * @param arg
     * @param id
     * @param <T>
     * @return
     */
    public <T extends RealmObject> RealmResults<T> getById(Class<T> clazz, String arg, String id) {
        if (realm == null) {
            realm = Realm.getDefaultInstance();
        }

        RealmQuery<T> query = realm.where(clazz);
        query.equalTo(arg, id);
        return query.findAll();
    }

    public <T extends RealmObject> RealmResults<T> getById(Class<T> clazz, String arg, long id) {
        if (realm == null) {
            realm = Realm.getDefaultInstance();
        }

        RealmQuery<T> query = realm.where(clazz);
        query.equalTo(arg, id);
        return query.findAll();
    }

    public <T extends RealmObject> RealmResults<T> getByIds(Class<T> clazz, String[] args, String[] ids) {
        if (realm == null) {
            realm = Realm.getDefaultInstance();
        }

        RealmQuery<T> query = realm.where(clazz)
                .equalTo(args[0], ids[0])
                .equalTo(args[1], ids[1]);
        return query.findAll();
    }

    public void deleteRecords(Class... realmClasses) {
        for (Class realmClass : realmClasses) {
            deleteRecord(realmClass);
        }
    }

    public <T extends RealmObject> void deleteRecord(T t) {
        if (realm == null) {
            realm = Realm.getDefaultInstance();
        }

        if (!realm.isInTransaction()) {
            realm.beginTransaction();
        }
        t.deleteFromRealm();
        realm.commitTransaction();
    }

    public <T extends RealmObject> void deleteRecord(Class<T> clazz) {
        if (realm == null) {
            realm = Realm.getDefaultInstance();
        }

        if (!realm.isInTransaction()) {
            realm.beginTransaction();
        }
        realm.delete(clazz);
        realm.commitTransaction();
    }

    public void beginTransaction() {
        if (realm == null) {
            realm = Realm.getDefaultInstance();
        }

        realm.beginTransaction();
    }

    public <T extends RealmObject> T createObject(Class<T> clazz) {
        if (realm == null) {
            realm = Realm.getDefaultInstance();
        }

        return realm.createObject(clazz);
    }

    public <T extends RealmObject> void insertOrUpdate(T Object) {
        if (realm == null) {
            realm = Realm.getDefaultInstance();
        }
        realm.insertOrUpdate(Object);

    }

    public void commitTransaction() {
        if (realm == null) {
            realm = Realm.getDefaultInstance();
        }

        realm.commitTransaction();
    }

    public void cancelTransaction() {
        if (realm == null) {
            realm = Realm.getDefaultInstance();
        }

        realm.cancelTransaction();
    }

    public void close() {
        if (realm == null) {
            realm = Realm.getDefaultInstance();
        }

        realm.close();
    }

    /**
     * Returns rows count of a table/class.
     *
     * @param clazz class/table
     * @return rows count
     */
    public <T extends RealmObject> long getCount(Class<T> clazz) {
        if (realm == null) {
            realm = Realm.getDefaultInstance();
        }

        return realm.where(clazz).count();
    }

    public <T extends RealmObject> void insertOrUpdateAll(Collection<T> objects) {
        realm.beginTransaction();
        realm.insertOrUpdate(objects);
        realm.commitTransaction();
    }

}
