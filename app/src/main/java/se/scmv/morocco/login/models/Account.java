package se.scmv.morocco.login.models;

import android.content.Context;

import androidx.annotation.Keep;

import com.google.firebase.analytics.FirebaseAnalytics;
import com.google.gson.annotations.SerializedName;

import se.scmv.morocco.Avito;
import se.scmv.morocco.utils.FlavorUtils;
import se.scmv.morocco.utils.SharedGson;
import se.scmv.morocco.utils.Utils;

@Keep
public class Account  {

    public static final String ACCOUNT_PREF = "account";


    @SerializedName("accountId")
    private int accountId = -1;
    @SerializedName("name")
    private String name;
    @SerializedName("email")
    private String email = "";
    @SerializedName("phone")
    private String phone;
    @SerializedName("password")
    private String password;
    @SerializedName("lang")
    private String lang = "fr";
    @SerializedName("phoneHidden")
    private int phoneHidden;
    @SerializedName("accountType")
    private int accountType;
    @SerializedName("region")
    private int city = 0;
    @SerializedName("activeSince")
    private String activeSince = "";

    /**
     * Get the current Account  logged in
     *
     * @param context context for the getStringPreference method
     * @return the account model parsed from the saved JSON file
     */
    public static Account getCurrentAccount(Context context) {

        Account accountInstance = null;
        String json = Utils.getStringPreference(context, ACCOUNT_PREF);
        if (json != null) {
            accountInstance = SharedGson.get().fromJson(json, Account.class);
        }

        return accountInstance;
    }

    public static void setCurrentAccount(Account account) {
        Utils.savePreference(Avito.Companion.getContext(), Account.ACCOUNT_PREF, SharedGson.get().toJson(account));
        FirebaseAnalytics.getInstance(Avito.Companion.getContext()).setUserProperty("email", account.email);
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public int getCity() {
        return city;
    }

    public void setCity(int city) {
        this.city = city;
    }

    public int getAccountId() {
        return accountId;
    }

    public void setAccountId(int accountId) {
        this.accountId = accountId;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public int isPhoneHidden() {
        return phoneHidden;
    }

    public void setPhoneHidden(int phoneHidden) {
        this.phoneHidden = phoneHidden;
    }

    public String getActiveSince() {
        return activeSince;
    }

    public void setActiveSince(String activeSince) {
        this.activeSince = activeSince;
    }

    public int getAccountType() {
        if (FlavorUtils.Companion.isShopFlavor()) {
            return 1;
        }
        return accountType;
    }

    public void setAccountType(int accountType) {
        this.accountType = accountType;
    }

    public boolean isValid() {
        return name != null && phone != null && email != null;
    }
}