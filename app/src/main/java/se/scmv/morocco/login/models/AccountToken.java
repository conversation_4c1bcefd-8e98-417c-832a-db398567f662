package se.scmv.morocco.login.models;

import android.content.Context;
import android.util.Base64;

import com.google.firebase.crashlytics.FirebaseCrashlytics;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import se.scmv.morocco.utils.FlavorUtils;
import se.scmv.morocco.utils.SharedGson;
import se.scmv.morocco.utils.Utils;

/**
 * Created by <PERSON><PERSON> on 3/7/16.
 * Refactored to support the new graphql API Login by <PERSON><PERSON><PERSON>i on 29/11/22
 */
public class AccountToken {

    private static final String TOKEN_KEY = "account_token";
    private static boolean SESSION_IS_EXPIRED = false;
    private static boolean TOKEN_INVALID = false;
    private String token = "";
    private String type = "jwt";
    private long expiration;
    private int accountId = -1;
    private int storeId = -1;
    private String email = "";
    private List<String> roles = new ArrayList<>();
    private Map<String, Object> allowedAccess = new HashMap<>();
    private boolean isStore = false;
    private String uuId;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    private void extractEmailFrom(JSONObject json) {
        String email = "";
        try {
            email = json.getString("email");
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (IllegalArgumentException ex) {
            FirebaseCrashlytics.getInstance().log(ex.getMessage() + " : Used token = " + token);
        }
        setEmail(email);

    }

    private void extractExpFrom(JSONObject json) {
        int exp = 0;
        try {
            exp = json.getInt("exp");
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (IllegalArgumentException ex) {
            FirebaseCrashlytics.getInstance().log(ex.getMessage() + " : Used token = " + token);
        }
        setExpiration(exp);
    }

    private void extractTypeFrom(JSONObject json) {
        try {
            setType(json.getString("typ").toLowerCase());
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (IllegalArgumentException ex) {
            FirebaseCrashlytics.getInstance().log(ex.getMessage() + " : Used token = " + token);
        }
    }

    public void extractRolesFrom(JSONObject json) {
        try {
            JSONArray roles = json.getJSONArray("roles");
            for (int i = 0; i < roles.length(); i++) {
                this.roles.add(roles.get(i).toString());
            }
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (IllegalArgumentException ex) {
            FirebaseCrashlytics.getInstance().log(ex.getMessage() + " : Used token = " + token);
        }
    }

    public void extractAllowedAccessFrom(JSONObject json) {
        try {
            if (json.has("allowedAccess")) {
                JSONObject allowedAccess = json.getJSONObject("allowedAccess");
                Iterator itr = allowedAccess.keys();
                while (itr.hasNext()) {
                    String key = (String) itr.next();
                    this.allowedAccess.put(key, allowedAccess.get(key));
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (IllegalArgumentException ex) {
            FirebaseCrashlytics.getInstance().log(ex.getMessage() + " : Used token = " + token);
        }
    }

    public void setUserIdFrom(JSONObject json) {
        int userId = 0;
        try {
            userId = json.getInt("sub");
            for (String role : roles) {
                if (role.contains("STORE")) {
                    isStore = true;
                    break;
                }
            }
            if (isStore) {
                setStoreId(userId);
                setUuId("S" + userId);
            } else {
                setAccountId(userId);
                setUuId("A" + userId);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (IllegalArgumentException ex) {
            FirebaseCrashlytics.getInstance().log(ex.getMessage() + " : Used token = " + token);
        }
    }

    private JSONObject extractJsonSplitFromToken(String tk, int index) {
        String[] splitToken = tk.split("\\.");
        JSONObject json = new JSONObject();
        try {
            String str = new String(Base64.decode(splitToken[index], Base64.URL_SAFE));
            json = new JSONObject(str);
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (IllegalArgumentException ex) {
            FirebaseCrashlytics.getInstance().log(ex.getMessage() + " : Used token = " + tk + " : SplitToken : " + Arrays.toString(splitToken));
        }

        return json;
    }

    public static boolean isProfessionalAccount(AccountToken tk) {
        return tk.isStore();
    }

    public static AccountToken getCurrentToken(Context context) {
        String json = Utils.getStringPreference(context, TOKEN_KEY);
        return json != null ? SharedGson.get().fromJson(json, AccountToken.class) : null;
    }

    /**
     * Retrieves the allowed access permissions associated with the current account token.
     *
     * @param context The context object providing access to application-specific resources and classes.
     * @return A Map containing allowed access permissions, where keys are Strings representing
     * access types and values are Objects representing permission details.
     */
    public static Map<String, Object> getAllowedAccess(Context context) {
        AccountToken currentToken = getCurrentToken(context);
        return currentToken != null ? currentToken.allowedAccess : new HashMap<>();
    }

    public static void deleteCurrentToken(Context context) {
        Utils.removePreference(context, TOKEN_KEY);
    }

    public static boolean isLoggedIn(Context context) {
        return getCurrentToken(context) != null;
    }

    public static boolean isSessionExpired() {
        return SESSION_IS_EXPIRED;
    }

    ///TODO: Used now in chat -> to be used in the entire app
    public static boolean isSessionExpired(Context context) {
        AccountToken token = AccountToken.getCurrentToken(context);
        return token == null || token.expiration < (System.currentTimeMillis() / 1000L);
    }

    public static boolean isTokenInvalid() {
        return TOKEN_INVALID;
    }

    public static void setTokenIsInvalid(boolean isTokenInvalid) {
        TOKEN_INVALID = isTokenInvalid;
    }

    public static void setSessionIsExpired(boolean sessionIsExpired) {
        SESSION_IS_EXPIRED = sessionIsExpired;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getRequestToken() {
        return "Bearer " + token;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<String> getRoles() {
        return roles;
    }

    public long getExpiration() {
        return expiration;
    }

    public void setAllowedAccess(Map<String, Object> allowedAccess) {
        this.allowedAccess = allowedAccess;
    }

    public Map<String, Object> getAllowedAccess() {
        return allowedAccess;
    }

    public void setExpiration(long expiration) {
        this.expiration = expiration;
    }

    public int getAccountId() {
        if (FlavorUtils.Companion.isShopFlavor()) return storeId;
        return accountId;
    }

    public void setAccountId(int accountId) {
        this.accountId = accountId;
    }

    public String getUuId() {
        return uuId;
    }

    public void setUuId(String uuId) {
        this.uuId = uuId;
    }

    public int getStoreId() {
        return storeId;
    }

    public void setStoreId(int storeId) {
        this.storeId = storeId;
    }

    public boolean isStore() {
        return (uuId.startsWith("S") || storeId != -1);
    }

    public void saveToken(Context context, String token) {
        JSONObject json = extractJsonSplitFromToken(token, 1);
        extractEmailFrom(json);
        extractRolesFrom(json);
        extractAllowedAccessFrom(json);
        extractExpFrom(json);
        setUserIdFrom(json);
        this.token = token;
        json = extractJsonSplitFromToken(token, 0);
        extractTypeFrom(json);
        Utils.savePreference(context, TOKEN_KEY, SharedGson.get().toJson(this));
    }
}
