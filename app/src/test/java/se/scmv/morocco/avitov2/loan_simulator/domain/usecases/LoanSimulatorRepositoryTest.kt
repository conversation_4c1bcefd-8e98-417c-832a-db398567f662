package se.scmv.morocco.avitov2.loan_simulator.domain.usecases

import com.google.gson.Gson
import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import junit.framework.TestCase.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import se.scmv.morocco.avitov2.loan_simulator.data.repositories.LoanSimulatorRepositoryImpl
import se.scmv.morocco.avitov2.loan_simulator.domain.models.LoanSimulatorConfig
import se.scmv.morocco.avitov2.loan_simulator.domain.repositories.LoanSimulatorRepository
import se.scmv.morocco.domain.repositories.RemoteConfigRepository


@RunWith(JUnit4::class)
class LoanSimulatorRepositoryTest {

    @MockK
    lateinit var remoteConfigRepository: RemoteConfigRepository

    private val useCase: LoanSimulatorRepository by lazy {
        LoanSimulatorRepositoryImpl(remoteConfigRepository, Gson())
    }

    @Before
    fun setUp() {
        MockKAnnotations.init(this, relaxUnitFun = true)
    }

    private val configJson = """
        {
          "loan_durations": [
            1, 2
          ],
          "default_duration": 7,
          "interest_percentage": 1.2,
          "loan_categories": [
            {
              "category": "1",
              "type": "sell"
            },
            {
              "category": "2",
              "type": "sell"
            },
          ],
          "redirection_url": "https://example.com"
        }
    """.trimIndent()

    @Test
    fun `invoke, success`() {
        // GIVEN
        val adCategoryId = "1"
        val expectedConfig = LoanSimulatorConfig(
            loanDurations = listOf(1, 2),
            defaultDuration = 7,
            interestPercentage = 1.2,
            redirectionUrl = "https://example.com"
        )
        coEvery {
            remoteConfigRepository.getString(RemoteConfigRepository.LOAN_SIMULATOR_CONFIG_KEY)
        } returns configJson

        // WHEN
        val result = useCase.getConfig(adCategoryId, "sell")

        // THEN
        assertEquals(expectedConfig, result)
    }

    @Test
    fun `invoke, success but unavailable for this category id`() {
        // GIVEN
        val adCategoryId = "3"
        val expectedConfig = null
        coEvery {
            remoteConfigRepository.getString(RemoteConfigRepository.LOAN_SIMULATOR_CONFIG_KEY)
        } returns configJson

        // WHEN
        val result = useCase.getConfig(adCategoryId, "sell")

        // THEN
        assertEquals(expectedConfig, result)
    }

    @Test
    fun `invoke, success but unavailable for type`() {
        // GIVEN
        val adCategoryId = "2"
        val expectedConfig = null
        coEvery {
            remoteConfigRepository.getString(RemoteConfigRepository.LOAN_SIMULATOR_CONFIG_KEY)
        } returns configJson

        // WHEN
        val result = useCase.getConfig(adCategoryId, "buy")

        // THEN
        assertEquals(expectedConfig, result)
    }

    @Test
    fun `invoke, error`() {
        // GIVEN
        val adCategoryId = "3"
        val expectedError = mockk<Throwable>()
        coEvery {
            remoteConfigRepository.getString(RemoteConfigRepository.LOAN_SIMULATOR_CONFIG_KEY)
        } throws expectedError

        // WHEN
        val result = useCase.getConfig(adCategoryId, "sell")

        // THEN
        assertEquals(null, result)
    }
}