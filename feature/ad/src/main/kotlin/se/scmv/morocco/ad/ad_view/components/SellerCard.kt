package se.scmv.morocco.ad.ad_view.components

import android.content.res.Configuration
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import se.scmv.morocco.ad.R
import se.scmv.morocco.designsystem.theme.dimens

@Composable
fun SellerCard(
    sellerName: String,
    isVerified: Boolean,
    sellerLogo: String?,
    isShop: Boolean,
    isAccountAd: Boolean,
    memberSince: kotlinx.datetime.LocalDateTime?,
    location: String?,
    website: String?,
    whatsAppAllowed: Boolean,
    hasPhone: Boolean,
    onWhatsAppClick: () -> Unit,
    onWebsiteClick: () -> Unit,
    onChatClick: () -> Unit,
    onCallClick: () -> Unit,
    onShowStoreClick: () -> Unit,
    onBoostClick: () -> Unit,
) {
    // Cache painter resources to avoid repeated loading
    val profilePlaceholderPainter = painterResource(R.drawable.ic_profile_placeholder)
    val shopIconPainter = painterResource(R.drawable.ic_shop)
    val arrowRightPainter = painterResource(R.drawable.ic_arrow_right)
    val calendarPainter = painterResource(R.drawable.ic_stats_calendar)
    val locationPainter = painterResource(R.drawable.ic_location_on_24)
    val websitePainter = painterResource(R.drawable.ic_www_circle)
    val rocketPainter = painterResource(R.drawable.outline_rocket_launch_16)
    val whatsappPainter = painterResource(R.drawable.ic_whatsapp)
    val messagePainter = painterResource(R.drawable.ic_message)
    val callPainter = painterResource(R.drawable.ic_call_grid)

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = MaterialTheme.dimens.default)
            .background(MaterialTheme.colorScheme.background)
            .padding(vertical = MaterialTheme.dimens.default)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween,
            modifier = Modifier.fillMaxWidth()
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.weight(1f, fill = false)
            ) {
                if (sellerLogo.isNullOrEmpty()) {
                    Image(
                        painter = profilePlaceholderPainter,
                        contentDescription = "Profile Image",
                        modifier = Modifier
                            .size(48.dp)
                            .clip(CircleShape)
                            .background(Color.LightGray)
                    )
                } else {
                    AsyncImage(
                        model = sellerLogo,
                        contentDescription = "Profile Image",
                        modifier = Modifier
                            .size(48.dp)
                            .clip(CircleShape)
                            .background(Color.LightGray),
                        contentScale = ContentScale.Crop,
                        placeholder = profilePlaceholderPainter,
                        error = profilePlaceholderPainter
                    )
                }

                Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))
                Column(verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.tiny)) {
                    if (isShop) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier
                                .height(IntrinsicSize.Min)
                                .background(
                                    color = Color(0xFFfc942d),
                                    shape = RoundedCornerShape(MaterialTheme.dimens.small)
                                )
                                .padding(
                                    horizontal = MaterialTheme.dimens.medium,
                                    vertical = MaterialTheme.dimens.tiny
                                )
                        ) {
                            Icon(
                                painter = shopIconPainter,
                                contentDescription = "Boutique",
                                tint = Color.White,
                                modifier = Modifier.size(MaterialTheme.dimens.betweenRegularDefault)
                            )
                            Spacer(modifier = Modifier.width(MaterialTheme.dimens.small))
                            Text(
                                text = stringResource(R.string.store_lable),
                                style = TextStyle(
                                    fontSize = 10.sp,
                                    color = Color.White,
                                    fontWeight = FontWeight.Bold,
                                    lineHeight = 12.sp
                                ),
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis
                            )
                        }
                    }
                    SellerNameVerifiedRow(sellerName, isVerified)
                }
            }


            if (isShop)
                Icon(
                    painter = arrowRightPainter,
                    contentDescription = "See More",
                    tint = Color.Gray,
                    modifier = Modifier
                        .size(MaterialTheme.dimens.bigger)
                        .clickable {
                            onShowStoreClick()
                        }
                )
        }

        if (memberSince != null) {
            Spacer(modifier = Modifier.height(MaterialTheme.dimens.regular))
            Row(verticalAlignment = Alignment.CenterVertically) {
                Icon(
                    painter = calendarPainter,
                    contentDescription = "Date",
                    tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f),
                    modifier = Modifier.size(MaterialTheme.dimens.default)
                )
                Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))
                Text(
                    text = "${stringResource(R.string.member_since)} ${memberSince.year}",
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f),
                    fontSize = 12.sp
                )
            }
        }


        if (location != null) {
            Spacer(modifier = Modifier.height(MaterialTheme.dimens.small))
            Row(verticalAlignment = Alignment.CenterVertically) {
                Icon(
                    painter = locationPainter,
                    contentDescription = "Location",
                    tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f),
                    modifier = Modifier.size(MaterialTheme.dimens.default)
                )
                Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))

                Text(
                    text = location,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f),
                    fontSize = 12.sp
                )
            }
        }

        if (!website.isNullOrBlank()) {
            Spacer(modifier = Modifier.height(MaterialTheme.dimens.small))
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.clickable {
                    onWebsiteClick()
                }
            ) {
                Image(
                    painter = websitePainter,
                    contentDescription = "website",
                    modifier = Modifier.size(MaterialTheme.dimens.default)
                )
                Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))

                Text(
                    text = website,
                    color = Color(0xFF1A73E8),
                    fontSize = 12.sp,
                    textDecoration = TextDecoration.Underline
                )
            }
        }


        Spacer(modifier = Modifier.height(MaterialTheme.dimens.default))

        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
            modifier = Modifier.fillMaxWidth()
        ) {

            if (isAccountAd) {
                Button(
                    onClick = onBoostClick,
                    modifier = Modifier
                        .weight(1f) // Equal width
                        .fillMaxWidth(), // Ensures button fills the weight allocation
                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFFFC942D)), // Background color
                    shape = RoundedCornerShape(MaterialTheme.dimens.regular),
                    contentPadding = PaddingValues(
                        horizontal = MaterialTheme.dimens.default,
                        vertical = MaterialTheme.dimens.regular
                    )
                ) {

                    Icon(
                        painter = rocketPainter,
                        contentDescription = "Time Icon",
                        modifier = Modifier.size(MaterialTheme.dimens.default),
                    )


                    Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))
                    Text(
                        text = stringResource(R.string.boost_Ad),
                        color = Color.White,
                        style = MaterialTheme.typography.bodyMedium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            } else {
                if (whatsAppAllowed && hasPhone)
                    CircularIconButton(
                        whatsappPainter,
                        iconColor = Color(0xFF64c571),
                        backgroundColor = Color(0xFFedf9f0),
                        onClick = onWhatsAppClick
                    )

                CircularIconButton(
                    messagePainter,
                    iconColor = Color(0xFF2563EB),
                    backgroundColor = Color(0xFFEAF0FF),
                    onClick = onChatClick
                )

                if (hasPhone)
                    Button(
                        onClick = onCallClick,
                        modifier = Modifier
                            .height(38.dp)
                            .fillMaxWidth(),
                        shape = RoundedCornerShape(MaterialTheme.dimens.medium),
                        colors = ButtonDefaults.buttonColors()
                            .copy(containerColor = Color(0xFF2563EB)),
                        contentPadding = PaddingValues(
                            horizontal = MaterialTheme.dimens.default,
                            vertical = MaterialTheme.dimens.medium
                        )
                    ) {
                        Icon(
                            painter = callPainter,
                            contentDescription = "Call",
                            tint = Color.White,
                            modifier = Modifier.size(MaterialTheme.dimens.big)
                        )
                        Spacer(modifier = Modifier.width(MaterialTheme.dimens.small))
                        Text(
                            text = stringResource(R.string.common_call),
                            color = Color.White,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.SemiBold
                        )
                    }
            }

        }
    }
}

@Preview(showBackground = true, uiMode = Configuration.UI_MODE_NIGHT_YES)
@Composable
fun PreviewCustomItemRow() {
    SellerCard(
        sellerName = "LOCATION DE VOITURE NATIO",
        isVerified = true,
        sellerLogo = "LOCATION DE VOITURE NATIO",
        website = "www.avito.ma",
        isShop = true,
        isAccountAd = true,
        memberSince = null,
        location = "Tanger Tetouan Martil Mdiq et Fnidak",
        onWhatsAppClick = { /* Handle WhatsApp */ },
        onChatClick = { /* Handle Chat */ },
        onCallClick = { /* Handle Call */ },
        onWebsiteClick = {

        },
        onShowStoreClick = {

        },
        hasPhone = true,
        whatsAppAllowed = true,
        onBoostClick = {}
    )
}