package se.scmv.morocco.ad.ad_view.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import se.scmv.morocco.ad.R
import se.scmv.morocco.designsystem.theme.dimens
import java.time.LocalDate
import java.time.YearMonth
import java.time.format.TextStyle
import java.time.temporal.ChronoUnit
import java.util.Locale

@Composable
fun CalendarScreen(
    reservedDates: List<String>?,
    price: Int?,
    cityName: String?
) {
    var startDate by remember { mutableStateOf<LocalDate?>(null) }
    var endDate by remember { mutableStateOf<LocalDate?>(null) }

    val reserved = reservedDates?.map { LocalDate.parse(it) } ?: emptyList()

    val selectedDays = remember(startDate, endDate) {
        if (startDate != null && endDate != null) {
            ChronoUnit.DAYS.between(startDate, endDate)?.toInt()
        } else null
    }
    val totalPrice = selectedDays?.let { price?.times(it) }

    Column(
        modifier = Modifier.padding(MaterialTheme.dimens.default)
    ) {
        if (startDate != null && endDate != null) {
            Text("$selectedDays ${stringResource(R.string.nights)} - $cityName")

        } else {
            Text(
                text = "${stringResource(R.string.availability)} - ${cityName.orEmpty()}",
                fontSize = 16.sp,
                fontWeight = FontWeight.SemiBold,
                modifier = Modifier.padding(bottom = MaterialTheme.dimens.medium)
            )
        }

        CalendarView(
            reservedDates = reserved,
            startDate = startDate,
            endDate = endDate,
            onDateSelected = { date ->
                if (startDate == null || (startDate != null && endDate != null)) {
                    startDate = date
                    endDate = null
                } else {
                    if (date.isAfter(startDate)) {
                        endDate = date
                    } else {
                        startDate = date
                    }
                }
            }
        )

        Spacer(modifier = Modifier.height(MaterialTheme.dimens.small))

        if (startDate != null && endDate != null) {
            totalPrice?.let {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = MaterialTheme.dimens.medium),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "${stringResource(R.string.total_)}  ${
                            stringResource(
                                R.string.common_price_with_currency,
                                it
                            )
                        }",
                        style = MaterialTheme.typography.bodyLarge
                    )

                    Text(
                        text = stringResource(R.string.common_clear),
                        color = MaterialTheme.colorScheme.primary,
                        modifier = Modifier
                            .clickable {
                                startDate = null
                                endDate = null
                            }
                            .padding(MaterialTheme.dimens.medium)
                    )
                }
            }
        } else {
            Text(stringResource(R.string.select_arrival_date))
        }
    }
}

@Composable
fun CalendarView(
    reservedDates: List<LocalDate>,
    startDate: LocalDate?,
    endDate: LocalDate?,
    onDateSelected: (LocalDate) -> Unit
) {
    var displayedMonth by remember { mutableStateOf(YearMonth.now()) }
    val today = LocalDate.now()

    Column {
        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = MaterialTheme.dimens.medium)
        ) {
            Button(onClick = { displayedMonth = displayedMonth.minusMonths(1) }) {
                Text("<")
            }
            Text(
                "${
                    displayedMonth.month.getDisplayName(
                        TextStyle.FULL,
                        Locale.getDefault()
                    ).lowercase().replaceFirstChar { it.uppercase() }
                } ${displayedMonth.year}",
                style = MaterialTheme.typography.titleMedium
            )
            Button(onClick = { displayedMonth = displayedMonth.plusMonths(1) }) {
                Text(">")
            }
        }

        // Days of week
        Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
            val daysOfWeek = listOf(
                stringResource(R.string.day_lu),
                stringResource(R.string.day_ma),
                stringResource(R.string.day_me),
                stringResource(R.string.day_je),
                stringResource(R.string.day_ve),
                stringResource(R.string.day_sa),
                stringResource(R.string.day_di)
            )
            daysOfWeek.forEach { day ->
                Text(
                    text = day,
                    style = MaterialTheme.typography.bodySmall,
                    modifier = Modifier.weight(1f),
                    textAlign = TextAlign.Center
                )
            }
        }

        val firstDayOfWeek = displayedMonth.atDay(1).dayOfWeek.ordinal
        val daysInMonth = displayedMonth.lengthOfMonth()
        val totalCells = daysInMonth + firstDayOfWeek

        Box(modifier = Modifier.height(270.dp)) {
            LazyVerticalGrid(
                columns = GridCells.Fixed(7),
                content = {
                    items(totalCells) { index ->
                        if (index < firstDayOfWeek) {
                            Box(
                                modifier = Modifier
                                    .aspectRatio(1f)
                                    .padding(MaterialTheme.dimens.none)
                            )
                        } else {
                            val day = index - firstDayOfWeek + 1
                            val date = displayedMonth.atDay(day)

                            val isReserved = reservedDates.contains(date)
                            val isInPast = date.isBefore(today)

                            val isSelected = startDate?.let { start ->
                                date.isEqual(start) || (endDate?.let { end ->
                                    date.isAfter(start) && date.isBefore(end) || date.isEqual(end)
                                } ?: false)
                            } ?: false

                            val backgroundColor = when {
                                isSelected -> MaterialTheme.colorScheme.primary.copy(alpha = 0.3f)
                                else -> Color.Transparent
                            }

                            Box(
                                modifier = Modifier
                                    .aspectRatio(1f)
                                    .padding(MaterialTheme.dimens.none)
                                    .background(backgroundColor)
                                    .clickable(enabled = !isReserved && !isInPast) {
                                        onDateSelected(date)
                                    }
                            ) {
                                Text(
                                    text = day.toString(),
                                    modifier = Modifier
                                        .fillMaxSize()
                                        .padding(MaterialTheme.dimens.medium),
                                    color = when {
                                        isReserved || isInPast -> Color.Gray
                                        else -> MaterialTheme.colorScheme.onSurface
                                    },
                                    textDecoration = if (isReserved || isInPast) TextDecoration.LineThrough else TextDecoration.None,
                                    textAlign = TextAlign.Center
                                )
                            }
                        }
                    }
                }
            )
        }
    }
}

@Preview(showBackground = true, showSystemUi = false)
@Composable
fun CalendarScreenPreview() {
    CalendarScreen(
        reservedDates = listOf("2025-04-01", "2025-04-02", "2025-04-15"),
        price = 500,
        cityName = stringResource(R.string.share)
    )
}


