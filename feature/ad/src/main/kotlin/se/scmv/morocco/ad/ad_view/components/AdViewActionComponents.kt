package se.scmv.morocco.ad.ad_view.components

import android.widget.Toast
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.compose.rememberAsyncImagePainter
import coil.decode.SvgDecoder
import coil.request.ImageRequest
import kotlinx.datetime.LocalDateTime
import se.scmv.morocco.ad.R
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.designsystem.utils.relativeValue
import se.scmv.morocco.designsystem.utils.toRelativeTimeRes
import se.scmv.morocco.domain.models.AdParam
import se.scmv.morocco.domain.models.AdPrice
import se.scmv.morocco.domain.models.PriceChangeType


@Composable
fun SellerNameVerifiedRow(
    sellerName: String,
    isVerified: Boolean
) {
    Row(verticalAlignment = Alignment.CenterVertically) {
        Text(
            text = sellerName,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium
        )
        if (isVerified) {
            Spacer(modifier = Modifier.width(MaterialTheme.dimens.tiny))
            Icon(
                painter = painterResource(R.drawable.verified_check_icon),
                contentDescription = "Verified Seller",
                tint = Color(0xFFFFA500),
                modifier = Modifier.size(MaterialTheme.dimens.default)
            )
        }
    }
}

@Composable
fun EcommerceRow(
    text: String
) {
    Row(verticalAlignment = Alignment.CenterVertically) {
        Text(
            text = text,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium
        )
    }
}


@Composable
fun CircularIconButton(
    icon: Painter,
    iconColor: Color,
    backgroundColor: Color,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .size(38.dp)
            .clip(CircleShape)
            .background(backgroundColor)
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Icon(
            painter = icon,
            contentDescription = null,
            tint = iconColor,
            modifier = Modifier.size(22.dp)
        )
    }
}


@Composable
fun CategoryRow(
    categoryName: String?,
    categoryType: String?,
    categoryId: String?,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = MaterialTheme.dimens.default)
    ) {
        Divider(color = Color(0xFFE6E6E6), thickness = 0.2.dp)
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = MaterialTheme.dimens.regular),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                modifier = Modifier.weight(1f),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = stringResource(R.string.common_category),
                        fontSize = 12.sp,
                        color = Color(0xFF58606B)
                    )
                    Text(
                        text = "$categoryName $categoryType",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.SemiBold,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }

            if (categoryId != null) {
                val iconUrl = buildIconUrl("category_$categoryId")
                val painter = rememberAsyncImagePainter(
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(iconUrl)
                        .decoderFactory(SvgDecoder.Factory())
                        .build()
                )

                Image(
                    painter = painter,
                    contentDescription = "category icon",
                    modifier = Modifier.size(48.dp)
                )
            }
        }
        Divider(color = Color(0xFFE6E6E6), thickness = 0.2.dp)
    }
}

@Preview(showBackground = true)
@Composable
fun CategoryRowPreview() {
    CategoryRow(
        categoryName = "Appartement",
        categoryType = "à vendre",
        categoryId = "apartment_for_sale" // Example ID
    )
}

fun buildIconUrl(url: String?): String {
    url?.let {
        return "https://assets.avito.ma/icons/svg/" + url + ".svg"
    }
    return "null"
}

@Composable
fun TopBarButton(
    onClick: () -> Unit,
    icon: @Composable () -> Unit,
    modifier: Modifier = Modifier
        .padding(start = MaterialTheme.dimens.regular)
        .size(38.dp),
    backgroundColor: Color = MaterialTheme.colorScheme.background
) {
    Box(
        modifier = modifier
            .clip(CircleShape)
            .background(backgroundColor)
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        icon()
    }
}

@Composable
fun ShareButton(modifier: Modifier = Modifier, onClick: () -> Unit) {
    Row(
        modifier = modifier
            .clip(RoundedCornerShape(50))
            .background(MaterialTheme.colorScheme.background)
            .clickable(onClick = onClick)
            .padding(horizontal = MaterialTheme.dimens.betweenSmallMedium)
            .size(width = 100.dp, height = 38.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(
            MaterialTheme.dimens.medium,
            alignment = Alignment.CenterHorizontally
        )
    ) {
        Icon(
            painter = painterResource(R.drawable.ic_share),
            contentDescription = stringResource(R.string.share),
            tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
            modifier = Modifier.size(MaterialTheme.dimens.big)
        )
        Text(
            text = stringResource(R.string.share),
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
            fontSize = 15.sp
        )
    }
}


@OptIn(ExperimentalFoundationApi::class)
@Composable
fun AdDescription(
    title: String,
    description: String,
    modifier: Modifier = Modifier
        .padding(horizontal = MaterialTheme.dimens.default)

) {
    var showFullDescription by remember { mutableStateOf(false) }
    val context = LocalContext.current
    val clipboardManager = LocalClipboardManager.current
    val hapticFeedback = LocalHapticFeedback.current
    var isTextTruncated by remember { mutableStateOf(false) }

    Column(
        modifier = modifier
            .fillMaxWidth()
            .combinedClickable(
                onClick = { showFullDescription = !showFullDescription },
                onLongClick = {
                    clipboardManager.setText(AnnotatedString(description))
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                    Toast
                        .makeText(context, "Description copied", Toast.LENGTH_SHORT)
                        .show()
                }
            )
    ) {
        Text(
            text = title,
            fontSize = 16.sp,
            fontWeight = FontWeight.SemiBold
        )

        Spacer(modifier = Modifier.height(MaterialTheme.dimens.medium))

        Text(
            text = description,
            fontSize = 14.sp,
            fontWeight = FontWeight.Normal,
            maxLines = if (showFullDescription) Int.MAX_VALUE else 3,
            overflow = TextOverflow.Ellipsis,
            onTextLayout = { textLayoutResult ->
                if (!showFullDescription) {
                    isTextTruncated = textLayoutResult.hasVisualOverflow
                }
            }
        )

        if (isTextTruncated) {
            if (!showFullDescription) {
                Spacer(modifier = Modifier.height(MaterialTheme.dimens.tiny))
                ShowMoreLessButton(
                    onClick = { showFullDescription = true },
                    label = stringResource(R.string.read_more)
                )
            } else {
                Spacer(modifier = Modifier.height(MaterialTheme.dimens.tiny))
                ShowMoreLessButton(
                    onClick = {
                        showFullDescription = false
                    },
                    label = stringResource(R.string.read_less)
                )
            }
        }
    }
}

@Composable
fun ShowMoreLessButton(
    onClick: () -> Unit,
    label: String = stringResource(R.string.read_more)
) {
    OutlinedButton(
        onClick = onClick,
        modifier = Modifier
            .defaultMinSize(minWidth = 1.dp, minHeight = 1.dp),
        shape = RoundedCornerShape(MaterialTheme.dimens.medium),
        border = BorderStroke(1.dp, MaterialTheme.colorScheme.onSurface),
        colors = ButtonDefaults.outlinedButtonColors(
            contentColor = MaterialTheme.colorScheme.onSurface
        ),
        contentPadding = PaddingValues(
            horizontal = MaterialTheme.dimens.regular,
            vertical = MaterialTheme.dimens.betweenSmallMedium
        )
    ) {
        Text(
            label,
            fontSize = 11.sp,
            modifier = Modifier.padding(vertical = MaterialTheme.dimens.none)
        )
    }
}


@Composable
fun AdTitlePriceLocationDate(
    title: String,
    price: AdPrice,
    location: String,
    date: LocalDateTime?,
    isUrgent: Boolean?,
    isHotDeal: Boolean = false,
    discountPercentage: Int?,
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = MaterialTheme.dimens.small)
            .padding(horizontal = MaterialTheme.dimens.default)

    ) {

        Row(verticalAlignment = Alignment.CenterVertically) {
            if (isUrgent == true) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .background(
                            Color(0xFF3A6FF6),
                            RoundedCornerShape(MaterialTheme.dimens.small)
                        )
                        .padding(
                            horizontal = MaterialTheme.dimens.betweenSmallMedium,
                            vertical = MaterialTheme.dimens.none
                        )
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_urgent),
                        contentDescription = "Urgent",
                        tint = Color.White,
                        modifier = Modifier.size(MaterialTheme.dimens.default)
                    )
                    Spacer(modifier = Modifier.width(MaterialTheme.dimens.tiny))
                    Text(
                        text = stringResource(R.string.urgent),
                        color = Color.White,
                        fontSize = 12.sp,
                    )
                }
                Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))
            }
            if (isHotDeal && discountPercentage != null) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .background(
                            Color(0xFFec5a5f),
                            RoundedCornerShape(MaterialTheme.dimens.small)
                        )
                        .padding(
                            horizontal = MaterialTheme.dimens.betweenSmallMedium,
                            vertical = MaterialTheme.dimens.none
                        )
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_promotion),
                        contentDescription = stringResource(R.string.hot_deal),
                        tint = Color.White,
                        modifier = Modifier.size(MaterialTheme.dimens.default)
                    )
                    Spacer(modifier = Modifier.width(MaterialTheme.dimens.tiny))
                    Text(
                        text = "${stringResource(R.string.hot_deal)} -$discountPercentage%",
                        color = Color.White,
                        fontSize = 12.sp,
                    )
                }
                Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))

            }
        }

        Spacer(modifier = Modifier.height(MaterialTheme.dimens.medium))

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.Top
        ) {
            Text(
                text = title,
                fontSize = 15.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.weight(1f)
            )
            if (price is AdPrice.Available) {
                Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))
                Column(horizontalAlignment = Alignment.End) {
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            when (price.changeType) {
                                PriceChangeType.DECREASE -> R.drawable.decrease_green
                                PriceChangeType.INCREASE -> R.drawable.increase_red
                                else -> null
                            }?.let { icon ->
                                Image(
                                    modifier = Modifier.size(MaterialTheme.dimens.bigger),
                                    painter = painterResource(icon),
                                    contentDescription = null,
                                )
                            }
                            Text(
                                text = price.currentWithCurrency,
                                fontSize = 15.sp,
                                fontWeight = FontWeight.Bold,
                                color = MaterialTheme.colorScheme.primary
                            )
                        }

                        if (price.oldWithCurrency != null && isHotDeal) {
                            Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))

                            val annotatedString = AnnotatedString(
                                text = price.oldWithCurrency!!,
                                spanStyles = listOf(
                                    AnnotatedString.Range(
                                        SpanStyle(
                                            textDecoration = TextDecoration.LineThrough
                                        ),
                                        start = 0,
                                        end = price.oldWithCurrency!!.length
                                    )
                                )
                            )
                            Text(
                                text = annotatedString,
                                fontWeight = FontWeight.Bold,
                                color = Color(0xFF6B7280),
                                fontSize = 15.sp
                            )
                        }

                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(MaterialTheme.dimens.large))

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painter = painterResource(R.drawable.ic_location_on_24),
                contentDescription = "",
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(18.dp)
            )
            Spacer(modifier = Modifier.width(MaterialTheme.dimens.small))
            Text(
                text = location,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.primary,
                fontSize = 13.sp
            )

            Spacer(modifier = Modifier.width(MaterialTheme.dimens.default))

            Icon(
                painter = painterResource(R.drawable.ic_time_filled),
                contentDescription = "",
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(18.dp)
            )
            Spacer(modifier = Modifier.width(MaterialTheme.dimens.small))
            Text(
                text = if (date?.toRelativeTimeRes() != null) {
                    stringResource(
                        date.toRelativeTimeRes(),
                        date.relativeValue()
                    )
                } else {
                    ""
                },
                fontSize = 13.sp,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.primary

            )
        }
    }
}


@Composable
fun AdParamsGrid(params: List<AdParam>) {
    var showAllParams by rememberSaveable { mutableStateOf(false) }

    val visibleParams: List<AdParam> =
        if (showAllParams) params else params.take(4) // Show first 4 by default
    val groupedParams: List<List<AdParam>> =
        visibleParams.chunked(2) // Split into rows of 2 items each

    Column(
        Modifier
            .fillMaxWidth()
            .padding(horizontal = MaterialTheme.dimens.default)
            .clickable { showAllParams = !showAllParams } // Toggle on click
    ) {
        groupedParams.forEach { rowParams ->
            Row(
                Modifier
                    .fillMaxWidth()
                    .padding(vertical = MaterialTheme.dimens.medium),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                rowParams.forEach { param ->
                    AdParamItem(param, Modifier.weight(1f))
                }
                if (rowParams.size == 1) {
                    Spacer(modifier = Modifier.weight(1f)) // Maintain grid structure
                }
            }
        }

        if (params.size > 4) {
            Spacer(modifier = Modifier.height(MaterialTheme.dimens.tiny))
            ShowMoreLessButton(
                onClick = { showAllParams = !showAllParams },
                label = if (showAllParams) stringResource(R.string.read_less) else stringResource(R.string.read_more)
            )
        }


    }
}


@Composable
fun AdParamItem(param: AdParam, modifier: Modifier = Modifier) {
    Row(
        modifier
            .fillMaxWidth()
            .padding(vertical = MaterialTheme.dimens.none),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Check if URL is not null or empty
        if (param.iconUrl.isNotEmpty()) {
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(param.iconUrl)
                    .decoderFactory(SvgDecoder.Factory())
                    .crossfade(true)
                    .build(),
                contentDescription = param.label,
                modifier = Modifier.size(MaterialTheme.dimens.bigger),
                colorFilter = ColorFilter.tint(MaterialTheme.colorScheme.onSurface)
            )
        } else {
            Image(
                painter = painterResource(R.drawable.ic_no_image),
                contentDescription = "",
                modifier = Modifier.size(MaterialTheme.dimens.bigger)
            )
        }

        Spacer(modifier = Modifier.width(5.dp))
        // Column for Value and Label
        Column(
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.Start,
            modifier = Modifier.align(Alignment.CenterVertically)
        ) {
            val displayText = when {
                param.value.isNotEmpty() -> param.value
                param.label.isNotEmpty() -> param.label
                else -> null // Both are empty, so we don't display anything
            }

            displayText?.let {
                Text(
                    text = it,
                    fontSize = 14.sp,
                    style = LocalTextStyle.current.copy(lineHeight = TextUnit.Unspecified),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(horizontal = 1.dp, vertical = 1.dp)
                )
            }

            if (param.value.isNotEmpty() && param.label.isNotEmpty()) {
                Text(
                    text = param.label,
                    fontSize = 12.sp,
                    color = Color(0xFF58606B),
                    style = LocalTextStyle.current.copy(lineHeight = TextUnit.Unspecified),
                    modifier = Modifier.padding(horizontal = 1.dp, vertical = 1.dp)
                )
            }

        }


    }
}


@Composable
fun MediaInfoBar(
    hasVideo: Boolean,
    imageCount: String,
    isBoutique: Boolean,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.Start
    ) {
        Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))
        MediaInfoItem(icon = painterResource(R.drawable.ic_image), count = imageCount)
        if (hasVideo) {
            Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))
            MediaInfoItem(icon = painterResource(R.drawable.video_ic), count = "1")
        }
        if (isBoutique) {
            Spacer(modifier = Modifier.width(MaterialTheme.dimens.medium))
            BoutiqueItem()
        }
    }
}

@Composable
fun MediaInfoItem(icon: Painter, count: String) {
    Row(
        modifier = Modifier
            .clip(RoundedCornerShape(MaterialTheme.dimens.small))
            .background(Color.Black.copy(alpha = 0.8f)) // Adjust background color and opacity
            .padding(
                horizontal = MaterialTheme.dimens.medium,
                vertical = MaterialTheme.dimens.small
            ),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            painter = icon,
            contentDescription = null,
            tint = Color.White,
            modifier = Modifier.size(18.dp)
        )
        Spacer(modifier = Modifier.width(MaterialTheme.dimens.small))
        Text(
            text = count,
            color = Color.White,
            fontSize = 14.sp,
            fontWeight = FontWeight.SemiBold
        )
    }
}

@Composable
fun BoutiqueItem() {
    Row(
        modifier = Modifier
            .clip(RoundedCornerShape(MaterialTheme.dimens.small))
            .background(Color.Black.copy(alpha = 0.8f))
            .padding(
                horizontal = MaterialTheme.dimens.medium,
                vertical = MaterialTheme.dimens.small
            ),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            painter = painterResource(R.drawable.shop_icon),
            contentDescription = null,
            tint = Color.White,
            modifier = Modifier.size(18.dp)
        )
        Spacer(modifier = Modifier.width(MaterialTheme.dimens.small))
        Text(
            text = stringResource(R.string.store_lable),
            color = Color.White,
            fontSize = 14.sp,
            fontWeight = FontWeight.SemiBold
        )
    }
}