package se.scmv.morocco.account.presentation.statistics

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.netguru.multiplatform.charts.ChartDisplayAnimation
import com.netguru.multiplatform.charts.grid.axisscale.y.YAxisScaleDynamic
import com.netguru.multiplatform.charts.line.LineChart
import com.netguru.multiplatform.charts.line.LineChartColors
import com.netguru.multiplatform.charts.line.LineChartData
import com.netguru.multiplatform.charts.line.LineChartPoint
import com.netguru.multiplatform.charts.line.LineChartSeries
import com.netguru.multiplatform.charts.line.TooltipConfig
import com.netguru.multiplatform.charts.line.XAxisConfig
import com.netguru.multiplatform.charts.line.YAxisConfig
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.toImmutableList
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.atStartOfDayIn
import se.scmv.morocco.account.R
import se.scmv.morocco.analytics.TrackScreenViewEvent
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.Param
import se.scmv.morocco.designsystem.components.ScreenErrorState
import se.scmv.morocco.designsystem.theme.AvitoTheme
import se.scmv.morocco.designsystem.theme.White
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.AccountStatisticsMetric
import se.scmv.morocco.domain.models.AccountStatisticsMetricName
import se.scmv.morocco.domain.models.AccountStatisticsPoint
import kotlin.math.roundToInt
import kotlin.random.Random

@Composable
fun AccountStatisticsRoute(
    modifier: Modifier = Modifier,
    viewModel: AccountStatisticsViewModel = hiltViewModel()
) {
    val state = viewModel.viewState.collectAsStateWithLifecycle().value
    AccountStatisticsScreen(
        modifier = modifier,
        state = state,
        onRefresh = viewModel::onRefresh
    )
    TrackScreenViewEvent(
        screenName = AnalyticsEvent.ScreensNames.STATISTICS,
        properties = setOf(
            Param(AnalyticsEvent.ParamKeys.ACCOUNT_TYPE, AnalyticsEvent.ParamValues.ACCOUNT_TYPE_SHOP)
        )
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AccountStatisticsScreen(
    modifier: Modifier = Modifier,
    state: AccountStatisticsViewState,
    onRefresh: () -> Unit
) {
    val context = LocalContext.current
    when {
        state.metrics.isEmpty() && state.errorMessage != null -> ScreenErrorState(
            modifier = modifier,
            title = stringResource(R.string.common_oups),
            description = state.errorMessage.getValue(context),
            actionText = stringResource(R.string.common_refresh),
            onActionClicked = onRefresh
        )

        else -> PullToRefreshBox(
            modifier = modifier
                .fillMaxSize()
                .padding(horizontal = MaterialTheme.dimens.default)
                .padding(vertical = MaterialTheme.dimens.big),
            isRefreshing = state.loading,
            onRefresh = onRefresh
        ) {
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(bottom = MaterialTheme.dimens.extraBig)
            ) {
                item {
                    AccountStatisticsTitle(
                        modifier = Modifier.padding(bottom = MaterialTheme.dimens.medium),
                        icon = R.drawable.ic_stats_report,
                        title = R.string.statistics_screen_summary_title
                    )
                }
                item {
                    AccountStatisticsSummary(metrics = state.metrics)
                }
                item {
                    AccountStatisticsTitle(
                        modifier = Modifier.padding(
                            top = MaterialTheme.dimens.big,
                            bottom = MaterialTheme.dimens.medium
                        ),
                        icon = R.drawable.ic_stats_calendar,
                        title = R.string.statistics_screen_metrics_title
                    )
                }
                items(
                    items = state.metrics,
                    key = { item -> item.name }
                ) { item ->
                    with(item) {
                        AccountStatisticsMetricCard(
                            title = name.localizedName(),
                            total = total,
                            points = data.toImmutableList(),
                            color = name.color(),
                            icon = name.icon()
                        )
                    }
                    Spacer(modifier = Modifier.height(MaterialTheme.dimens.large))
                }
            }
        }
    }
}

@Preview
@Composable
private fun AccountStatisticsScreenErrorPreview() {
    AvitoTheme {
        Surface {
            AccountStatisticsScreen(
                state = AccountStatisticsViewState(
                    errorMessage = UiText.FromRes(R.string.common_network_error_verify_and_try_later)
                ),
                onRefresh = {}
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun AccountStatisticsMetricPreview() {
    AvitoTheme {
        Surface {
            val metrics = List(8) { metric ->
                AccountStatisticsMetric(
                    name = AccountStatisticsMetricName.entries[metric],
                    data = (1 until 30).map {
                        val dayString = if (it < 10) "0$it" else "$it"
                        AccountStatisticsPoint(
                            date = LocalDate.parse("2024-12-$dayString"),
                            value = Random.nextInt(0, 30)
                        )
                    },
                    total = metric * 100
                )
            }.toImmutableList()
            AccountStatisticsScreen(
                modifier = Modifier,
                state = AccountStatisticsViewState(
                    metrics = metrics
                ),
                onRefresh = {}
            )
        }
    }
}

@Composable
fun AccountStatisticsTitle(
    modifier: Modifier = Modifier,
    @DrawableRes icon: Int,
    @StringRes title: Int
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .background(MaterialTheme.colorScheme.surfaceContainerHighest, CircleShape)
                .size(MaterialTheme.dimens.extraBig),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                painter = painterResource(icon),
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onBackground
            )
        }
        Text(
            text = stringResource(title),
            style = MaterialTheme.typography.titleLarge
        )
    }
}

@Composable
private fun AccountStatisticsSummary(
    modifier: Modifier = Modifier,
    metrics: ImmutableList<AccountStatisticsMetric>
) {
    Card {
        Row(
            modifier = modifier
                .padding(MaterialTheme.dimens.default)
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
        ) {
            metrics
                .filter { it.name == AccountStatisticsMetricName.VIEW || it.name == AccountStatisticsMetricName.PHONEVIEW }
                .forEach { metric ->
                    Card(
                        modifier = Modifier.weight(1f),
                        elevation = CardDefaults.elevatedCardElevation(defaultElevation = MaterialTheme.dimens.tiny)
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(
                                    brush = metric.name
                                        .color()
                                        .toLinearGradient()
                                )
                                .padding(MaterialTheme.dimens.large),
                            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium)
                        ) {
                            Row(
                                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.large),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Image(
                                    painter = painterResource(metric.name.icon()),
                                    contentDescription = null
                                )
                                Text(
                                    text = stringResource(metric.name.localizedName()),
                                    style = MaterialTheme.typography.labelLarge,
                                    color = White
                                )
                            }
                            Text(
                                text = metric.total.toString(),
                                style = MaterialTheme.typography.titleMedium,
                                color = White
                            )
                        }
                    }
                }
        }
    }
}

@Composable
private fun AccountStatisticsMetricCard(
    modifier: Modifier = Modifier,
    @StringRes title: Int,
    total: Int,
    points: ImmutableList<AccountStatisticsPoint>,
    color: Color,
    @DrawableRes icon: Int,
) {
    var expanded by rememberSaveable { mutableStateOf(false) }
    Card(
        modifier = modifier.fillMaxWidth(),
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(MaterialTheme.dimens.medium),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimens.medium),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(painter = painterResource(icon), contentDescription = null)
                Text(text = "$total ${stringResource(title)}")
            }
            IconButton(
                onClick = {
                    expanded = expanded.not()
                },
            ) {
                Icon(
                    imageVector = if (expanded) {
                        Icons.Filled.KeyboardArrowUp
                    } else Icons.Filled.KeyboardArrowDown,
                    contentDescription = null
                )
            }
        }
        AnimatedVisibility(visible = expanded) {
            val data = LineChartData(
                series = listOf(
                    LineChartSeries(
                        dataName = "",
                        lineColor = color,
                        fillColor = color,
                        listOfPoints = points.map { point ->
                            LineChartPoint(
                                x = point.date
                                    .atStartOfDayIn(TimeZone.currentSystemDefault())
                                    .toEpochMilliseconds(),
                                y = point.value.toFloat(),
                            )
                        }
                    )
                ),
                dataUnit = null
            )
            LineChart(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(MaterialTheme.dimens.medium)
                    .aspectRatio(1f),
                data = data,
                xAxisConfig = XAxisConfig(
                    markerLayout = { date ->
                        Text(
                            text = (date as Long).toXAxisLabel(),
                            fontSize = MaterialTheme.typography.labelMedium.fontSize,
                            textAlign = TextAlign.Center
                        )
                    }
                ),
                yAxisConfig = YAxisConfig(
                    markerLayout = {
                        Text(
                            text = "$it",
                            fontSize = MaterialTheme.typography.labelMedium.fontSize,
                            textAlign = TextAlign.Center
                        )
                    },
                    yAxisTitleData = null,
                    scale = YAxisScaleDynamic(chartData = data, forceShowingValueZeroLine = true)
                ),
                legendConfig = null,
                colors = LineChartColors(
                    grid = color.copy(CHART_GRID_COLOR_ALPHA),
                    surface = Color.Unspecified,
                    overlayLine = color.copy(CHART_OVERLAY_LINE_COLOR_ALPHA),
                    overlaySurface = color.copy(CHART_OVERLAY_SURFACE_COLOR_ALPHA),
                ),
                tooltipConfig = TooltipConfig(
                    headerLabel = { dateInMilliseconds, _ ->
                        Text(
                            text = (dateInMilliseconds as Long).toTooltipHeaderLabel(),
                            style = MaterialTheme.typography.labelMedium,
                            textAlign = TextAlign.Center
                        )
                    },
                    dataEntryLabel = { _, _, _, value ->
                        Text(
                            text = (value as Float).roundToInt().toString(),
                            style = MaterialTheme.typography.labelMedium,
                            textAlign = TextAlign.Center
                        )
                    },
                    showInterpolatedValues = false,
                    showEnlargedPointOnLine = true,
                    width = TOOLTIP_WIDTH.dp,
                ),
                shouldInterpolateOverNullValues = false,
                shouldDrawValueDots = true,
                displayAnimation = ChartDisplayAnimation.Sequenced()
            )
        }
    }
}