package se.scmv.morocco.account.presentation.messaging.ui.conversation

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import se.scmv.morocco.account.R
import se.scmv.morocco.domain.ChatRepository
import se.scmv.morocco.domain.ChatResponse
import se.scmv.morocco.domain.Conversation
import se.scmv.morocco.domain.RealtimeChatEvent
import se.scmv.morocco.ui.renderSuccess
import se.scmv.morocco.ui.renderFailure
import se.scmv.morocco.designsystem.utils.UiText
import java.util.*
import javax.inject.Inject

data class ConversationListState(
    val conversations: List<Conversation> = emptyList(),
    val isLoading: Boolean = false,
    val isLoadingMore: Boolean = false,
    val isRefreshing: Boolean = false,
    val unreadCount: Int = 0,
    val error: String? = null,
    val errorCode: String? = null,
    val hasMoreConversations: Boolean = true

)

sealed interface ConversationListEvent {
    data class NavigateToChat(val conversation: Conversation) : ConversationListEvent
}

@HiltViewModel
class ConversationListViewModel @Inject constructor(
    private val chatRepository: ChatRepository
) : ViewModel() {
    private val _state = MutableStateFlow(ConversationListState())
    val state: StateFlow<ConversationListState> = _state.asStateFlow()

    private val _events = MutableSharedFlow<ConversationListEvent>()
    val events = _events.asSharedFlow()

    private val allConversations: MutableMap<String, Int> = mutableMapOf()
    private var oldestMessageTime: Date? = null
    private var newestMessageTime: Date? = null
    private var isSubscribed = false

    init {
        loadConversations()
        subscribeToChat()
    }

    private fun subscribeToChat() {
        if (isSubscribed) {
            return
        }
        
        viewModelScope.launch {
            try {
                chatRepository.subscribeToChat()
                    .retryWhen { cause, attempt ->
                        val isNetworkError = cause is java.net.SocketTimeoutException || 
                            cause is java.net.UnknownHostException ||
                            cause is java.io.IOException ||
                            cause.javaClass.name.contains("ApolloNetworkException") ||
                            cause.message?.contains("Network error", ignoreCase = true) == true ||
                            cause.message?.contains("connection abort", ignoreCase = true) == true ||
                            cause.message?.contains("ssl", ignoreCase = true) == true ||
                            cause.message?.contains("i/o error", ignoreCase = true) == true
                        
                        if (isNetworkError) {
                            val delayMillis = minOf(1000L * (1L shl attempt.toInt()), 30000L)
                            kotlinx.coroutines.delay(delayMillis)
                            true
                        } else {
                            false
                        }
                    }
                    .catch { e ->
                        val msg = e.message ?: ""
                        
                        val isNetworkError = e.javaClass.name.contains("ApolloNetworkException") ||
                            msg.contains("Network error", ignoreCase = true) ||
                            msg.contains("connection abort", ignoreCase = true) ||
                            msg.contains("ssl", ignoreCase = true) ||
                            msg.contains("i/o error", ignoreCase = true) ||
                            msg.contains("null data event", ignoreCase = true)
                        
                        if (msg.isNotBlank() && !isNetworkError) {
                            _state.value = _state.value.copy(
                                error = msg,
                                errorCode = "SUBSCRIPTION_ERROR"
                            )
                            renderFailure(UiText.Text(msg))
                        }
                        
                        isSubscribed = false
                        kotlinx.coroutines.delay(5000L)
                        subscribeToChat()
                    }
                    .collect { event ->
                        if (event == null || event.message == null) {
                            return@collect
                        }
                        handleChatEvent(event)
                    }
                isSubscribed = true
            } catch (e: Exception) {
                val msg = e.message ?: ""
                
                val isNetworkError = e.javaClass.name.contains("ApolloNetworkException") ||
                    msg.contains("Network error", ignoreCase = true) ||
                    msg.contains("connection abort", ignoreCase = true) ||
                    msg.contains("ssl", ignoreCase = true) ||
                    msg.contains("i/o error", ignoreCase = true) ||
                    msg.contains("null data event", ignoreCase = true)
                
                if (msg.isNotBlank() && !isNetworkError) {
                    _state.value = _state.value.copy(
                        error = msg,
                        errorCode = "SUBSCRIPTION_ERROR"
                    )
                    renderFailure(UiText.Text(msg))
                }
                
                isSubscribed = false
                kotlinx.coroutines.delay(5000L)
                subscribeToChat()
            }
        }
    }

    private fun handleChatEvent(event: RealtimeChatEvent?) {
        if (event == null || event.message == null) return
        val message = event.message
        val conversationId = message.conversationId ?: return
        val currentConversations = _state.value.conversations.toMutableList()
        val conversationIndex = currentConversations.indexOfFirst { it.id == conversationId }

        if (conversationIndex != -1) {
            // Existing conversation - update lastMessage, increment unreadCount, and move to top
            val conversation = currentConversations.removeAt(conversationIndex)
            val updatedConversation = conversation.copy(
                lastMessage = message,
                unreadCount = conversation.unreadCount + 1
            )
            allConversations[conversationId] = updatedConversation.unreadCount
            currentConversations.add(0, updatedConversation)
            _state.value = _state.value.copy(
                conversations = currentConversations,
                unreadCount = allConversations.values.sum()
            )
        } else {
            // New conversation - fetch and add to top (existing logic)
            viewModelScope.launch {
                try {
                    chatRepository.getChatConversation(conversationId, 1)
                        .collect { result ->
                            result.fold(
                                onSuccess = { conversation ->

                                    val newConversation = conversation.copy(
                                        lastMessage = message,
                                        unreadCount = conversation.unreadCount + 1
                                    )
                                    allConversations[conversationId] = newConversation.unreadCount
                                    val updatedList = listOf(newConversation) + _state.value.conversations
                                    _state.value = _state.value.copy(
                                        conversations = updatedList,
                                        unreadCount = allConversations.values.sum()
                                    )
                                },
                                onFailure = { error ->
                                    // Silently handle conversation fetch failures
                                }
                            )
                        }
                } catch (e: Exception) {
                    // Silently handle conversation fetch errors
                }
            }
            return
        }

        _state.value = _state.value.copy(
            conversations = currentConversations,
            unreadCount = allConversations.values.sum()
        )
    }

    private fun loadConversations(
        pageSize: Int = 20,
        isRefreshing: Boolean = false,
        isLoadingMore: Boolean = false
    ) {
        viewModelScope.launch {
            try {
                _state.value = _state.value.copy(
                    isLoading = !isRefreshing && !isLoadingMore,
                    isRefreshing = isRefreshing,
                    isLoadingMore = isLoadingMore,
                    error = null
                )

                chatRepository.getConversations(
                    pageSize = pageSize,
                    beforeTime = if (isLoadingMore) oldestMessageTime else null,
                    afterTime = if (isRefreshing) newestMessageTime else null
                ).collect { result ->
                    result.fold(
                        onSuccess = { response ->
                            handleConversationsSuccess(response, isRefreshing)
                        },
                        onFailure = { error ->
                            handleError(error)
                        }
                    )
                }
            } catch (e: Exception) {
                val isNetworkError = e.javaClass.name.contains("ApolloNetworkException") ||
                    e.message?.contains("Network error", ignoreCase = true) == true ||
                    e.message?.contains("connection abort", ignoreCase = true) == true ||
                    e.message?.contains("ssl", ignoreCase = true) == true ||
                    e.message?.contains("i/o error", ignoreCase = true) == true
                
                if (!isNetworkError) {
                    _state.value = _state.value.copy(
                        error = e.message,
                        errorCode = "LOAD_ERROR",
                        isLoading = false,
                        isRefreshing = false,
                        isLoadingMore = false
                    )
                    renderFailure(UiText.Text(e.message ?: "Unknown error occurred"))
                } else {
                    _state.value = _state.value.copy(
                        isLoading = false,
                        isRefreshing = false,
                        isLoadingMore = false
                    )
                }
            }
        }
    }

    private fun handleConversationsSuccess(response: ChatResponse, isRefreshing: Boolean) {
        val newConversations = response.conversations
        if (newConversations.isNotEmpty()) {
            newConversations.firstOrNull()?.lastMessage?.time?.let { time ->
                newestMessageTime = time
            }
            newConversations.lastOrNull()?.lastMessage?.time?.let { time ->
                oldestMessageTime = time
            }
        }

        // Update allConversations map with new unread counts
        newConversations.forEach { conversation ->
            allConversations[conversation.id] = conversation.unreadCount
        }

        _state.value = _state.value.copy(
            conversations = if (isRefreshing) {
                newConversations
            } else {
                _state.value.conversations + newConversations
            },
            unreadCount = allConversations.values.sum(),
            isLoading = false,
            isRefreshing = false,
            isLoadingMore = false,
            error = null,
            hasMoreConversations = newConversations.isNotEmpty()
        )
    }

    fun loadMore() {
        if (!_state.value.isLoadingMore && oldestMessageTime != null && _state.value.hasMoreConversations) {
            loadConversations(isLoadingMore = true)
        }
    }

    fun refresh() {
        loadConversations(isRefreshing = true)
    }

    fun retry() {
        _state.value = _state.value.copy(error = null)
        loadConversations()
    }

    fun onConversationClick(conversation: Conversation) {
        viewModelScope.launch {
            try {
                // Try to mark as read, but don't wait for it to succeed
                chatRepository.markConversationAsRead(conversation.id)
                    .collect { result ->
                        result.fold(
                            onSuccess = { marked ->
                                if (marked) {
                                    // Update the conversation in the list
                                    val updatedConversations = _state.value.conversations.map { conv ->
                                        if (conv.id == conversation.id) {
                                            conv.copy(unreadCount = 0)
                                        } else {
                                            conv
                                        }
                                    }

                                    // Update the allConversations map
                                    allConversations[conversation.id] = 0

                                    // Calculate new total unread count
                                    val totalUnreadCount = allConversations.values.sum()

                                    // Update state with new values
                                    _state.value = _state.value.copy(
                                        conversations = updatedConversations,
                                        unreadCount = totalUnreadCount
                                    )
                                }
                            },
                            onFailure = { error ->
                                // Silently handle mark as read failures
                            }
                        )
                    }
            } catch (e: Exception) {
                // Silently handle any exceptions
            }
            
            // Always navigate to chat regardless of mark as read success/failure
            _events.emit(ConversationListEvent.NavigateToChat(conversation))
        }
    }

    fun onBlockUser(conversation: Conversation) {
        viewModelScope.launch {
            chatRepository.blockConversation(conversation.id)
                .collect { result ->
                    result.fold(
                        onSuccess = { blocked ->
                            if (blocked) {
                                updateConversationBlockStatus(conversation.id, true)
                                renderSuccess(UiText.FromRes(R.string.has_been_blocked))
                            }
                        },
                        onFailure = { error ->
                            handleError(error)
                        }
                    )
                }
        }
    }

    fun onUnblockUser(conversation: Conversation) {
        viewModelScope.launch {
            chatRepository.unblockConversation(conversation.id)
                .collect { result ->
                    result.fold(
                        onSuccess = { unblocked ->
                            if (unblocked) {
                                updateConversationBlockStatus(conversation.id, false)
                                renderSuccess(UiText.FromRes(R.string.has_been_unblocked))
                            }
                        },
                        onFailure = { error ->
                            handleError(error)
                        }
                    )
                }
        }
    }

    fun onDeleteChat(conversation: Conversation) {
        viewModelScope.launch {
            chatRepository.clearConversation(conversation.id)
                .collect { result ->
                    result.fold(
                        onSuccess = { cleared ->
                            if (cleared) {
                                removeConversation(conversation.id)
                                renderSuccess(UiText.FromRes(R.string.has_been_deleted))
                            }
                        },
                        onFailure = { error ->
                            handleError(error)
                        }
                    )
                }
        }
    }

    fun onMessageCopied() {
        renderSuccess(UiText.Text("Message copié dans le presse-papiers"))
    }

    fun resetUnreadCount(conversationId: String) {
        viewModelScope.launch {
            try {
                // First mark as read in the repository
                chatRepository.markConversationAsRead(conversationId)
                    .collect { result ->
                        result.fold(
                            onSuccess = { marked ->
                                if (marked) {
                                    // Update the conversation in the list
                                    val updatedConversations = _state.value.conversations.map { conversation ->
                                        if (conversation.id == conversationId) {
                                            conversation.copy(unreadCount = 0)
                                        } else {
                                            conversation
                                        }
                                    }

                                    // Update the allConversations map
                                    allConversations[conversationId] = 0

                                    // Calculate new total unread count
                                    val totalUnreadCount = allConversations.values.sum()

                                    // Update state with new values
                                    _state.value = _state.value.copy(
                                        conversations = updatedConversations,
                                        unreadCount = totalUnreadCount
                                    )
                                }
                            },
                            onFailure = { error ->
                                handleError(error)
                            }
                        )
                    }
            } catch (e: Exception) {
                handleError(e)
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        isSubscribed = false
    }

    private fun updateConversationBlockStatus(conversationId: String, blocked: Boolean) {
        val updatedConversations = _state.value.conversations.map { conversation ->
            if (conversation.id == conversationId) {
                conversation.copy(isBlockedByMe = blocked)
            } else {
                conversation
            }
        }
        _state.value = _state.value.copy(conversations = updatedConversations)
    }

    private fun removeConversation(conversationId: String) {
        val updatedConversations = _state.value.conversations.filter { it.id != conversationId }
        val removedConversation = _state.value.conversations.find { it.id == conversationId }
        _state.value = _state.value.copy(
            conversations = updatedConversations,
            unreadCount = _state.value.unreadCount - (removedConversation?.unreadCount ?: 0)
        )
    }

    private fun handleError(error: Throwable) {
        val isNetworkError = error.javaClass.name.contains("ApolloNetworkException") ||
            error.message?.contains("Network error", ignoreCase = true) == true ||
            error.message?.contains("connection abort", ignoreCase = true) == true ||
            error.message?.contains("ssl", ignoreCase = true) == true ||
            error.message?.contains("i/o error", ignoreCase = true) == true
        
        if (!isNetworkError) {
            _state.value = _state.value.copy(
                isLoading = false,
                isRefreshing = false,
                isLoadingMore = false,
                error = error.message
            )
            viewModelScope.launch {
                renderFailure(UiText.Text(error.message ?: "Unknown error occurred"))
            }
        } else {
            _state.value = _state.value.copy(
                isLoading = false,
                isRefreshing = false,
                isLoadingMore = false
            )
        }
    }
}

