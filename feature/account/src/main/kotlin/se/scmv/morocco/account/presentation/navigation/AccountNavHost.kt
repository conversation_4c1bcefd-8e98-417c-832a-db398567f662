package se.scmv.morocco.account.presentation.navigation

import android.content.Intent
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.window.DialogProperties
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.dialog
import androidx.navigation.navDeepLink
import kotlinx.serialization.Serializable
import se.scmv.morocco.account.presentation.bookmarks.master.AccountBookmarksPages
import se.scmv.morocco.account.presentation.edit_account.EditAccountActivity
import se.scmv.morocco.account.presentation.edit_account.EditAccountRoute
import se.scmv.morocco.account.presentation.master.AccountMasterRoute
import se.scmv.morocco.account.presentation.messaging.ui.chat.ChatScreen
import se.scmv.morocco.account.presentation.myads.AccountAdsActivity
import se.scmv.morocco.account.presentation.myads.AdsRoute
import se.scmv.morocco.account.presentation.orders.AccountOrdersActivity
import se.scmv.morocco.account.presentation.orders.AccountOrdersRoute
import se.scmv.morocco.account.presentation.update_password.ARG_EMAIL
import se.scmv.morocco.account.presentation.update_password.UpdatePasswordRoute
import se.scmv.morocco.authentication.presentation.AuthenticationActivity
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.MyAccountAdStatus
import se.scmv.morocco.domain.models.VasPacksApplication
import se.scmv.morocco.ui.AppDeepLinks
import se.scmv.morocco.ui.AuthProtectedContent
import se.scmv.morocco.ui.composableWithAnimation

enum class AccountScreen(val route: String) {
    MASTER("account_master"),
    EDIT_ACCOUNT("edit_account"),
    UPDATE_PASSWORD("update_password/{$ARG_EMAIL}")
}

@Composable
fun AccountNavHost(
    modifier: Modifier = Modifier,
    account: Account.Connected,
    navHostController: NavHostController,
) {
    val context = LocalContext.current
    NavHost(
        modifier = modifier,
        navController = navHostController,
        startDestination = AccountScreen.MASTER.route
    ) {
        composable(route = AccountScreen.MASTER.route) {
            AccountMasterRoute(
                account = account,
                navigateToAccountEdit = {
                    // TODO replace this by navigating to composable, once all the app is in compose
                    val intent = Intent(context, EditAccountActivity::class.java)
                    context.startActivity(intent)
                },
                navigateToAccountAds = {
                    val intent = Intent(context, AccountAdsActivity::class.java)
                    context.startActivity(intent)
                },
                navigateToAccountOrders = {
                    // TODO replace this by navigating to composable, once all the app is in compose
                    val intent = Intent(context, AccountOrdersActivity::class.java)
                    context.startActivity(intent)
                },
                navigateToUpdatePassword = { email ->
                    val route = AccountScreen.UPDATE_PASSWORD.route
                        .replace("{$ARG_EMAIL}", email)
                    navHostController.navigate(route)
                }
            )
        }
        dialog(
            route = AccountScreen.UPDATE_PASSWORD.route,
            dialogProperties = DialogProperties(
                dismissOnClickOutside = false,
                dismissOnBackPress = false
            )
        ) {
            UpdatePasswordRoute(
                account = account,
                onFinished = { navHostController.popBackStack() },
                navigateToAuthentication = {
                    // TODO replace this by navigating to auth nestedNavHost, once all the app is in compose
                    val intent = Intent(context, AuthenticationActivity::class.java)
                    context.startActivity(intent)
                }
            )
        }
    }
}

// OFFICIAL NAVIGATION

@Serializable
data object AccountMasterRoute

@Serializable
data class AccountBookmarksRoute(val tab: AccountBookmarksPages?= null)

@Serializable
data object AccountEditRoute

@Serializable
data class AccountAdsRoute(val status: MyAccountAdStatus? = null)

@Serializable
data object AccountOrdersRoute

@Serializable
data object AccountStatisticsRoute

@Serializable
data object MessagingRoute

@Serializable
data class ChatRoute(val conversationId: String)

@Serializable
data class UpdatePasswordRoute(val email: String)

fun NavGraphBuilder.accountGraph(
    account: Account,
    navController: NavHostController,
    navigateToAuthentication: () -> Unit,
    navigateToAdInsert: (adId: String?, toImageStep: Boolean) -> Unit,
    navigateToVas: (adId: String, adCategoryKey: String, adType: String, application: VasPacksApplication) -> Unit,
    navigateToAdview: (String) -> Unit
) {
    composableWithAnimation<AccountEditRoute> {
        AuthProtectedContent(
            account = account,
            onLoginClicked = navigateToAuthentication
        ) { connectedAccount ->
            EditAccountRoute(
                account = connectedAccount,
                navigateBack = { navController.navigateUp() },
                navigateToUpdatePassword = {
                    navController.navigate(
                        UpdatePasswordRoute(connectedAccount.connectedContact().email)
                    )
                },
                navigateToAuthentication = navigateToAuthentication,
            )
        }
    }
    composableWithAnimation<UpdatePasswordRoute> {
        AuthProtectedContent(
            account = account,
            onLoginClicked = navigateToAuthentication
        ) { connectedAccount ->
            UpdatePasswordRoute(
                account = connectedAccount,
                onFinished = { navController.navigateUp() },
                navigateToAuthentication = navigateToAuthentication
            )
        }
    }
    composableWithAnimation<AccountAdsRoute>(
        deepLinks = listOf(navDeepLink<AccountAdsRoute>(basePath = AppDeepLinks.ACCOUNT_ADS))
    ) {
        AuthProtectedContent(
            account = account,
            onLoginClicked = navigateToAuthentication
        ) { connectedAccount ->
            AdsRoute(
                navigateBack = { navController.navigateUp() },
                navigateToNewInsert = { adId, _, _, toImageStep ->
                    navigateToAdInsert(adId, toImageStep)
                },
                navigateToVasActivity = navigateToVas,
                navigateToAdView = { adListId, _, _, _, _, _, _, _, _, _, _, _, _, _, _, _ ->
                    navigateToAdview(adListId)
                },
                account = connectedAccount
            )
        }
    }

    composableWithAnimation<AccountOrdersRoute> {
        AuthProtectedContent(
            account = account,
            onLoginClicked = navigateToAuthentication
        ) { _ ->
            AccountOrdersRoute(
                navigateBack = { navController.navigateUp() }
            )
        }
    }
    composable<ChatRoute>(
        deepLinks = listOf(navDeepLink<ChatRoute>(basePath = AppDeepLinks.CHAT))
    ) { backStackEntry ->
        ChatScreen(
            onNavigateUp = { navController.navigateUp() },
            onOpenAd = navigateToAdview
        )
    }
}
