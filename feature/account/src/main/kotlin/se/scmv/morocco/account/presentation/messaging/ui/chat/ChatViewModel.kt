package se.scmv.morocco.account.presentation.messaging.ui.chat

import android.content.Context
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import se.scmv.morocco.account.R
import se.scmv.morocco.domain.Attachment
import se.scmv.morocco.domain.ChatRepository
import se.scmv.morocco.domain.Conversation
import se.scmv.morocco.domain.Message
import se.scmv.morocco.domain.RealtimeChatEvent
import se.scmv.morocco.ui.renderSuccess
import se.scmv.morocco.ui.renderFailure
import se.scmv.morocco.designsystem.utils.UiText
import java.io.File
import java.util.Date
import javax.inject.Inject


data class ChatState(
    val conversation: Conversation? = null,
    val messages: List<Message> = emptyList(),
    val isLoading: Boolean = false,
    val isLoadingMore: Boolean = false,
    val error: String? = null,
    val isSending: Boolean = false,
    val hasMoreMessages: Boolean = true,
    val isBlocking: Boolean = false

)

sealed interface UiChatEvent {
    data class MessageSent(val message: Message) : UiChatEvent
    data class ConversationRead(val conversationId: String) : UiChatEvent
}

@HiltViewModel
class ChatViewModel @Inject constructor(
    private val chatRepository: ChatRepository,
    private val savedStateHandle: SavedStateHandle,
    private val context: Context
) : ViewModel() {

    private var conversationId: String? = null
    private var isSubscribed = false

    private val _state = MutableStateFlow(ChatState())
    val state: StateFlow<ChatState> = _state.asStateFlow()

    private val _events = MutableSharedFlow<UiChatEvent>()
    val events: SharedFlow<UiChatEvent> = _events.asSharedFlow()

    private var oldestMessageTime: Date? = null
    private var newestMessageTime: Date? = null

    init {
        conversationId = savedStateHandle.get<String>("conversationId")
        loadConversation()
        // Subscribe to chat events immediately
        subscribeToChat()
    }

    fun setConversationId(id: String) {
        conversationId = id
        savedStateHandle["conversationId"] = id
        // Reset time markers when switching to a new chat
        oldestMessageTime = null
        newestMessageTime = null
        // Reset state
        _state.value = ChatState()
        loadConversation()
        // Mark conversation as read immediately when opened
        markAsRead()
    }

    private fun subscribeToChat() {
        if (isSubscribed) {
            android.util.Log.d("ChatSubscription", "Already subscribed, skipping")
            return
        }

        viewModelScope.launch {
            try {
                android.util.Log.d("ChatSubscription", "Starting subscription")
                chatRepository.subscribeToChat()
                    .catch { e ->
                        android.util.Log.e(
                            "ChatSubscription",
                            "Subscription error in ChatViewModel: ${e.message}",
                            e
                        )
                        renderFailure(UiText.Text(e.message ?: "Error subscribing to chat"))
                        isSubscribed = false
                        // Retry subscription after a delay
                        kotlinx.coroutines.delay(5000) // 5 seconds delay
                        subscribeToChat()
                    }
                    .collect { event ->
                        android.util.Log.d(
                            "ChatSubscription",
                            "Received event in ChatViewModel: messageId=${event.message.id}"
                        )
                        handleChatEvent(event)
                    }
                android.util.Log.d("ChatSubscription", "Subscription started successfully")
                isSubscribed = true
            } catch (e: Exception) {
                android.util.Log.e("ChatSubscription", "Exception in subscription: ${e.message}", e)
                renderFailure(UiText.Text(e.message ?: "Error subscribing to chat"))
                isSubscribed = false
                // Retry subscription after a delay
                kotlinx.coroutines.delay(5000) // 5 seconds delay
                subscribeToChat()
            }
        }
    }

    private fun handleChatEvent(event: RealtimeChatEvent) {
        val message = event.message
        if (message.conversationId == conversationId) {
            android.util.Log.d(
                "ChatSubscription",
                "Processing message for current conversation: ${message.id}"
            )
            val currentMessages = _state.value.messages.toMutableList()
            currentMessages.add(0, message)
            _state.value = _state.value.copy(messages = currentMessages)

            if (!message.isMine) {
                markAsRead()
            }
        } else {
            android.util.Log.d(
                "ChatSubscription",
                "Message for different conversation: ${message.conversationId}"
            )
        }
    }

    private fun loadConversation() {
        conversationId?.let { id ->
            viewModelScope.launch {
                _state.value = _state.value.copy(
                    isLoading = true,
                    error = null
                )
                chatRepository.getChatConversation(id, 20).collect { result ->
                    result.fold(
                        onSuccess = { conversation ->
                            _state.value = _state.value.copy(
                                conversation = conversation
                            )
                            loadMessages()
                        },
                        onFailure = { error ->
                            _state.value = _state.value.copy(
                                error = error.message ?: "Failed to load conversation",
                                isLoading = false
                            )
                        }
                    )
                }
            }
        }
    }

    private fun loadMessages() {
        conversationId?.let { id ->
            viewModelScope.launch {
                chatRepository.getMessages(id).collect { result ->
                    result.fold(
                        onSuccess = { messages ->
                            _state.value = _state.value.copy(
                                messages = messages,
                                isLoading = false,
                                hasMoreMessages = messages.size >= 20

                            )
                        },
                        onFailure = { error ->
                            _state.value = _state.value.copy(
                                error = error.message ?: "Failed to load messages",
                                isLoading = false
                            )
                        }
                    )
                }
            }
        }
    }

    companion object {
        private const val MAX_FILE_SIZE_MB = 4
        private const val MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024
    }

    fun sendMessage(text: String, attachment: Attachment? = null) {
        val currentId = conversationId ?: return

        viewModelScope.launch {
            try {
                _state.value = _state.value.copy(isSending = true)

                if (attachment != null) {
                    val file = File(attachment.filePath)
                    if (!file.exists()) {
                        renderFailure(UiText.FromRes(R.string.chat_screen_file_not_found))
                        return@launch
                    }

                    // Check file size before processing
                    if (file.length() > MAX_FILE_SIZE_BYTES) {
                        renderFailure(
                            UiText.FromRes(
                                R.string.chat_screen_file_too_large,
                                listOf(MAX_FILE_SIZE_MB)
                            )
                        )
                        return@launch
                    }
                }

                chatRepository.sendMessage(
                    conversationId = currentId,
                    text = text,
                    attachment = attachment
                ).collect { result ->
                    result.fold(
                        onSuccess = { message ->
                            handleMessageSent(message)
                        },
                        onFailure = { error ->
                            handleError(error)
                        }
                    )
                }
            } catch (e: Exception) {
                handleError(e)
            } finally {
                _state.value = _state.value.copy(isSending = false)
            }
        }
    }

    private fun handleMessageSent(message: Message) {
        val currentMessages = _state.value.messages.toMutableList()
        currentMessages.add(0, message)

        _state.value = _state.value.copy(
            messages = currentMessages,
            isSending = false
        )

        viewModelScope.launch {
            _events.emit(UiChatEvent.MessageSent(message))
        }
    }

    private fun markAsRead() {
        val currentId = conversationId ?: return

        viewModelScope.launch {
            try {
                chatRepository.markConversationAsRead(currentId)
                    .collect { result ->
                        result.fold(
                            onSuccess = { marked ->
                                if (marked) {
                                    // Update local state
                                    _state.value = _state.value.copy(
                                        conversation = _state.value.conversation?.copy(unreadCount = 0)
                                    )
                                    // Notify the conversation list to update its unread count
                                    _events.emit(UiChatEvent.ConversationRead(currentId))
                                }
                            },
                            onFailure = { error ->
                                handleError(error)
                            }
                        )
                    }
            } catch (e: Exception) {
                handleError(e)
            }
        }
    }

    fun blockUser() {
        conversationId?.let { id ->
            viewModelScope.launch {
                _state.value = _state.value.copy(isBlocking = true)
                chatRepository.blockConversation(id).collect { result ->
                    result.fold(
                        onSuccess = { blocked ->
                            if (blocked) {
                                _state.value = _state.value.copy(
                                    conversation = _state.value.conversation?.copy(isBlockedByMe = true)
                                )
                                renderSuccess(UiText.FromRes(R.string.has_been_blocked))
                            }
                        },
                        onFailure = { error ->
                            renderFailure(UiText.Text(error.message ?: "Failed to block user"))
                        }
                    )
                }
                _state.value = _state.value.copy(isBlocking = false)
            }
        }
    }

    fun unblockUser() {
        conversationId?.let { id ->
            viewModelScope.launch {
                _state.value = _state.value.copy(isBlocking = true)
                chatRepository.unblockConversation(id).collect { result ->
                    result.fold(
                        onSuccess = { unblocked ->
                            if (unblocked) {
                                _state.value = _state.value.copy(
                                    conversation = _state.value.conversation?.copy(isBlockedByMe = false)
                                )
                                renderSuccess(UiText.FromRes(R.string.has_been_unblocked))
                            }
                        },
                        onFailure = { error ->
                            renderFailure(UiText.Text(error.message ?: "Failed to unblock user"))
                        }
                    )
                }
                _state.value = _state.value.copy(isBlocking = false)
            }
        }
    }

    fun clearChat() {
        conversationId?.let { id ->
            viewModelScope.launch {
                chatRepository.clearConversation(id).collect { result ->
                    result.fold(
                        onSuccess = { cleared ->
                            if (cleared) {
                                _state.value = _state.value.copy(messages = emptyList())
                                renderSuccess(UiText.FromRes(R.string.has_been_deleted))
                            }
                        },
                        onFailure = { error ->
                            renderFailure(UiText.Text(error.message ?: "Failed to clear chat"))
                        }
                    )
                }
            }
        }
    }

    fun onMessageCopied() {
        renderSuccess(UiText.FromRes(R.string.chat_screen_message_copied))
    }

    fun retry() {
        _state.value = _state.value.copy(error = null)
        loadConversation()
    }

    private fun handleError(error: Throwable) {
        _state.value = _state.value.copy(
            error = error.message ?: "Failed to send message",
            isSending = false
        )

        viewModelScope.launch {
            renderFailure(UiText.Text(error.message ?: "Failed to send message"))
        }
    }

    fun loadMoreMessages() {
        if (_state.value.isLoadingMore || !_state.value.hasMoreMessages) return

        viewModelScope.launch {
            _state.value = _state.value.copy(isLoadingMore = true)
            conversationId?.let { id ->
                chatRepository.getChatConversation(id, 20, beforeTime = oldestMessageTime)
                    .collect { result ->
                        result.fold(
                            onSuccess = { conversation ->
                                val newMessages = conversation.messages
                                if (newMessages.isNotEmpty()) {
                                    oldestMessageTime = newMessages.last().time
                                    _state.value = _state.value.copy(
                                        messages = _state.value.messages + newMessages,
                                        isLoadingMore = false,
                                        hasMoreMessages = newMessages.size >= 20
                                    )
                                } else {
                                    _state.value = _state.value.copy(
                                        isLoadingMore = false,
                                        hasMoreMessages = false
                                    )
                                }
                            },
                            onFailure = { error ->
                                _state.value = _state.value.copy(
                                    error = error.message ?: "Failed to load more messages",
                                    isLoadingMore = false
                                )
                            }
                        )
                    }
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        // Subscription will be automatically cancelled when ViewModel is cleared
    }
} 