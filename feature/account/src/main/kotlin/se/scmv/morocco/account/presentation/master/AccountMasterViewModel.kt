package se.scmv.morocco.account.presentation.master

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import se.scmv.morocco.account.R
import se.scmv.morocco.common.utils.TemporaryUtils
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.usecases.LogoutUseCase
import se.scmv.morocco.ui.renderFailure
import se.scmv.morocco.ui.renderSuccess
import javax.inject.Inject

@HiltViewModel
class AccountMasterViewModel @Inject constructor(
    private val logoutUseCase: LogoutUseCase,
    @ApplicationContext private val context: Context
) : ViewModel() {

    private val _oneTimeEvents = MutableSharedFlow<AccountMasterOneTimeEvents>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    // TODO Remove this once the rebirth of is no longer needed.
    lateinit var account: Account.Connected

    fun onLogout() {
        viewModelScope.launch {
            showHideLoading(true)
            val result = logoutUseCase()
            showHideLoading(false)
            when (result) {
                is Resource.Success -> {
                    // TODO Remove this once the rebirth of is no longer needed.
                    if (account is Account.Connected.Shop) {
                        TemporaryUtils.rebirthApp(ctx = context)
                    } else {
                        renderSuccess(message = UiText.FromRes(R.string.common_logout_success_message))
                    }
                }

                is Resource.Failure -> renderFailure(result.error)
            }
        }
    }

    private suspend fun showHideLoading(isLoading: Boolean) {
        _oneTimeEvents.emit(AccountMasterOneTimeEvents(showHideProgress = isLoading))
    }
}
