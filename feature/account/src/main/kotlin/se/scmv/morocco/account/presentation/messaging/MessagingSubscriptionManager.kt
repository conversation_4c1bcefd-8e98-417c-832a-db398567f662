package se.scmv.morocco.account.presentation.messaging

import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import se.scmv.morocco.domain.ChatRepository
import se.scmv.morocco.domain.RealtimeChatEvent
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class MessagingSubscriptionManager @Inject constructor(
    private val chatRepository: ChatRepository
) {
    private val _newMessageEvents = MutableSharedFlow<RealtimeChatEvent>(extraBufferCapacity = 64)
    val newMessageEvents: SharedFlow<RealtimeChatEvent> = _newMessageEvents.asSharedFlow()

    private val _unreadCount = MutableStateFlow(0)
    val unreadCount: StateFlow<Int> = _unreadCount.asStateFlow()

    private var subscriptionJob: Job? = null

    fun startSubscription() {
        if (subscriptionJob != null) return
        subscriptionJob = CoroutineScope(Dispatchers.IO).launch {
            try {
                chatRepository.subscribeToChat()
                    .collect { event ->
                        if (event != null && event.message != null && !event.message.isMine) {
                            Log.d("MessagingSubMgr", "Received new message: ${event.message}")
                            _newMessageEvents.emit(event)
                            _unreadCount.value = _unreadCount.value + 1
                            Log.d(
                                "MessagingSubMgr",
                                "Incremented unread count: ${_unreadCount.value}"
                            )
                        }
                    }
            } catch (e: Exception) {
                Log.e("MessagingSubMgr", "Subscription error: ${e.message}", e)
            }
        }
    }

    fun resetUnreadCount() {
        _unreadCount.value = 0
    }

    fun stopSubscription() {
        subscriptionJob?.cancel()
        subscriptionJob = null
    }

    suspend fun initializeUnreadCount() {
        try {
            // Get the first page of conversations (pageSize = 30)
            chatRepository.getConversations(pageSize = 30)
                .firstOrNull()
                ?.fold(
                    onSuccess = { response ->
                        _unreadCount.value = response.unreadCount
                        Log.d(
                            "MessagingSubMgr",
                            "Initialized unread count from backend: ${response.unreadCount}"
                        )
                    },
                    onFailure = { error ->
                        Log.e(
                            "MessagingSubMgr",
                            "Failed to initialize unread count: ${error.message}"
                        )
                    }
                )
        } catch (e: Exception) {
            Log.e("MessagingSubMgr", "Exception initializing unread count: ${e.message}")
        }
    }
} 