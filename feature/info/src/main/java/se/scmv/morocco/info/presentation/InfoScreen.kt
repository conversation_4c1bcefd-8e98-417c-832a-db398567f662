package se.scmv.morocco.info.presentation

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.consumeWindowInsets
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.outlined.Info
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.ListItem
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalUriHandler
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import se.scmv.morocco.designsystem.R
import se.scmv.morocco.designsystem.components.AvTopAppBar
import se.scmv.morocco.designsystem.theme.AvitoTheme

@Composable
fun InfoScreen(
    modifier: Modifier = Modifier,
    onBackPress: () -> Unit,
    navigateToAbout: () -> Unit,
    navigateToWebView: (title: String, url: String) -> Unit,
) {
    val items = listOf(
        InfoItem(
            text = stringResource(se.scmv.morocco.info.R.string.a_propos),
            icon = Icons.Outlined.Info,
            infoItemType = InfoItemType.NavigateToAbout,
        ),
        InfoItem(
            text = stringResource(se.scmv.morocco.info.R.string.noter_lapplication),
            icon = painterResource(R.drawable.ic_outlined_google_store),
            infoItemType = InfoItemType.OpenPlayStore,
        ),
        InfoItem(
            text = stringResource(se.scmv.morocco.info.R.string.regles_publication),
            icon = painterResource(R.drawable.ic_outlined_grey_rule),
            infoItemType = InfoItemType.UrlNavigation("https://aide.avito.ma/regles-de-publication-dannonce/"),
        ),
        InfoItem(
            text = stringResource(se.scmv.morocco.info.R.string.common_privacy_policy),
            icon = painterResource(R.drawable.ic_outlined_grey_user_shield),
            infoItemType = InfoItemType.UrlNavigation("https://aide.avito.ma/traitement-des-donnees-personnelles/"),
        ),
        InfoItem(
            text = stringResource(se.scmv.morocco.info.R.string.common_term_of_services),
            icon = painterResource(R.drawable.ic_outlined_grey_privacy),
            infoItemType = InfoItemType.UrlNavigation("https://aide.avito.ma/conditions-generales-dutilisation-du-service-avito/"),
        )
    )
    val uriHandler = LocalUriHandler.current
    Scaffold(
        modifier = modifier,
        topBar = {
            AvTopAppBar(
                navigationIcon = Icons.AutoMirrored.Default.ArrowBack,
                onNavigationIconClicked = onBackPress
            )
        }
    ) { paddingValue ->
        Column(modifier = Modifier.consumeWindowInsets(paddingValue)) {
            items.forEachIndexed { index, item ->
                when (item.infoItemType) {
                    is InfoItemType.NavigateToAbout -> {
                        InfoListItem(
                            modifier = Modifier.clickable(onClick = navigateToAbout),
                            text = item.text,
                            icon = item.icon
                        )
                    }

                    is InfoItemType.UrlNavigation -> {
                        InfoListItem(
                            modifier = Modifier.clickable {
                                navigateToWebView(item.text, item.infoItemType.url)
                            },
                            text = item.text,
                            icon = item.icon
                        )
                    }

                    is InfoItemType.OpenPlayStore -> InfoListItem(
                        modifier = Modifier.clickable {
                            uriHandler.openUri("https://play.google.com/store/apps/details?id=se.scmv.morocco")
                        },
                        text = item.text,
                        icon = item.icon
                    )
                }
                if (index != items.lastIndex) HorizontalDivider()
            }
        }
    }
}

@Composable
fun InfoListItem(
    modifier: Modifier = Modifier,
    text: String,
    icon: Any
) {
    ListItem(
        modifier = modifier,
        leadingContent = {
            when (icon) {
                is ImageVector -> {
                    Icon(
                        imageVector = icon,
                        contentDescription = text
                    )
                }

                is Painter -> Icon(
                    painter = icon,
                    contentDescription = text
                )
            }
        },
        headlineContent = {
            Text(
                text = text,
                fontWeight = MaterialTheme.typography.titleMedium.fontWeight,
                fontFamily = MaterialTheme.typography.titleMedium.fontFamily
            )
        }
    )
}

data class InfoItem(
    val text: String,
    val icon: Any,
    val infoItemType: InfoItemType,
)

sealed interface InfoItemType {
    data class UrlNavigation(val url: String) : InfoItemType
    data object OpenPlayStore : InfoItemType
    data object NavigateToAbout : InfoItemType
}

@Preview
@Composable
private fun InfoScreenPreview() {
    AvitoTheme {
        InfoScreen(modifier = Modifier, { }, {}, { _, _ -> })
    }
}