package se.zrcoding.convention

import com.android.build.api.dsl.CommonExtension
import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies

/**
 * Configure Compose-specific options
 */
internal fun Project.configureAndroidCompose(
    commonExtension: CommonExtension<*, *, *, *, *, *>,
) {

    commonExtension.apply {
        buildFeatures {
            compose = true
        }

        dependencies {
            val bom = versionCatalog().findLibrary("androidx.compose.bom").get()
            add(configurationName = "implementation", dependencyNotation = platform(bom))
            add(
                configurationName = "implementation",
                dependencyNotation = versionCatalog().findLibrary("androidx.compose.ui").get()
            )
            add(
                configurationName = "implementation",
                dependencyNotation = versionCatalog().findLibrary("androidx.compose.runtime").get()
            )
            add(
                configurationName = "implementation",
                dependencyNotation = versionCatalog().findLibrary("androidx.compose.ui.tooling.preview").get()
            )
            add(
                configurationName = "implementation",
                dependencyNotation = versionCatalog().findLibrary("androidx.compose.material3").get()
            )
            add(
                configurationName = "debugImplementation",
                dependencyNotation = versionCatalog().findLibrary("androidx.compose.ui.tooling").get()
            )
            add(
                configurationName = "implementation",
                dependencyNotation = versionCatalog().findLibrary("kotlinx.collections.immutable").get()
            )
            add(
                configurationName = "implementation",
                dependencyNotation = versionCatalog().findLibrary("coil.compose").get()
            )
            add(
                configurationName = "implementation",
                dependencyNotation = versionCatalog().findLibrary("coil.svg").get()
            )
            add(
                configurationName = "implementation",
                dependencyNotation = versionCatalog().findLibrary("coil.video").get()
            )
            add(
                configurationName = "implementation",
                dependencyNotation = versionCatalog().findLibrary("coil.gif").get()
            )
            add(
                configurationName = "implementation",
                dependencyNotation = versionCatalog().findLibrary("constraintlayout.compose").get()
            )
        }
    }
}